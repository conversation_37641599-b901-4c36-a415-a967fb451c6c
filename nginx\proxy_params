# Nginx 代理参数配置文件
# 用于统一管理代理设置

proxy_set_header Host $host;
proxy_set_header X-Real-IP $remote_addr;
proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
proxy_set_header X-Forwarded-Proto $scheme;
proxy_set_header X-Forwarded-Host $host;
proxy_set_header X-Forwarded-Port $server_port;

# 超时配置
proxy_connect_timeout 30s;
proxy_send_timeout 60s;
proxy_read_timeout 60s;

# 缓冲配置
proxy_buffering on;
proxy_buffer_size 128k;
proxy_buffers 4 256k;
proxy_busy_buffers_size 256k;

# 其他设置
proxy_redirect off;
proxy_http_version 1.1;
proxy_set_header Connection "";

# 错误处理
proxy_next_upstream error timeout invalid_header http_500 http_502 http_503 http_504;