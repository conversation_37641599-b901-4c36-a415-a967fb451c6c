import React, { useState } from 'react';
import '../../styles/responsive.css';

interface TouchableCardProps {
  title: string;
  subtitle?: string;
  image?: string;
  description?: string;
  actions?: React.ReactNode;
  onClick?: () => void;
  children?: React.ReactNode;
  className?: string;
  badge?: {
    text: string;
    color?: string;
  };
}

const TouchableCard: React.FC<TouchableCardProps> = ({
  title,
  subtitle,
  image,
  description,
  actions,
  onClick,
  children,
  className = '',
  badge
}) => {
  const [touchStart, setTouchStart] = useState<{ x: number; y: number } | null>(null);
  const [isSwiping, setIsSwiping] = useState(false);

  const handleTouchStart = (e: React.TouchEvent) => {
    setTouchStart({
      x: e.touches[0].clientX,
      y: e.touches[0].clientY
    });
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    if (!touchStart) return;

    const deltaX = Math.abs(e.touches[0].clientX - touchStart.x);
    const deltaY = Math.abs(e.touches[0].clientY - touchStart.y);

    // 如果水平滑动大于垂直滑动，认为是滑动操作
    if (deltaX > deltaY && deltaX > 10) {
      setIsSwiping(true);
    }
  };

  const handleTouchEnd = () => {
    if (!isSwiping && onClick) {
      onClick();
    }
    setTouchStart(null);
    setIsSwiping(false);
  };

  return (
    <div 
      className={`touchable-card ${className}`}
      onTouchStart={handleTouchStart}
      onTouchMove={handleTouchMove}
      onTouchEnd={handleTouchEnd}
      onClick={() => !isSwiping && onClick?.()}
      style={{
        backgroundColor: '#fff',
        border: '1px solid #e0e0e0',
        borderRadius: '12px',
        overflow: 'hidden',
        cursor: onClick ? 'pointer' : 'default',
        transition: 'all 0.3s ease',
        boxShadow: '0 2px 4px rgba(0,0,0,0.08)',
        position: 'relative'
      }}
      onMouseEnter={(e) => {
        if (onClick) {
          e.currentTarget.style.transform = 'translateY(-2px)';
          e.currentTarget.style.boxShadow = '0 4px 12px rgba(0,0,0,0.15)';
        }
      }}
      onMouseLeave={(e) => {
        e.currentTarget.style.transform = 'translateY(0)';
        e.currentTarget.style.boxShadow = '0 2px 4px rgba(0,0,0,0.08)';
      }}
    >
      {/* 徽章 */}
      {badge && (
        <div style={{
          position: 'absolute',
          top: '12px',
          right: '12px',
          backgroundColor: badge.color || '#007bff',
          color: '#fff',
          padding: '4px 12px',
          borderRadius: '20px',
          fontSize: '12px',
          fontWeight: '500',
          zIndex: 1
        }}>
          {badge.text}
        </div>
      )}

      {/* 图片 */}
      {image && (
        <div style={{
          width: '100%',
          height: '200px',
          overflow: 'hidden',
          backgroundColor: '#f5f5f5'
        }}>
          <img 
            src={image} 
            alt={title}
            style={{
              width: '100%',
              height: '100%',
              objectFit: 'cover',
              transition: 'transform 0.3s ease'
            }}
            onMouseEnter={(e) => {
              if (onClick) {
                e.currentTarget.style.transform = 'scale(1.05)';
              }
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.transform = 'scale(1)';
            }}
            onError={(e) => {
              e.currentTarget.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjMwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iNDAwIiBoZWlnaHQ9IjMwMCIgZmlsbD0iI2Y1ZjVmNSIvPjx0ZXh0IHg9IjUwJSIgeT0iNTAlIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTYiIGZpbGw9IiNhYWEiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGR5PSIuM2VtIj5JbWFnZSBOb3QgQXZhaWxhYmxlPC90ZXh0Pjwvc3ZnPg==';
            }}
          />
        </div>
      )}

      {/* 内容 */}
      <div style={{ padding: '16px' }}>
        {/* 标题区域 */}
        <div style={{ marginBottom: '12px' }}>
          <h3 className="text-truncate" style={{
            fontSize: '18px',
            fontWeight: '600',
            margin: '0 0 4px 0',
            color: '#333'
          }}>
            {title}
          </h3>
          {subtitle && (
            <p style={{
              fontSize: '14px',
              color: '#666',
              margin: 0
            }}>
              {subtitle}
            </p>
          )}
        </div>

        {/* 描述 */}
        {description && (
          <p className="line-clamp-3" style={{
            fontSize: '14px',
            color: '#555',
            marginBottom: '16px',
            lineHeight: '1.5'
          }}>
            {description}
          </p>
        )}

        {/* 自定义内容 */}
        {children}

        {/* 操作按钮 */}
        {actions && (
          <div style={{
            marginTop: '16px',
            paddingTop: '16px',
            borderTop: '1px solid #e0e0e0',
            display: 'flex',
            gap: '8px',
            flexWrap: 'wrap'
          }}>
            {actions}
          </div>
        )}
      </div>
    </div>
  );
};

export default TouchableCard;