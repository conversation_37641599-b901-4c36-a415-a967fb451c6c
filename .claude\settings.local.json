{"$schema": "https://json.schemastore.org/claude-code-settings.json", "permissions": {"allow": ["Bash(npm run build:*)", "Bash(npm install)", "Bash(npm audit:*)", "Bash(timeout 30 npm run build)", "Bash(npx tsc:*)", "Bash(npm run type-check:*)", "Bash(npm run:*)", "Bash(node:*)", "Bash(npm --version)", "<PERSON><PERSON>(dir)", "Bash(npm test:*)", "Bash(npm install:*)", "Bash(ls:*)", "Bash(find:*)", "<PERSON><PERSON>(touch:*)"], "deny": []}}