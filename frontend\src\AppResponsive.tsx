import React, { useEffect, Suspense, lazy } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ConfigProvider, App as AntdApp, Spin } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';

import { useAuthStore } from './stores/authStore';
import ResponsiveHeader from './components/layout/ResponsiveHeader';
import MobileBottomTabBar from './components/layout/MobileBottomTabBar';
import ProtectedRoute from './components/ui/ProtectedRoute';
import ErrorBoundary from './components/ui/ErrorBoundary';
import './styles/responsive.css';
import './styles/mobile.css';

// 懒加载页面组件
const HomePage = lazy(() => import('./pages/Home/HomePage'));
const BooksListPage = lazy(() => import('./pages/Books/BooksListPage'));
const BookDetailPage = lazy(() => import('./pages/Books/BookDetailPage'));
const CartPage = lazy(() => import('./pages/Cart/CartPage'));
const CheckoutPage = lazy(() => import('./pages/Checkout/CheckoutPage'));
const OrdersPage = lazy(() => import('./pages/Orders/OrdersPage'));
const AuthPage = lazy(() => import('./pages/Auth/AuthPage'));
const UserProfile = lazy(() => import('./components/business/UserProfile'));
const AdminDashboard = lazy(() => import('./pages/Admin/Dashboard'));
const SuperAdminLayout = lazy(() => import('./pages/Admin/SuperAdminLayout'));
const SuperAdminDashboard = lazy(() => import('./pages/Admin/SuperAdminDashboard'));
const AdminManagement = lazy(() => import('./pages/Admin/AdminManagement'));
const AdminPortal = lazy(() => import('./pages/Admin/AdminPortal'));
const AdminTestPanel = lazy(() => import('./components/debug/AdminTestPanel'));

// 设置dayjs中文
dayjs.locale('zh-cn');

// 加载组件
const LoadingComponent: React.FC = () => (
  <div style={{
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    height: '200px'
  }}>
    <Spin size="large" />
  </div>
);

// 响应式布局包装器
interface ResponsiveLayoutProps {
  children: React.ReactNode;
}

const ResponsiveLayout: React.FC<ResponsiveLayoutProps> = ({ children }) => {
  const [isMobile, setIsMobile] = React.useState(false);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 992);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  return (
    <div style={{
      minHeight: '100vh',
      display: 'flex',
      flexDirection: 'column',
      backgroundColor: '#f5f5f5'
    }}>
      <ResponsiveHeader />
      
      <main style={{
        flex: 1,
        paddingBottom: isMobile ? '70px' : '0',
        width: '100%',
        overflowX: 'hidden'
      }}>
        <div className="container-responsive" style={{
          padding: isMobile ? '16px' : '24px',
          maxWidth: '1200px',
          margin: '0 auto'
        }}>
          {children}
        </div>
      </main>

      {isMobile && <MobileBottomTabBar />}
    </div>
  );
};

// 添加移动端viewport设置
const setViewport = () => {
  const viewport = document.querySelector('meta[name="viewport"]');
  if (viewport) {
    viewport.setAttribute('content', 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover');
  }
};

function AppResponsive() {
  const { checkAuth } = useAuthStore();

  useEffect(() => {
    // 应用启动时检查认证状态
    checkAuth();
    
    // 设置移动端viewport
    setViewport();
    
    // 添加移动端优化类
    if ('ontouchstart' in window) {
      document.body.classList.add('touch-device');
    }
    
    // 防止iOS回弹效果
    document.body.addEventListener('touchmove', (e) => {
      if (e.target === document.body) {
        e.preventDefault();
      }
    }, { passive: false });
  }, [checkAuth]);

  return (
    <ErrorBoundary>
      <ConfigProvider 
        locale={zhCN}
        theme={{
          token: {
            colorPrimary: '#007bff',
            borderRadius: 8,
            fontSize: 14,
          },
          components: {
            Button: {
              controlHeight: 44, // 移动端友好的按钮高度
            },
            Input: {
              controlHeight: 44,
            },
          },
        }}
      >
        <AntdApp>
          <Router>
            <Suspense fallback={<LoadingComponent />}>
              <Routes>
                {/* 认证页面 - 不需要布局包装 */}
                <Route path="/auth/*" element={<AuthPage />} />
                <Route path="/login" element={<Navigate to="/auth/login" replace />} />
                <Route path="/register" element={<Navigate to="/auth/register" replace />} />

                {/* 主应用路由 - 使用响应式布局 */}
                <Route path="/" element={<ResponsiveLayout><HomePage /></ResponsiveLayout>} />
                <Route path="/books" element={<ResponsiveLayout><BooksListPage /></ResponsiveLayout>} />
                <Route path="/books/:id" element={<ResponsiveLayout><BookDetailPage /></ResponsiveLayout>} />

                {/* 需要登录的路由 */}
                <Route path="/cart" element={
                  <ProtectedRoute>
                    <ResponsiveLayout><CartPage /></ResponsiveLayout>
                  </ProtectedRoute>
                } />
                <Route path="/checkout" element={
                  <ProtectedRoute>
                    <ResponsiveLayout><CheckoutPage /></ResponsiveLayout>
                  </ProtectedRoute>
                } />
                <Route path="/orders" element={
                  <ProtectedRoute>
                    <ResponsiveLayout><OrdersPage /></ResponsiveLayout>
                  </ProtectedRoute>
                } />
                <Route path="/orders/:id" element={
                  <ProtectedRoute>
                    <ResponsiveLayout><OrdersPage /></ResponsiveLayout>
                  </ProtectedRoute>
                } />
                <Route path="/profile" element={
                  <ProtectedRoute>
                    <ResponsiveLayout><UserProfile /></ResponsiveLayout>
                  </ProtectedRoute>
                } />

                {/* 管理员入口 */}
                <Route path="/admin-portal" element={
                  <ProtectedRoute requireAdmin>
                    <ResponsiveLayout><AdminPortal /></ResponsiveLayout>
                  </ProtectedRoute>
                } />

                {/* 管理员路由 - 不使用移动端布局 */}
                <Route path="/admin/*" element={
                  <ProtectedRoute requireAdmin>
                    <AdminDashboard />
                  </ProtectedRoute>
                } />

                {/* 超级管理员路由 */}
                <Route path="/super-admin/*" element={
                  <ProtectedRoute requireSuperAdmin>
                    <SuperAdminLayout>
                      <Routes>
                        <Route index element={<Navigate to="dashboard" replace />} />
                        <Route path="dashboard" element={<SuperAdminDashboard />} />
                        <Route path="admins" element={<AdminManagement />} />
                        <Route path="system/*" element={<div>系统管理</div>} />
                        <Route path="security/*" element={<div>安全中心</div>} />
                        <Route path="database" element={<div>数据库管理</div>} />
                        <Route path="monitoring" element={<div>系统监控</div>} />
                        <Route path="logs" element={<div>系统日志</div>} />
                        <Route path="settings" element={<div>系统设置</div>} />
                      </Routes>
                    </SuperAdminLayout>
                  </ProtectedRoute>
                } />

                {/* 404重定向 */}
                <Route path="*" element={<Navigate to="/" replace />} />
              </Routes>
            </Suspense>
          </Router>

          {/* 开发环境测试面板 - 只在桌面端显示 */}
          {process.env.NODE_ENV === 'development' && window.innerWidth > 768 && (
            <AdminTestPanel />
          )}
        </AntdApp>
      </ConfigProvider>
    </ErrorBoundary>
  );
}

export default AppResponsive;