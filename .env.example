# 环境配置模板
# 复制此文件为 .env 并填入实际值

# 环境模式
NODE_ENV=production

# 数据库配置
DB_HOST=postgres
DB_PORT=5432
DB_NAME=bookstore
DB_USER=postgres
DB_PASSWORD=your_secure_password_here
DB_SSL=false

# Redis配置
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password_here
REDIS_DB=0

# JWT配置
JWT_SECRET=your_very_long_random_string_here_at_least_32_chars
JWT_EXPIRES_IN=7d
JWT_REFRESH_SECRET=another_very_long_random_string_here
JWT_REFRESH_EXPIRES_IN=30d

# 应用配置
API_PORT=3001
API_URL=http://localhost:3001
FRONTEND_URL=http://localhost:3000

# 文件上传配置
UPLOAD_DIR=/app/uploads
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=jpg,jpeg,png,gif,webp

# 邮件配置（可选）
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USER=<EMAIL>
MAIL_PASS=your_app_password
MAIL_FROM=<EMAIL>

# 第三方服务（可选）
WECHAT_APP_ID=
WECHAT_APP_SECRET=
QQ_APP_ID=
QQ_APP_KEY=

# 监控配置（可选）
SENTRY_DSN=
GRAFANA_API_KEY=
ELASTIC_APM_SERVICE_NAME=bookstore-api
ELASTIC_APM_SECRET_TOKEN=

# 安全配置
CORS_ORIGINS=http://localhost:3000,http://localhost:80
RATE_LIMIT_WINDOW=15
RATE_LIMIT_MAX_REQUESTS=100
BCRYPT_ROUNDS=10

# 日志配置
LOG_LEVEL=info
LOG_DIR=/app/logs
LOG_MAX_SIZE=10m
LOG_MAX_FILES=7d

# 备份配置
BACKUP_DIR=/backups
BACKUP_RETENTION_DAYS=30
BACKUP_CRON="0 2 * * *"

# 生成安全密钥的方法：
# openssl rand -base64 32
# 或
# node -e "console.log(require('crypto').randomBytes(32).toString('base64'))"