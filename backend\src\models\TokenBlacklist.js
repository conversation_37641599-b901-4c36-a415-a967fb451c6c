const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const TokenBlacklist = sequelize.define('TokenBlacklist', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  token_jti: {
    type: DataTypes.STRING(255),
    unique: true,
    allowNull: false,
    comment: 'JWT ID (jti claim)'
  },
  user_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    },
    comment: '用户ID'
  },
  token_type: {
    type: DataTypes.ENUM('access', 'refresh'),
    defaultValue: 'access',
    comment: '令牌类型'
  },
  revoked_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW,
    comment: '撤销时间'
  },
  revoked_reason: {
    type: DataTypes.STRING(255),
    comment: '撤销原因'
  },
  expires_at: {
    type: DataTypes.DATE,
    allowNull: false,
    comment: '令牌过期时间'
  },
  ip_address: {
    type: DataTypes.STRING(45),
    comment: '撤销时的IP地址'
  },
  user_agent: {
    type: DataTypes.TEXT,
    comment: '撤销时的User Agent'
  }
}, {
  tableName: 'token_blacklist',
  timestamps: true,
  underscored: true,
  indexes: [
    {
      fields: ['token_jti'],
      unique: true
    },
    {
      fields: ['user_id']
    },
    {
      fields: ['expires_at']
    },
    {
      fields: ['revoked_at']
    }
  ]
});

// 定义关联
TokenBlacklist.associate = (models) => {
  TokenBlacklist.belongsTo(models.User, {
    foreignKey: 'user_id',
    as: 'user'
  });
};

// 类方法：清理过期的黑名单记录
TokenBlacklist.cleanupExpired = async function() {
  const count = await this.destroy({
    where: {
      expires_at: {
        [Op.lt]: new Date()
      }
    }
  });
  return count;
};

// 类方法：检查令牌是否在黑名单中
TokenBlacklist.isBlacklisted = async function(jti) {
  const blacklisted = await this.findOne({
    where: {
      token_jti: jti,
      expires_at: {
        [Op.gt]: new Date()
      }
    }
  });
  return !!blacklisted;
};

// 类方法：撤销用户的所有令牌
TokenBlacklist.revokeAllUserTokens = async function(userId, reason = 'User logout all devices') {
  // 这需要与JWT生成逻辑配合，记录所有活跃的token JTI
  // 在实际实现中，可能需要维护一个活跃token列表
  console.log(`Revoking all tokens for user ${userId}: ${reason}`);
};

module.exports = TokenBlacklist;