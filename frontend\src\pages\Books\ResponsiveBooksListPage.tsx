import React, { useState, useEffect, useCallback } from 'react';
import { useSearchParams } from 'react-router-dom';
import { message } from 'antd';
import MobileSearch from '../../components/ui/MobileSearch';
import MobileBookCard from '../../components/business/MobileBookCard';
import ResponsiveTable from '../../components/ui/ResponsiveTable';
import { books as booksService } from '../../services/books';
import { favorites as favoritesService } from '../../services/favorites';
import { useAuth } from '../../stores/authStore';
import '../../styles/responsive.css';
import '../../styles/mobile.css';

interface Book {
  id: number;
  title: string;
  author: string;
  price: number;
  original_price?: number;
  cover_image?: string;
  description?: string;
  condition?: string;
  isbn?: string;
  stock?: number;
  category?: {
    id: number;
    name: string;
  };
  user?: {
    id: number;
    username: string;
  };
  created_at: string;
}

interface FilterState {
  category?: number;
  condition?: string;
  minPrice?: number;
  maxPrice?: number;
  sortBy?: string;
}

const ResponsiveBooksListPage: React.FC = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const { user } = useAuth();
  const [books, setBooks] = useState<Book[]>([]);
  const [favorites, setFavorites] = useState<number[]>([]);
  const [loading, setLoading] = useState(true);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [filters, setFilters] = useState<FilterState>({});
  const [isMobile, setIsMobile] = useState(window.innerWidth < 768);

  // 检测是否为移动设备
  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // 加载书籍
  const loadBooks = useCallback(async (pageNum: number, reset = false) => {
    try {
      setLoading(true);
      const searchQuery = searchParams.get('search') || '';
      const categoryId = filters.category || searchParams.get('category');
      
      const response = await booksService.getBooks({
        page: pageNum,
        limit: 20,
        search: searchQuery,
        category_id: categoryId,
        condition: filters.condition,
        min_price: filters.minPrice,
        max_price: filters.maxPrice,
        sort_by: filters.sortBy || 'created_at',
        sort_order: 'DESC'
      });

      if (reset) {
        setBooks(response.data.books);
      } else {
        setBooks(prev => [...prev, ...response.data.books]);
      }
      
      setHasMore(response.data.books.length === 20);
    } catch (error) {
      message.error('加载书籍失败');
    } finally {
      setLoading(false);
    }
  }, [searchParams, filters]);

  // 加载收藏列表
  const loadFavorites = useCallback(async () => {
    if (!user) return;
    
    try {
      const response = await favoritesService.getFavorites();
      setFavorites(response.data.map((fav: any) => fav.book_id));
    } catch (error) {
      console.error('加载收藏列表失败:', error);
    }
  }, [user]);

  useEffect(() => {
    loadBooks(1, true);
    loadFavorites();
  }, [loadBooks, loadFavorites]);

  // 搜索处理
  const handleSearch = (query: string) => {
    setSearchParams({ search: query });
    setPage(1);
    loadBooks(1, true);
  };

  // 收藏切换
  const handleFavoriteToggle = async (bookId: number) => {
    if (!user) {
      message.warning('请先登录');
      return;
    }

    try {
      if (favorites.includes(bookId)) {
        await favoritesService.removeFavorite(bookId);
        setFavorites(prev => prev.filter(id => id !== bookId));
        message.success('取消收藏成功');
      } else {
        await favoritesService.addFavorite(bookId);
        setFavorites(prev => [...prev, bookId]);
        message.success('收藏成功');
      }
    } catch (error) {
      message.error('操作失败');
    }
  };

  // 加载更多
  const handleLoadMore = () => {
    const nextPage = page + 1;
    setPage(nextPage);
    loadBooks(nextPage);
  };

  // 筛选条件
  const filterOptions = [
    { key: 'all', label: '全部' },
    { key: 'new', label: '全新' },
    { key: 'like_new', label: '9成新' },
    { key: 'good', label: '良好' },
    { key: 'price_asc', label: '价格↑' },
    { key: 'price_desc', label: '价格↓' },
  ];

  const handleFilterChange = (key: string) => {
    const newFilters: FilterState = { ...filters };
    
    switch (key) {
      case 'all':
        delete newFilters.condition;
        break;
      case 'new':
      case 'like_new':
      case 'good':
        newFilters.condition = key;
        break;
      case 'price_asc':
        newFilters.sortBy = 'price';
        break;
      case 'price_desc':
        newFilters.sortBy = 'price_desc';
        break;
    }
    
    setFilters(newFilters);
    setPage(1);
    loadBooks(1, true);
  };

  // 表格列配置（桌面端）
  const tableColumns = [
    {
      key: 'cover_image',
      label: '封面',
      render: (value: string, book: Book) => (
        <img 
          src={value || '/placeholder.jpg'} 
          alt={book.title}
          style={{ width: '50px', height: '65px', objectFit: 'cover', borderRadius: '4px' }}
        />
      ),
      hideOnMobile: true
    },
    {
      key: 'title',
      label: '书名',
      priority: 3,
      render: (value: string, book: Book) => (
        <div>
          <div style={{ fontWeight: '500' }}>{value}</div>
          <div style={{ fontSize: '12px', color: '#666' }}>{book.author}</div>
        </div>
      )
    },
    {
      key: 'price',
      label: '价格',
      priority: 2,
      render: (value: number, book: Book) => (
        <div>
          <span style={{ color: '#dc3545', fontWeight: 'bold' }}>¥{value}</span>
          {book.original_price && (
            <span style={{ 
              marginLeft: '8px', 
              fontSize: '12px', 
              color: '#999', 
              textDecoration: 'line-through' 
            }}>
              ¥{book.original_price}
            </span>
          )}
        </div>
      )
    },
    {
      key: 'condition',
      label: '成色',
      priority: 1,
      render: (value: string) => {
        const conditionMap: Record<string, { text: string; color: string }> = {
          new: { text: '全新', color: '#28a745' },
          like_new: { text: '9成新', color: '#17a2b8' },
          good: { text: '良好', color: '#ffc107' },
          fair: { text: '一般', color: '#6c757d' }
        };
        const condition = conditionMap[value] || { text: value, color: '#6c757d' };
        return (
          <span style={{ 
            padding: '2px 8px', 
            borderRadius: '4px', 
            backgroundColor: condition.color, 
            color: '#fff',
            fontSize: '12px'
          }}>
            {condition.text}
          </span>
        );
      }
    },
    {
      key: 'category',
      label: '分类',
      render: (value: any) => value?.name || '-',
      hideOnMobile: true
    },
    {
      key: 'user',
      label: '卖家',
      render: (value: any) => value?.username || '-',
      hideOnMobile: true
    }
  ];

  return (
    <div className="books-page-responsive">
      {/* 搜索栏 */}
      <div style={{
        backgroundColor: '#fff',
        padding: isMobile ? '16px' : '20px',
        marginBottom: '16px',
        borderRadius: isMobile ? '0' : '8px',
        boxShadow: '0 1px 3px rgba(0,0,0,0.1)'
      }}>
        <MobileSearch
          placeholder="搜索书名、作者、ISBN..."
          onSearch={handleSearch}
          value={searchParams.get('search') || ''}
          suggestions={['高等数学', '线性代数', '大学英语', '数据结构', '计算机网络']}
        />
      </div>

      {/* 筛选栏 - 移动端 */}
      {isMobile && (
        <div className="mobile-filters" style={{
          backgroundColor: '#fff',
          padding: '12px 16px',
          marginBottom: '16px',
          display: 'flex',
          gap: '8px',
          overflowX: 'auto',
          WebkitOverflowScrolling: 'touch'
        }}>
          {filterOptions.map(option => (
            <button
              key={option.key}
              onClick={() => handleFilterChange(option.key)}
              className={`filter-item ${
                (option.key === filters.condition || 
                 (option.key === 'price_asc' && filters.sortBy === 'price') ||
                 (option.key === 'price_desc' && filters.sortBy === 'price_desc'))
                ? 'active' : ''
              }`}
              style={{
                padding: '6px 16px',
                borderRadius: '20px',
                border: '1px solid #d9d9d9',
                backgroundColor: '#fff',
                fontSize: '14px',
                whiteSpace: 'nowrap',
                transition: 'all 0.3s'
              }}
            >
              {option.label}
            </button>
          ))}
        </div>
      )}

      {/* 书籍列表 */}
      <div style={{ minHeight: '400px' }}>
        {isMobile ? (
          // 移动端卡片列表
          <div style={{ padding: '0 16px' }}>
            {books.map(book => (
              <div key={book.id} style={{ marginBottom: '12px' }}>
                <MobileBookCard
                  book={book}
                  isFavorited={favorites.includes(book.id)}
                  onFavoriteToggle={handleFavoriteToggle}
                />
              </div>
            ))}
          </div>
        ) : (
          // 桌面端表格
          <div style={{
            backgroundColor: '#fff',
            borderRadius: '8px',
            padding: '20px',
            boxShadow: '0 1px 3px rgba(0,0,0,0.1)'
          }}>
            <ResponsiveTable
              data={books}
              columns={tableColumns}
              loading={loading && page === 1}
              emptyMessage="暂无书籍"
              onRowClick={(book) => window.location.href = `/books/${book.id}`}
            />
          </div>
        )}
      </div>

      {/* 加载更多 */}
      {hasMore && !loading && books.length > 0 && (
        <div style={{
          padding: '20px',
          textAlign: 'center'
        }}>
          <button
            onClick={handleLoadMore}
            className="btn-touch"
            style={{
              width: isMobile ? '100%' : 'auto',
              padding: '12px 32px',
              backgroundColor: '#fff',
              border: '1px solid #d9d9d9',
              borderRadius: '8px',
              fontSize: '16px',
              color: '#595959',
              cursor: 'pointer',
              transition: 'all 0.3s'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.borderColor = '#007bff';
              e.currentTarget.style.color = '#007bff';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.borderColor = '#d9d9d9';
              e.currentTarget.style.color = '#595959';
            }}
          >
            {loading ? '加载中...' : '加载更多'}
          </button>
        </div>
      )}

      {/* 空状态 */}
      {!loading && books.length === 0 && (
        <div className="empty-state" style={{
          padding: '60px 20px',
          textAlign: 'center',
          backgroundColor: '#fff',
          borderRadius: '8px',
          margin: '0 16px'
        }}>
          <div style={{ fontSize: '64px', marginBottom: '16px' }}>📚</div>
          <div style={{ fontSize: '18px', color: '#333', marginBottom: '8px' }}>
            暂无相关书籍
          </div>
          <div style={{ fontSize: '14px', color: '#666' }}>
            试试其他搜索条件吧
          </div>
        </div>
      )}

      {/* 加载中状态 */}
      {loading && page === 1 && (
        <div style={{ padding: '40px', textAlign: 'center' }}>
          <div className="spinner-mobile" />
          <div style={{ marginTop: '16px', color: '#666' }}>加载中...</div>
        </div>
      )}
    </div>
  );
};

export default ResponsiveBooksListPage;