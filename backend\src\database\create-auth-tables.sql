-- 创建令牌黑名单表
CREATE TABLE IF NOT EXISTS token_blacklist (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    token_jti VARCHAR(255) UNIQUE NOT NULL,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    token_type VARCHAR(20) DEFAULT 'access' CHECK (token_type IN ('access', 'refresh')),
    revoked_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    revoked_reason VARCHAR(255),
    expires_at TIMESTAMP NOT NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建刷新令牌表
CREATE TABLE IF NOT EXISTS refresh_tokens (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    token VARCHAR(512) UNIQUE NOT NULL,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    device_id VARCHAR(255),
    device_name VARCHAR(255),
    ip_address VARCHAR(45),
    user_agent TEXT,
    last_used_at TIMESTAMP,
    expires_at TIMESTAMP NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_by_refresh UUID,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX idx_token_blacklist_jti ON token_blacklist(token_jti);
CREATE INDEX idx_token_blacklist_user_id ON token_blacklist(user_id);
CREATE INDEX idx_token_blacklist_expires_at ON token_blacklist(expires_at);
CREATE INDEX idx_token_blacklist_revoked_at ON token_blacklist(revoked_at);

CREATE INDEX idx_refresh_tokens_token ON refresh_tokens(token);
CREATE INDEX idx_refresh_tokens_user_id ON refresh_tokens(user_id);
CREATE INDEX idx_refresh_tokens_device_id ON refresh_tokens(device_id);
CREATE INDEX idx_refresh_tokens_expires_at ON refresh_tokens(expires_at);
CREATE INDEX idx_refresh_tokens_is_active ON refresh_tokens(is_active);

-- 创建更新时间触发器
CREATE OR REPLACE FUNCTION update_token_blacklist_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER set_token_blacklist_updated_at
BEFORE UPDATE ON token_blacklist
FOR EACH ROW
EXECUTE FUNCTION update_token_blacklist_updated_at();

CREATE OR REPLACE FUNCTION update_refresh_tokens_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER set_refresh_tokens_updated_at
BEFORE UPDATE ON refresh_tokens
FOR EACH ROW
EXECUTE FUNCTION update_refresh_tokens_updated_at();

-- 添加注释
COMMENT ON TABLE token_blacklist IS '令牌黑名单表';
COMMENT ON COLUMN token_blacklist.token_jti IS 'JWT ID (jti claim)';
COMMENT ON COLUMN token_blacklist.user_id IS '用户ID';
COMMENT ON COLUMN token_blacklist.token_type IS '令牌类型';
COMMENT ON COLUMN token_blacklist.revoked_at IS '撤销时间';
COMMENT ON COLUMN token_blacklist.revoked_reason IS '撤销原因';
COMMENT ON COLUMN token_blacklist.expires_at IS '令牌过期时间';
COMMENT ON COLUMN token_blacklist.ip_address IS '撤销时的IP地址';
COMMENT ON COLUMN token_blacklist.user_agent IS '撤销时的User Agent';

COMMENT ON TABLE refresh_tokens IS '刷新令牌表';
COMMENT ON COLUMN refresh_tokens.token IS '刷新令牌（哈希值）';
COMMENT ON COLUMN refresh_tokens.user_id IS '用户ID';
COMMENT ON COLUMN refresh_tokens.device_id IS '设备标识符';
COMMENT ON COLUMN refresh_tokens.device_name IS '设备名称';
COMMENT ON COLUMN refresh_tokens.ip_address IS 'IP地址';
COMMENT ON COLUMN refresh_tokens.user_agent IS 'User Agent';
COMMENT ON COLUMN refresh_tokens.last_used_at IS '最后使用时间';
COMMENT ON COLUMN refresh_tokens.expires_at IS '过期时间';
COMMENT ON COLUMN refresh_tokens.is_active IS '是否有效';
COMMENT ON COLUMN refresh_tokens.created_by_refresh IS '由哪个刷新令牌创建（用于链式追踪）';