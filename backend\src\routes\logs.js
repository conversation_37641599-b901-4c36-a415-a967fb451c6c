const express = require('express');
const { authenticateToken, requireAdmin } = require('../middleware/authEnhanced');
const logger = require('../utils/logger');
const path = require('path');
const fs = require('fs').promises;

const router = express.Router();

// 获取日志文件列表
router.get('/files', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const files = await logger.getLogFiles();
    
    res.json({
      success: true,
      data: {
        files: files.map(file => ({
          name: file.name,
          size: file.size,
          sizeFormatted: formatBytes(file.size),
          created: file.name.match(/\d{4}-\d{2}-\d{2}/)?.[0] || 'unknown'
        }))
      }
    });
  } catch (error) {
    logger.error('获取日志文件列表失败', { error: error.message });
    res.status(500).json({
      success: false,
      message: '获取日志文件列表失败'
    });
  }
});

// 读取日志文件内容
router.get('/files/:filename', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { filename } = req.params;
    const { lines = 100 } = req.query;
    
    // 安全检查文件名
    if (filename.includes('..') || filename.includes('/')) {
      return res.status(400).json({
        success: false,
        message: '非法文件名'
      });
    }
    
    const logLines = await logger.readLogFile(filename, parseInt(lines));
    
    res.json({
      success: true,
      data: {
        filename,
        lines: logLines.map(line => {
          try {
            return JSON.parse(line);
          } catch {
            return { message: line };
          }
        }),
        totalLines: logLines.length
      }
    });
  } catch (error) {
    logger.error('读取日志文件失败', { error: error.message });
    res.status(500).json({
      success: false,
      message: '读取日志文件失败'
    });
  }
});

// 搜索日志
router.post('/search', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { 
      level, 
      startDate, 
      endDate, 
      keyword, 
      limit = 100 
    } = req.body;
    
    const files = await logger.getLogFiles();
    const results = [];
    
    for (const file of files) {
      // 检查文件日期是否在范围内
      const fileDate = file.name.match(/\d{4}-\d{2}-\d{2}/)?.[0];
      if (fileDate) {
        if (startDate && fileDate < startDate) continue;
        if (endDate && fileDate > endDate) continue;
      }
      
      const lines = await logger.readLogFile(file.name, 1000);
      
      for (const line of lines) {
        try {
          const log = JSON.parse(line);
          
          // 过滤条件
          if (level && log.level !== level) continue;
          if (keyword && !JSON.stringify(log).toLowerCase().includes(keyword.toLowerCase())) continue;
          
          results.push({
            ...log,
            file: file.name
          });
          
          if (results.length >= limit) break;
        } catch {
          // 跳过无法解析的行
        }
      }
      
      if (results.length >= limit) break;
    }
    
    res.json({
      success: true,
      data: {
        results: results.slice(0, limit),
        total: results.length
      }
    });
  } catch (error) {
    logger.error('搜索日志失败', { error: error.message });
    res.status(500).json({
      success: false,
      message: '搜索日志失败'
    });
  }
});

// 下载日志文件
router.get('/download/:filename', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { filename } = req.params;
    
    // 安全检查文件名
    if (filename.includes('..') || filename.includes('/')) {
      return res.status(400).json({
        success: false,
        message: '非法文件名'
      });
    }
    
    const logDir = process.env.LOG_DIR || path.join(__dirname, '../../logs');
    const filePath = path.join(logDir, filename);
    
    // 检查文件是否存在
    try {
      await fs.access(filePath);
    } catch {
      return res.status(404).json({
        success: false,
        message: '文件不存在'
      });
    }
    
    res.download(filePath, filename);
  } catch (error) {
    logger.error('下载日志文件失败', { error: error.message });
    res.status(500).json({
      success: false,
      message: '下载日志文件失败'
    });
  }
});

// 清理旧日志
router.post('/cleanup', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { daysToKeep = 30 } = req.body;
    
    const deletedCount = await logger.cleanOldLogs(parseInt(daysToKeep));
    
    res.json({
      success: true,
      message: `清理了 ${deletedCount} 个旧日志文件`,
      data: {
        deletedCount,
        daysToKeep
      }
    });
  } catch (error) {
    logger.error('清理日志失败', { error: error.message });
    res.status(500).json({
      success: false,
      message: '清理日志失败'
    });
  }
});

// 获取日志统计
router.get('/stats', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const files = await logger.getLogFiles();
    
    // 统计各级别日志数量
    const stats = {
      totalFiles: files.length,
      totalSize: files.reduce((sum, file) => sum + file.size, 0),
      levels: {
        error: 0,
        warn: 0,
        info: 0,
        http: 0,
        debug: 0
      }
    };
    
    // 读取最近的日志文件进行统计
    if (files.length > 0) {
      const recentFile = files[files.length - 1];
      const lines = await logger.readLogFile(recentFile.name, 1000);
      
      for (const line of lines) {
        try {
          const log = JSON.parse(line);
          if (log.level && stats.levels[log.level] !== undefined) {
            stats.levels[log.level]++;
          }
        } catch {
          // 跳过无法解析的行
        }
      }
    }
    
    res.json({
      success: true,
      data: {
        ...stats,
        totalSizeFormatted: formatBytes(stats.totalSize)
      }
    });
  } catch (error) {
    logger.error('获取日志统计失败', { error: error.message });
    res.status(500).json({
      success: false,
      message: '获取日志统计失败'
    });
  }
});

// 实时日志流（WebSocket）
router.get('/stream', authenticateToken, requireAdmin, (req, res) => {
  res.json({
    success: true,
    message: '请使用WebSocket连接到 /ws/logs 获取实时日志'
  });
});

// 工具函数：格式化字节大小
function formatBytes(bytes, decimals = 2) {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
}

module.exports = router;