version: '3.8'

services:
  # PostgreSQL数据库服务
  postgres:
    image: postgres:15-alpine
    container_name: bookstore_postgres_secure
    restart: unless-stopped
    environment:
      POSTGRES_USER: ${POSTGRES_USER:-bookstore_user}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_DB: ${POSTGRES_DB:-bookstore_db}
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --locale=zh_CN.UTF-8"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    networks:
      - bookstore_network
    ports:
      - "127.0.0.1:5432:5432"  # 只绑定到本地
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-bookstore_user}"]
      interval: 10s
      timeout: 5s
      retries: 5
    security_opt:
      - no-new-privileges:true
    deploy:
      resources:
        limits:
          cpus: '1'
          memory: 512M

  # Redis缓存服务
  redis:
    image: redis:7-alpine
    container_name: bookstore_redis_secure
    restart: unless-stopped
    command: >
      redis-server
      --requirepass ${REDIS_PASSWORD}
      --maxmemory 256mb
      --maxmemory-policy allkeys-lru
      --appendonly yes
      --appendfilename "appendonly.aof"
      --dbfilename "dump.rdb"
      --dir /data
      --protected-mode yes
      --bind 0.0.0.0
      --port 6379
    volumes:
      - redis_data:/data
    networks:
      - bookstore_network
    ports:
      - "127.0.0.1:6379:6379"  # 只绑定到本地
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    security_opt:
      - no-new-privileges:true
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 256M

  # 后端API服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.prod
      args:
        NODE_ENV: production
    container_name: bookstore_backend_secure
    restart: unless-stopped
    environment:
      NODE_ENV: production
      PORT: 3001
      DATABASE_URL: postgresql://${POSTGRES_USER:-bookstore_user}:${POSTGRES_PASSWORD}@postgres:5432/${POSTGRES_DB:-bookstore_db}
      JWT_SECRET: ${JWT_SECRET}
      JWT_REFRESH_SECRET: ${JWT_REFRESH_SECRET}
      REDIS_HOST: redis
      REDIS_PORT: 6379
      REDIS_PASSWORD: ${REDIS_PASSWORD}
      UPLOAD_DIR: /app/uploads
      MAX_FILE_SIZE: 5242880  # 5MB
      ALLOWED_FILE_TYPES: image/jpeg,image/png,image/gif,image/webp
      CORS_ORIGIN: ${FRONTEND_URL:-http://localhost}
      SENTRY_DSN: ${SENTRY_DSN:-}
      LOG_LEVEL: ${LOG_LEVEL:-info}
    volumes:
      - backend_uploads:/app/uploads
      - backend_logs:/app/logs
    networks:
      - bookstore_network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3001/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    security_opt:
      - no-new-privileges:true
    user: "node"
    read_only: true
    tmpfs:
      - /tmp
    deploy:
      resources:
        limits:
          cpus: '1'
          memory: 512M

  # 前端服务
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.prod
      args:
        REACT_APP_API_URL: ${BACKEND_URL:-http://localhost/api}
        REACT_APP_SOCKET_URL: ${SOCKET_URL:-http://localhost}
    container_name: bookstore_frontend_secure
    restart: unless-stopped
    networks:
      - bookstore_network
    depends_on:
      - backend
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost"]
      interval: 30s
      timeout: 10s
      retries: 3
    security_opt:
      - no-new-privileges:true
    read_only: true
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 256M

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: bookstore_nginx_secure
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.secure.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - nginx_logs:/var/log/nginx
      - nginx_cache:/var/cache/nginx
    networks:
      - bookstore_network
    depends_on:
      - frontend
      - backend
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    security_opt:
      - no-new-privileges:true
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 128M

  # 备份服务
  backup:
    image: postgres:15-alpine
    container_name: bookstore_backup
    restart: unless-stopped
    environment:
      PGPASSWORD: ${POSTGRES_PASSWORD}
      BACKUP_RETENTION_DAYS: ${BACKUP_RETENTION_DAYS:-7}
    volumes:
      - ./backup:/backup
      - ./scripts/backup.sh:/usr/local/bin/backup.sh:ro
    networks:
      - bookstore_network
    depends_on:
      postgres:
        condition: service_healthy
    entrypoint: ["/bin/sh"]
    command: ["-c", "chmod +x /usr/local/bin/backup.sh && crond -f -d 8"]
    security_opt:
      - no-new-privileges:true
    deploy:
      resources:
        limits:
          cpus: '0.25'
          memory: 128M

networks:
  bookstore_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  backend_uploads:
    driver: local
  backend_logs:
    driver: local
  nginx_logs:
    driver: local
  nginx_cache:
    driver: local