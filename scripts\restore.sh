#!/bin/bash

# 收书卖书平台 - 数据恢复脚本
# 作者: DevOps Team
# 版本: 1.0

set -euo pipefail

# 配置变量
BACKUP_DIR="/backups"
DB_NAME="${DB_NAME:-booktrading}"
DB_USER="${DB_USER:-booktrading}"
DB_HOST="${DB_HOST:-postgres}"

# 日志函数
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

# 错误处理
error_exit() {
    log "ERROR: $1"
    exit 1
}

# 显示使用方法
usage() {
    cat << EOF
使用方法: $0 [选项] <备份文件或时间戳>

选项:
  -d, --database-only    仅恢复数据库
  -f, --files-only       仅恢复文件
  -l, --list            列出可用的备份
  -y, --yes             跳过确认提示
  -h, --help            显示此帮助信息

示例:
  $0 20231201_120000                    # 恢复指定时间戳的完整备份
  $0 -d /backups/database/backup.sql    # 仅恢复数据库
  $0 -l                                 # 列出可用备份

EOF
}

# 列出可用备份
list_backups() {
    log "可用的备份文件:"
    echo "================================"
    
    # 数据库备份
    echo "数据库备份:"
    find "${BACKUP_DIR}/database" -name "*.sql.custom" -o -name "*.sql.gz" 2>/dev/null | sort -r | head -10
    echo
    
    # 文件备份
    echo "文件备份:"
    find "${BACKUP_DIR}/uploads" -name "*.tar.gz" 2>/dev/null | sort -r | head -5
    find "${BACKUP_DIR}/logs" -name "*.tar.gz" 2>/dev/null | sort -r | head -5
    echo
    
    # 备份清单
    echo "备份清单:"
    find "${BACKUP_DIR}" -name "manifest_*.txt" 2>/dev/null | sort -r | head -5
}

# 检查备份文件是否存在
check_backup_exists() {
    local backup_identifier="$1"
    local backup_type="$2"
    
    case "$backup_type" in
        "database")
            # 查找数据库备份文件
            if [[ -f "$backup_identifier" ]]; then
                echo "$backup_identifier"
            elif [[ -f "${BACKUP_DIR}/database/${DB_NAME}_${backup_identifier}.sql.custom" ]]; then
                echo "${BACKUP_DIR}/database/${DB_NAME}_${backup_identifier}.sql.custom"
            elif [[ -f "${BACKUP_DIR}/database/${DB_NAME}_${backup_identifier}.sql.gz" ]]; then
                echo "${BACKUP_DIR}/database/${DB_NAME}_${backup_identifier}.sql.gz"
            else
                return 1
            fi
            ;;
        "uploads")
            if [[ -f "${BACKUP_DIR}/uploads/uploads_${backup_identifier}.tar.gz" ]]; then
                echo "${BACKUP_DIR}/uploads/uploads_${backup_identifier}.tar.gz"
            else
                return 1
            fi
            ;;
        "logs")
            if [[ -f "${BACKUP_DIR}/logs/logs_${backup_identifier}.tar.gz" ]]; then
                echo "${BACKUP_DIR}/logs/logs_${backup_identifier}.tar.gz"
            else
                return 1
            fi
            ;;
    esac
}

# 恢复数据库
restore_database() {
    local backup_file="$1"
    local confirm_restore="$2"
    
    log "准备恢复数据库: $backup_file"
    
    if [[ ! -f "$backup_file" ]]; then
        error_exit "备份文件不存在: $backup_file"
    fi
    
    # 确认提示
    if [[ "$confirm_restore" != "yes" ]]; then
        echo "警告: 此操作将完全替换当前数据库!"
        echo "当前数据库: $DB_NAME"
        echo "备份文件: $backup_file"
        read -p "确认继续? (输入 'yes' 继续): " confirm
        if [[ "$confirm" != "yes" ]]; then
            log "恢复操作已取消"
            exit 0
        fi
    fi
    
    log "开始数据库恢复..."
    
    # 判断备份文件类型并恢复
    if [[ "$backup_file" == *.sql.custom ]]; then
        # pg_restore 格式
        log "使用 pg_restore 恢复自定义格式备份..."
        if pg_restore -h "$DB_HOST" -U "$DB_USER" -d "$DB_NAME" \
            --verbose --clean --if-exists --single-transaction \
            "$backup_file"; then
            log "数据库恢复成功"
        else
            error_exit "数据库恢复失败"
        fi
    elif [[ "$backup_file" == *.sql.gz ]]; then
        # 压缩的SQL文件
        log "恢复压缩的SQL备份..."
        if zcat "$backup_file" | psql -h "$DB_HOST" -U "$DB_USER" -d "$DB_NAME" -v ON_ERROR_STOP=1; then
            log "数据库恢复成功"
        else
            error_exit "数据库恢复失败"
        fi
    elif [[ "$backup_file" == *.sql ]]; then
        # 普通SQL文件
        log "恢复SQL备份..."
        if psql -h "$DB_HOST" -U "$DB_USER" -d "$DB_NAME" -v ON_ERROR_STOP=1 -f "$backup_file"; then
            log "数据库恢复成功"
        else
            error_exit "数据库恢复失败"
        fi
    else
        error_exit "不支持的备份文件格式: $backup_file"
    fi
}

# 恢复文件
restore_files() {
    local backup_file="$1"
    local file_type="$2"
    local confirm_restore="$3"
    
    log "准备恢复文件: $backup_file"
    
    if [[ ! -f "$backup_file" ]]; then
        log "警告: 备份文件不存在: $backup_file"
        return 0
    fi
    
    # 确认提示
    if [[ "$confirm_restore" != "yes" ]]; then
        echo "警告: 此操作将替换现有的$file_type文件!"
        read -p "确认继续? (输入 'yes' 继续): " confirm
        if [[ "$confirm" != "yes" ]]; then
            log "$file_type 恢复操作已取消"
            return 0
        fi
    fi
    
    log "开始恢复 $file_type 文件..."
    
    # 创建目标目录
    case "$file_type" in
        "uploads")
            mkdir -p /app/uploads
            if tar -xzf "$backup_file" -C /app; then
                log "上传文件恢复成功"
            else
                error_exit "上传文件恢复失败"
            fi
            ;;
        "logs")
            mkdir -p /app/logs
            if tar -xzf "$backup_file" -C /app; then
                log "日志文件恢复成功"
            else
                error_exit "日志文件恢复失败"
            fi
            ;;
    esac
}

# 验证恢复结果
verify_restore() {
    log "验证恢复结果..."
    
    # 检查数据库连接
    if psql -h "$DB_HOST" -U "$DB_USER" -d "$DB_NAME" -c "SELECT version();" > /dev/null 2>&1; then
        log "数据库连接: 正常"
        
        # 检查表数量
        local table_count=$(psql -h "$DB_HOST" -U "$DB_USER" -d "$DB_NAME" -t -c "SELECT COUNT(*) FROM information_schema.tables WHERE table_type = 'BASE TABLE' AND table_schema = 'public';")
        log "数据库表数量: $table_count"
    else
        log "警告: 数据库连接失败"
    fi
    
    # 检查文件目录
    if [[ -d "/app/uploads" ]]; then
        local upload_count=$(find /app/uploads -type f | wc -l)
        log "上传文件数量: $upload_count"
    fi
    
    if [[ -d "/app/logs" ]]; then
        local log_count=$(find /app/logs -type f | wc -l)
        log "日志文件数量: $log_count"
    fi
}

# 主函数
main() {
    local database_only=false
    local files_only=false
    local confirm_restore="no"
    local backup_identifier=""
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -d|--database-only)
                database_only=true
                shift
                ;;
            -f|--files-only)
                files_only=true
                shift
                ;;
            -l|--list)
                list_backups
                exit 0
                ;;
            -y|--yes)
                confirm_restore="yes"
                shift
                ;;
            -h|--help)
                usage
                exit 0
                ;;
            -*)
                error_exit "未知选项: $1"
                ;;
            *)
                backup_identifier="$1"
                shift
                ;;
        esac
    done
    
    # 检查参数
    if [[ -z "$backup_identifier" ]]; then
        usage
        error_exit "请指定备份文件或时间戳"
    fi
    
    # 检查环境变量
    if [[ -z "${PGPASSWORD:-}" ]]; then
        error_exit "PGPASSWORD 环境变量未设置"
    fi
    
    log "=== 开始恢复过程 ==="
    log "备份标识: $backup_identifier"
    
    # 恢复数据库
    if [[ "$files_only" != true ]]; then
        if db_backup=$(check_backup_exists "$backup_identifier" "database"); then
            restore_database "$db_backup" "$confirm_restore"
        else
            if [[ "$database_only" == true ]]; then
                error_exit "找不到数据库备份文件"
            else
                log "警告: 找不到数据库备份文件，跳过数据库恢复"
            fi
        fi
    fi
    
    # 恢复文件
    if [[ "$database_only" != true ]]; then
        # 恢复上传文件
        if uploads_backup=$(check_backup_exists "$backup_identifier" "uploads"); then
            restore_files "$uploads_backup" "uploads" "$confirm_restore"
        else
            log "警告: 找不到上传文件备份，跳过"
        fi
        
        # 恢复日志文件
        if logs_backup=$(check_backup_exists "$backup_identifier" "logs"); then
            restore_files "$logs_backup" "logs" "$confirm_restore"
        else
            log "警告: 找不到日志文件备份，跳过"
        fi
    fi
    
    # 验证恢复结果
    verify_restore
    
    log "=== 恢复过程完成 ==="
}

# 错误处理
trap 'error_exit "恢复过程中断"' INT TERM

# 运行主函数
main "$@"