{"ast": null, "code": "import { create } from 'zustand';\nimport { persist } from 'zustand/middleware';\nimport { message } from 'antd';\nexport const useCartStore = create()(persist((set, get) => ({\n  // 状态\n  items: [],\n  totalItems: 0,\n  totalAmount: 0,\n  // 添加商品到购物车\n  addItem: (book, quantity = 1) => {\n    const {\n      items\n    } = get();\n    const existingItem = items.find(item => item.book_id === book.id);\n    if (existingItem) {\n      // 如果商品已存在，增加数量\n      const newQuantity = existingItem.quantity + quantity;\n      if (newQuantity > book.stock) {\n        message.warning(`库存不足，最多只能添加 ${book.stock} 本`);\n        return;\n      }\n      get().updateQuantity(book.id, newQuantity);\n    } else {\n      // 如果是新商品，添加到购物车\n      if (quantity > book.stock) {\n        message.warning(`库存不足，最多只能添加 ${book.stock} 本`);\n        return;\n      }\n      const newItem = {\n        book_id: book.id,\n        book,\n        quantity,\n        price: book.price,\n        original_price: book.original_price\n      };\n      set(state => ({\n        items: [...state.items, newItem]\n      }));\n    }\n    get().calculateTotals();\n    message.success('已添加到购物车');\n  },\n  // 从购物车移除商品\n  removeItem: bookId => {\n    set(state => ({\n      items: state.items.filter(item => item.book_id !== bookId)\n    }));\n    get().calculateTotals();\n    message.success('已从购物车移除');\n  },\n  // 更新商品数量\n  updateQuantity: (bookId, quantity) => {\n    if (quantity <= 0) {\n      get().removeItem(bookId);\n      return;\n    }\n    const {\n      items\n    } = get();\n    const item = items.find(item => item.book_id === bookId);\n    if (item && quantity > item.book.stock) {\n      message.warning(`库存不足，最多只能购买 ${item.book.stock} 本`);\n      return;\n    }\n    if (!item) {\n      console.warn(`未找到ID为 ${bookId} 的商品`);\n      return;\n    }\n    set(state => ({\n      items: state.items.map(item => item.book_id === bookId ? {\n        ...item,\n        quantity\n      } : item)\n    }));\n    get().calculateTotals();\n  },\n  // 清空购物车\n  clearCart: () => {\n    set({\n      items: [],\n      totalItems: 0,\n      totalAmount: 0\n    });\n  },\n  // 获取指定商品\n  getItem: bookId => {\n    const {\n      items\n    } = get();\n    return items.find(item => item.book_id === bookId);\n  },\n  // 计算总计\n  calculateTotals: () => {\n    const {\n      items\n    } = get();\n    const totalItems = items.reduce((sum, item) => sum + item.quantity, 0);\n    const totalAmount = items.reduce((sum, item) => {\n      const price = item.price || item.book.price;\n      return sum + price * item.quantity;\n    }, 0);\n    set({\n      totalItems,\n      totalAmount\n    });\n  },\n  // 获取总金额\n  getTotal: () => {\n    const {\n      totalAmount\n    } = get();\n    return totalAmount;\n  },\n  // 获取商品总数量\n  getItemCount: () => {\n    const {\n      totalItems\n    } = get();\n    return totalItems;\n  }\n}), {\n  name: 'cart-storage',\n  partialize: state => ({\n    items: state.items\n  }),\n  onRehydrateStorage: () => state => {\n    // 重新计算总计\n    if (state) {\n      state.calculateTotals();\n    }\n  }\n}));", "map": {"version": 3, "names": ["create", "persist", "message", "useCartStore", "set", "get", "items", "totalItems", "totalAmount", "addItem", "book", "quantity", "existingItem", "find", "item", "book_id", "id", "newQuantity", "stock", "warning", "updateQuantity", "newItem", "price", "original_price", "state", "calculateTotals", "success", "removeItem", "bookId", "filter", "console", "warn", "map", "clearCart", "getItem", "reduce", "sum", "getTotal", "getItemCount", "name", "partialize", "onRehydrateStorage"], "sources": ["D:/claude镜像/收书卖书/frontend/src/stores/cartStore.ts"], "sourcesContent": ["import { create } from 'zustand';\nimport { persist } from 'zustand/middleware';\nimport { CartItem, Book } from '../types';\nimport { message } from 'antd';\n\ninterface CartState {\n  items: CartItem[];\n  totalItems: number;\n  totalAmount: number;\n}\n\ninterface CartActions {\n  addItem: (book: Book, quantity?: number) => void;\n  removeItem: (bookId: string) => void;\n  updateQuantity: (bookId: string, quantity: number) => void;\n  clearCart: () => void;\n  getItem: (bookId: string) => CartItem | undefined;\n  calculateTotals: () => void;\n  getTotal: () => number;\n  getItemCount: () => number;\n}\n\nexport const useCartStore = create<CartState & CartActions>()(\n  persist(\n    (set, get) => ({\n      // 状态\n      items: [],\n      totalItems: 0,\n      totalAmount: 0,\n\n      // 添加商品到购物车\n      addItem: (book: Book, quantity = 1) => {\n        const { items } = get();\n        const existingItem = items.find(item => item.book_id === book.id);\n\n        if (existingItem) {\n          // 如果商品已存在，增加数量\n          const newQuantity = existingItem.quantity + quantity;\n          if (newQuantity > book.stock) {\n            message.warning(`库存不足，最多只能添加 ${book.stock} 本`);\n            return;\n          }\n          get().updateQuantity(book.id, newQuantity);\n        } else {\n          // 如果是新商品，添加到购物车\n          if (quantity > book.stock) {\n            message.warning(`库存不足，最多只能添加 ${book.stock} 本`);\n            return;\n          }\n          const newItem: CartItem = {\n            book_id: book.id,\n            book,\n            quantity,\n            price: book.price,\n            original_price: book.original_price\n          };\n          set(state => ({\n            items: [...state.items, newItem]\n          }));\n        }\n        \n        get().calculateTotals();\n        message.success('已添加到购物车');\n      },\n\n      // 从购物车移除商品\n      removeItem: (bookId: string) => {\n        set(state => ({\n          items: state.items.filter(item => item.book_id !== bookId)\n        }));\n        get().calculateTotals();\n        message.success('已从购物车移除');\n      },\n\n      // 更新商品数量\n      updateQuantity: (bookId: string, quantity: number) => {\n        if (quantity <= 0) {\n          get().removeItem(bookId);\n          return;\n        }\n\n        const { items } = get();\n        const item = items.find(item => item.book_id === bookId);\n        \n        if (item && quantity > item.book.stock) {\n          message.warning(`库存不足，最多只能购买 ${item.book.stock} 本`);\n          return;\n        }\n        \n        if (!item) {\n          console.warn(`未找到ID为 ${bookId} 的商品`);\n          return;\n        }\n\n        set(state => ({\n          items: state.items.map(item =>\n            item.book_id === bookId\n              ? { ...item, quantity }\n              : item\n          )\n        }));\n        get().calculateTotals();\n      },\n\n      // 清空购物车\n      clearCart: () => {\n        set({\n          items: [],\n          totalItems: 0,\n          totalAmount: 0\n        });\n      },\n\n      // 获取指定商品\n      getItem: (bookId: string) => {\n        const { items } = get();\n        return items.find(item => item.book_id === bookId);\n      },\n\n      // 计算总计\n      calculateTotals: () => {\n        const { items } = get();\n        const totalItems = items.reduce((sum, item) => sum + item.quantity, 0);\n        const totalAmount = items.reduce((sum, item) => {\n          const price = item.price || item.book.price;\n          return sum + (price * item.quantity);\n        }, 0);\n\n        set({\n          totalItems,\n          totalAmount\n        });\n      },\n\n      // 获取总金额\n      getTotal: () => {\n        const { totalAmount } = get();\n        return totalAmount;\n      },\n\n      // 获取商品总数量\n      getItemCount: () => {\n        const { totalItems } = get();\n        return totalItems;\n      }\n    }),\n    {\n      name: 'cart-storage',\n      partialize: (state) => ({\n        items: state.items\n      }),\n      onRehydrateStorage: () => (state) => {\n        // 重新计算总计\n        if (state) {\n          state.calculateTotals();\n        }\n      }\n    }\n  )\n);\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,SAAS;AAChC,SAASC,OAAO,QAAQ,oBAAoB;AAE5C,SAASC,OAAO,QAAQ,MAAM;AAmB9B,OAAO,MAAMC,YAAY,GAAGH,MAAM,CAA0B,CAAC,CAC3DC,OAAO,CACL,CAACG,GAAG,EAAEC,GAAG,MAAM;EACb;EACAC,KAAK,EAAE,EAAE;EACTC,UAAU,EAAE,CAAC;EACbC,WAAW,EAAE,CAAC;EAEd;EACAC,OAAO,EAAEA,CAACC,IAAU,EAAEC,QAAQ,GAAG,CAAC,KAAK;IACrC,MAAM;MAAEL;IAAM,CAAC,GAAGD,GAAG,CAAC,CAAC;IACvB,MAAMO,YAAY,GAAGN,KAAK,CAACO,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACC,OAAO,KAAKL,IAAI,CAACM,EAAE,CAAC;IAEjE,IAAIJ,YAAY,EAAE;MAChB;MACA,MAAMK,WAAW,GAAGL,YAAY,CAACD,QAAQ,GAAGA,QAAQ;MACpD,IAAIM,WAAW,GAAGP,IAAI,CAACQ,KAAK,EAAE;QAC5BhB,OAAO,CAACiB,OAAO,CAAC,eAAeT,IAAI,CAACQ,KAAK,IAAI,CAAC;QAC9C;MACF;MACAb,GAAG,CAAC,CAAC,CAACe,cAAc,CAACV,IAAI,CAACM,EAAE,EAAEC,WAAW,CAAC;IAC5C,CAAC,MAAM;MACL;MACA,IAAIN,QAAQ,GAAGD,IAAI,CAACQ,KAAK,EAAE;QACzBhB,OAAO,CAACiB,OAAO,CAAC,eAAeT,IAAI,CAACQ,KAAK,IAAI,CAAC;QAC9C;MACF;MACA,MAAMG,OAAiB,GAAG;QACxBN,OAAO,EAAEL,IAAI,CAACM,EAAE;QAChBN,IAAI;QACJC,QAAQ;QACRW,KAAK,EAAEZ,IAAI,CAACY,KAAK;QACjBC,cAAc,EAAEb,IAAI,CAACa;MACvB,CAAC;MACDnB,GAAG,CAACoB,KAAK,KAAK;QACZlB,KAAK,EAAE,CAAC,GAAGkB,KAAK,CAAClB,KAAK,EAAEe,OAAO;MACjC,CAAC,CAAC,CAAC;IACL;IAEAhB,GAAG,CAAC,CAAC,CAACoB,eAAe,CAAC,CAAC;IACvBvB,OAAO,CAACwB,OAAO,CAAC,SAAS,CAAC;EAC5B,CAAC;EAED;EACAC,UAAU,EAAGC,MAAc,IAAK;IAC9BxB,GAAG,CAACoB,KAAK,KAAK;MACZlB,KAAK,EAAEkB,KAAK,CAAClB,KAAK,CAACuB,MAAM,CAACf,IAAI,IAAIA,IAAI,CAACC,OAAO,KAAKa,MAAM;IAC3D,CAAC,CAAC,CAAC;IACHvB,GAAG,CAAC,CAAC,CAACoB,eAAe,CAAC,CAAC;IACvBvB,OAAO,CAACwB,OAAO,CAAC,SAAS,CAAC;EAC5B,CAAC;EAED;EACAN,cAAc,EAAEA,CAACQ,MAAc,EAAEjB,QAAgB,KAAK;IACpD,IAAIA,QAAQ,IAAI,CAAC,EAAE;MACjBN,GAAG,CAAC,CAAC,CAACsB,UAAU,CAACC,MAAM,CAAC;MACxB;IACF;IAEA,MAAM;MAAEtB;IAAM,CAAC,GAAGD,GAAG,CAAC,CAAC;IACvB,MAAMS,IAAI,GAAGR,KAAK,CAACO,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACC,OAAO,KAAKa,MAAM,CAAC;IAExD,IAAId,IAAI,IAAIH,QAAQ,GAAGG,IAAI,CAACJ,IAAI,CAACQ,KAAK,EAAE;MACtChB,OAAO,CAACiB,OAAO,CAAC,eAAeL,IAAI,CAACJ,IAAI,CAACQ,KAAK,IAAI,CAAC;MACnD;IACF;IAEA,IAAI,CAACJ,IAAI,EAAE;MACTgB,OAAO,CAACC,IAAI,CAAC,UAAUH,MAAM,MAAM,CAAC;MACpC;IACF;IAEAxB,GAAG,CAACoB,KAAK,KAAK;MACZlB,KAAK,EAAEkB,KAAK,CAAClB,KAAK,CAAC0B,GAAG,CAAClB,IAAI,IACzBA,IAAI,CAACC,OAAO,KAAKa,MAAM,GACnB;QAAE,GAAGd,IAAI;QAAEH;MAAS,CAAC,GACrBG,IACN;IACF,CAAC,CAAC,CAAC;IACHT,GAAG,CAAC,CAAC,CAACoB,eAAe,CAAC,CAAC;EACzB,CAAC;EAED;EACAQ,SAAS,EAAEA,CAAA,KAAM;IACf7B,GAAG,CAAC;MACFE,KAAK,EAAE,EAAE;MACTC,UAAU,EAAE,CAAC;MACbC,WAAW,EAAE;IACf,CAAC,CAAC;EACJ,CAAC;EAED;EACA0B,OAAO,EAAGN,MAAc,IAAK;IAC3B,MAAM;MAAEtB;IAAM,CAAC,GAAGD,GAAG,CAAC,CAAC;IACvB,OAAOC,KAAK,CAACO,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACC,OAAO,KAAKa,MAAM,CAAC;EACpD,CAAC;EAED;EACAH,eAAe,EAAEA,CAAA,KAAM;IACrB,MAAM;MAAEnB;IAAM,CAAC,GAAGD,GAAG,CAAC,CAAC;IACvB,MAAME,UAAU,GAAGD,KAAK,CAAC6B,MAAM,CAAC,CAACC,GAAG,EAAEtB,IAAI,KAAKsB,GAAG,GAAGtB,IAAI,CAACH,QAAQ,EAAE,CAAC,CAAC;IACtE,MAAMH,WAAW,GAAGF,KAAK,CAAC6B,MAAM,CAAC,CAACC,GAAG,EAAEtB,IAAI,KAAK;MAC9C,MAAMQ,KAAK,GAAGR,IAAI,CAACQ,KAAK,IAAIR,IAAI,CAACJ,IAAI,CAACY,KAAK;MAC3C,OAAOc,GAAG,GAAId,KAAK,GAAGR,IAAI,CAACH,QAAS;IACtC,CAAC,EAAE,CAAC,CAAC;IAELP,GAAG,CAAC;MACFG,UAAU;MACVC;IACF,CAAC,CAAC;EACJ,CAAC;EAED;EACA6B,QAAQ,EAAEA,CAAA,KAAM;IACd,MAAM;MAAE7B;IAAY,CAAC,GAAGH,GAAG,CAAC,CAAC;IAC7B,OAAOG,WAAW;EACpB,CAAC;EAED;EACA8B,YAAY,EAAEA,CAAA,KAAM;IAClB,MAAM;MAAE/B;IAAW,CAAC,GAAGF,GAAG,CAAC,CAAC;IAC5B,OAAOE,UAAU;EACnB;AACF,CAAC,CAAC,EACF;EACEgC,IAAI,EAAE,cAAc;EACpBC,UAAU,EAAGhB,KAAK,KAAM;IACtBlB,KAAK,EAAEkB,KAAK,CAAClB;EACf,CAAC,CAAC;EACFmC,kBAAkB,EAAEA,CAAA,KAAOjB,KAAK,IAAK;IACnC;IACA,IAAIA,KAAK,EAAE;MACTA,KAAK,CAACC,eAAe,CAAC,CAAC;IACzB;EACF;AACF,CACF,CACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}