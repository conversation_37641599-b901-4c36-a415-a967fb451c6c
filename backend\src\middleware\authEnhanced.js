const jwt = require('jsonwebtoken');
const crypto = require('crypto');
const { User, TokenBlacklist, RefreshToken } = require('../models');
const { Op } = require('sequelize');

// 生成JWT时包含jti
const generateAccessToken = (userId, additionalClaims = {}) => {
  const jti = crypto.randomBytes(16).toString('hex');
  const payload = {
    userId,
    jti,
    type: 'access',
    ...additionalClaims
  };
  
  return {
    token: jwt.sign(payload, process.env.JWT_SECRET, {
      expiresIn: process.env.JWT_EXPIRES_IN || '1h',
      algorithm: 'HS256'
    }),
    jti
  };
};

// 生成刷新令牌
const generateTokenPair = async (userId, deviceInfo = {}) => {
  // 生成访问令牌
  const { token: accessToken, jti } = generateAccessToken(userId);
  
  // 生成刷新令牌
  const { token: refreshToken, tokenRecord } = await RefreshToken.createToken(userId, deviceInfo);
  
  return {
    accessToken,
    refreshToken,
    accessTokenJti: jti,
    refreshTokenId: tokenRecord.id,
    expiresIn: 3600 // 1小时
  };
};

// 增强的JWT令牌验证（包含黑名单检查）
const authenticateToken = async (req, res, next) => {
  try {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];

    if (!token) {
      return res.status(401).json({
        success: false,
        message: '访问令牌缺失',
        code: 'TOKEN_MISSING'
      });
    }

    // 验证token格式
    if (!/^[A-Za-z0-9\-_.]+$/.test(token)) {
      return res.status(401).json({
        success: false,
        message: '无效的令牌格式',
        code: 'INVALID_TOKEN_FORMAT'
      });
    }

    // 解码并验证JWT
    let decoded;
    try {
      decoded = jwt.verify(token, process.env.JWT_SECRET, {
        algorithms: ['HS256'],
        maxAge: process.env.JWT_EXPIRES_IN || '1h'
      });
    } catch (jwtError) {
      if (jwtError.name === 'TokenExpiredError') {
        return res.status(401).json({
          success: false,
          message: '令牌已过期',
          code: 'TOKEN_EXPIRED'
        });
      }
      if (jwtError.name === 'JsonWebTokenError') {
        return res.status(401).json({
          success: false,
          message: '无效的令牌',
          code: 'INVALID_TOKEN'
        });
      }
      throw jwtError;
    }

    // 检查是否在黑名单中
    if (decoded.jti) {
      const isBlacklisted = await TokenBlacklist.isBlacklisted(decoded.jti);
      if (isBlacklisted) {
        return res.status(401).json({
          success: false,
          message: '令牌已被撤销',
          code: 'TOKEN_REVOKED'
        });
      }
    }

    // 查找用户并检查状态
    const user = await User.findByPk(decoded.userId, {
      attributes: ['id', 'username', 'email', 'phone', 'role', 'status', 'last_login_at']
    });
    
    if (!user) {
      return res.status(401).json({
        success: false,
        message: '用户不存在',
        code: 'USER_NOT_FOUND'
      });
    }

    if (user.status !== 'active') {
      // 将令牌加入黑名单
      if (decoded.jti) {
        await TokenBlacklist.create({
          token_jti: decoded.jti,
          user_id: user.id,
          token_type: 'access',
          revoked_reason: 'Account disabled',
          expires_at: new Date(decoded.exp * 1000),
          ip_address: req.ip,
          user_agent: req.headers['user-agent']
        });
      }
      
      return res.status(401).json({
        success: false,
        message: '账户已被禁用',
        code: 'ACCOUNT_DISABLED'
      });
    }

    // 检查令牌年龄，提示刷新
    const now = Math.floor(Date.now() / 1000);
    const tokenAge = now - decoded.iat;
    const maxAge = 60 * 60; // 1小时
    
    if (tokenAge > maxAge * 0.8) { // 80%时提示刷新
      res.set('X-Token-Refresh-Needed', 'true');
      res.set('X-Token-Expires-In', Math.floor((decoded.exp - now) / 60) + 'min');
    }

    // 设置请求上下文
    req.user = user;
    req.token = {
      jti: decoded.jti,
      type: decoded.type || 'access',
      iat: decoded.iat,
      exp: decoded.exp
    };
    
    // 异步更新最后活动时间（不阻塞请求）
    if (tokenAge > 300) { // 5分钟更新一次
      setImmediate(() => {
        user.update({ last_login_at: new Date() }).catch(console.error);
      });
    }
    
    next();
  } catch (error) {
    console.error('Authentication error:', error);
    return res.status(500).json({
      success: false,
      message: '服务器错误',
      code: 'SERVER_ERROR'
    });
  }
};

// 刷新令牌中间件
const refreshAccessToken = async (req, res, next) => {
  try {
    const { refreshToken } = req.body;
    
    if (!refreshToken) {
      return res.status(400).json({
        success: false,
        message: '刷新令牌缺失',
        code: 'REFRESH_TOKEN_MISSING'
      });
    }

    // 查找刷新令牌
    const hashedToken = crypto.createHash('sha256').update(refreshToken).digest('hex');
    const tokenRecord = await RefreshToken.findOne({
      where: {
        token: hashedToken,
        is_active: true,
        expires_at: {
          [Op.gt]: new Date()
        }
      },
      include: [{
        model: User,
        as: 'user',
        attributes: ['id', 'status']
      }]
    });

    if (!tokenRecord) {
      return res.status(401).json({
        success: false,
        message: '无效的刷新令牌',
        code: 'INVALID_REFRESH_TOKEN'
      });
    }

    // 检查用户状态
    if (tokenRecord.user.status !== 'active') {
      await tokenRecord.revoke();
      return res.status(401).json({
        success: false,
        message: '账户已被禁用',
        code: 'ACCOUNT_DISABLED'
      });
    }

    // 标记为已使用
    await tokenRecord.markAsUsed();

    // 生成新的访问令牌
    const { token: accessToken, jti } = generateAccessToken(tokenRecord.user_id);

    // 可选：旋转刷新令牌（更安全）
    let newRefreshToken = refreshToken;
    if (process.env.ROTATE_REFRESH_TOKENS === 'true') {
      // 撤销旧的刷新令牌
      await tokenRecord.revoke();
      
      // 生成新的刷新令牌
      const deviceInfo = {
        deviceId: tokenRecord.device_id,
        deviceName: tokenRecord.device_name,
        ipAddress: req.ip,
        userAgent: req.headers['user-agent']
      };
      
      const { token: rotatedRefreshToken } = await RefreshToken.createToken(
        tokenRecord.user_id,
        deviceInfo
      );
      
      newRefreshToken = rotatedRefreshToken;
    }

    res.json({
      success: true,
      data: {
        accessToken,
        refreshToken: newRefreshToken,
        expiresIn: 3600
      }
    });
  } catch (error) {
    console.error('Refresh token error:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误',
      code: 'SERVER_ERROR'
    });
  }
};

// 登出中间件（将令牌加入黑名单）
const logout = async (req, res, next) => {
  try {
    if (req.token && req.token.jti) {
      // 将访问令牌加入黑名单
      await TokenBlacklist.create({
        token_jti: req.token.jti,
        user_id: req.user.id,
        token_type: 'access',
        revoked_reason: 'User logout',
        expires_at: new Date(req.token.exp * 1000),
        ip_address: req.ip,
        user_agent: req.headers['user-agent']
      });
    }

    // 撤销刷新令牌（如果提供）
    const { refreshToken, logoutAllDevices } = req.body;
    
    if (logoutAllDevices) {
      // 撤销用户的所有刷新令牌
      await RefreshToken.revokeAllUserTokens(req.user.id);
    } else if (refreshToken) {
      // 撤销特定的刷新令牌
      const hashedToken = crypto.createHash('sha256').update(refreshToken).digest('hex');
      await RefreshToken.update(
        { is_active: false },
        {
          where: {
            token: hashedToken,
            user_id: req.user.id
          }
        }
      );
    }

    res.json({
      success: true,
      message: '登出成功'
    });
  } catch (error) {
    console.error('Logout error:', error);
    res.status(500).json({
      success: false,
      message: '登出失败',
      code: 'LOGOUT_FAILED'
    });
  }
};

// 获取用户活跃会话
const getActiveSessions = async (req, res) => {
  try {
    const devices = await RefreshToken.getUserDevices(req.user.id);
    
    res.json({
      success: true,
      data: {
        devices: devices.map(device => ({
          id: device.id,
          deviceId: device.device_id,
          deviceName: device.device_name || 'Unknown Device',
          ipAddress: device.ip_address,
          lastActive: device.last_used_at || device.created_at,
          createdAt: device.created_at
        }))
      }
    });
  } catch (error) {
    console.error('Get sessions error:', error);
    res.status(500).json({
      success: false,
      message: '获取会话列表失败'
    });
  }
};

// 撤销特定会话
const revokeSession = async (req, res) => {
  try {
    const { sessionId } = req.params;
    
    const result = await RefreshToken.update(
      { is_active: false },
      {
        where: {
          id: sessionId,
          user_id: req.user.id
        }
      }
    );

    if (result[0] === 0) {
      return res.status(404).json({
        success: false,
        message: '会话不存在'
      });
    }

    res.json({
      success: true,
      message: '会话已撤销'
    });
  } catch (error) {
    console.error('Revoke session error:', error);
    res.status(500).json({
      success: false,
      message: '撤销会话失败'
    });
  }
};

// 定期清理任务
const startCleanupJobs = () => {
  // 每小时清理一次过期的黑名单记录
  setInterval(async () => {
    try {
      const blacklistCount = await TokenBlacklist.cleanupExpired();
      const refreshCount = await RefreshToken.cleanupExpired();
      console.log(`Cleaned up ${blacklistCount} expired blacklist entries and ${refreshCount} expired refresh tokens`);
    } catch (error) {
      console.error('Cleanup job error:', error);
    }
  }, 60 * 60 * 1000); // 1小时
};

module.exports = {
  authenticateToken,
  refreshAccessToken,
  logout,
  getActiveSessions,
  revokeSession,
  generateTokenPair,
  generateAccessToken,
  startCleanupJobs,
  // 保持向后兼容
  authenticate: authenticateToken
};