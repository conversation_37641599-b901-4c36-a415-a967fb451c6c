#!/bin/bash

# 收书卖书平台 - 数据备份脚本
# 作者: DevOps Team
# 版本: 1.0

set -euo pipefail

# 配置变量
BACKUP_DIR="/backups"
DB_NAME="${DB_NAME:-booktrading}"
DB_USER="${DB_USER:-booktrading}"
DB_HOST="${DB_HOST:-postgres}"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
RETENTION_DAYS=30

# 日志函数
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "${BACKUP_DIR}/backup.log"
}

# 错误处理
error_exit() {
    log "ERROR: $1"
    exit 1
}

# 清理函数
cleanup() {
    log "Cleaning up old backups (older than ${RETENTION_DAYS} days)..."
    find "${BACKUP_DIR}" -name "*.sql.gz" -mtime +${RETENTION_DAYS} -delete
    find "${BACKUP_DIR}" -name "*.tar.gz" -mtime +${RETENTION_DAYS} -delete
    log "Cleanup completed"
}

# 创建备份目录
create_backup_dirs() {
    mkdir -p "${BACKUP_DIR}/database"
    mkdir -p "${BACKUP_DIR}/uploads"
    mkdir -p "${BACKUP_DIR}/logs"
}

# 数据库备份
backup_database() {
    log "Starting database backup..."
    
    local backup_file="${BACKUP_DIR}/database/${DB_NAME}_${TIMESTAMP}.sql"
    
    # 使用pg_dump进行备份
    if pg_dump -h "${DB_HOST}" -U "${DB_USER}" -d "${DB_NAME}" \
        --verbose --no-password --clean --if-exists \
        --format=custom --compress=9 > "${backup_file}.custom"; then
        
        # 同时创建SQL格式备份用于查看
        pg_dump -h "${DB_HOST}" -U "${DB_USER}" -d "${DB_NAME}" \
            --verbose --no-password --clean --if-exists > "${backup_file}"
        
        # 压缩SQL文件
        gzip "${backup_file}"
        
        log "Database backup completed: ${backup_file}.custom"
        log "SQL backup completed: ${backup_file}.gz"
        
        # 验证备份文件
        if [[ -f "${backup_file}.custom" ]] && [[ -s "${backup_file}.custom" ]]; then
            log "Database backup verification: SUCCESS"
        else
            error_exit "Database backup verification: FAILED"
        fi
    else
        error_exit "Database backup failed"
    fi
}

# 文件备份
backup_files() {
    log "Starting files backup..."
    
    local uploads_backup="${BACKUP_DIR}/uploads/uploads_${TIMESTAMP}.tar.gz"
    local logs_backup="${BACKUP_DIR}/logs/logs_${TIMESTAMP}.tar.gz"
    
    # 备份上传文件
    if [[ -d "/app/uploads" ]]; then
        tar -czf "${uploads_backup}" -C /app uploads/ 2>/dev/null || {
            log "WARNING: No uploads directory found or empty"
        }
        
        if [[ -f "${uploads_backup}" ]] && [[ -s "${uploads_backup}" ]]; then
            log "Uploads backup completed: ${uploads_backup}"
        fi
    fi
    
    # 备份日志文件
    if [[ -d "/app/logs" ]]; then
        tar -czf "${logs_backup}" -C /app logs/ 2>/dev/null || {
            log "WARNING: No logs directory found or empty"
        }
        
        if [[ -f "${logs_backup}" ]] && [[ -s "${logs_backup}" ]]; then
            log "Logs backup completed: ${logs_backup}"
        fi
    fi
}

# 创建备份清单
create_manifest() {
    log "Creating backup manifest..."
    
    local manifest_file="${BACKUP_DIR}/manifest_${TIMESTAMP}.txt"
    
    cat > "${manifest_file}" << EOF
Backup Manifest
===============
Date: $(date)
Database: ${DB_NAME}
Host: ${DB_HOST}
User: ${DB_USER}

Files included in this backup:
EOF
    
    find "${BACKUP_DIR}" -name "*_${TIMESTAMP}*" -type f >> "${manifest_file}"
    
    log "Backup manifest created: ${manifest_file}"
}

# 验证备份完整性
verify_backup() {
    log "Verifying backup integrity..."
    
    local database_backup="${BACKUP_DIR}/database/${DB_NAME}_${TIMESTAMP}.sql.custom"
    
    # 验证数据库备份
    if pg_restore --list "${database_backup}" > /dev/null 2>&1; then
        log "Database backup integrity: OK"
    else
        error_exit "Database backup integrity check failed"
    fi
    
    # 验证压缩文件
    for file in $(find "${BACKUP_DIR}" -name "*_${TIMESTAMP}.tar.gz" -o -name "*_${TIMESTAMP}.sql.gz"); do
        if gzip -t "${file}" 2>/dev/null; then
            log "File integrity OK: $(basename "${file}")"
        else
            log "WARNING: File integrity check failed: $(basename "${file}")"
        fi
    done
}

# 发送通知 (可选)
send_notification() {
    if [[ -n "${BACKUP_WEBHOOK_URL:-}" ]]; then
        local status="${1:-SUCCESS}"
        local message="Database backup ${status} at $(date)"
        
        curl -X POST "${BACKUP_WEBHOOK_URL}" \
            -H "Content-Type: application/json" \
            -d "{\"text\":\"${message}\"}" \
            --max-time 10 --silent || log "Failed to send notification"
    fi
}

# 主函数
main() {
    log "=== Starting backup process ==="
    
    # 检查必要的环境变量
    if [[ -z "${PGPASSWORD:-}" ]]; then
        error_exit "PGPASSWORD environment variable not set"
    fi
    
    # 创建备份目录
    create_backup_dirs
    
    # 执行备份
    backup_database
    backup_files
    
    # 创建清单
    create_manifest
    
    # 验证备份
    verify_backup
    
    # 清理旧备份
    cleanup
    
    # 计算备份大小
    local total_size=$(du -sh "${BACKUP_DIR}" | cut -f1)
    log "Total backup size: ${total_size}"
    
    log "=== Backup process completed successfully ==="
    
    # 发送成功通知
    send_notification "SUCCESS"
}

# 错误处理
trap 'error_exit "Backup process interrupted"' INT TERM
trap 'send_notification "FAILED"' ERR

# 运行主函数
main "$@"