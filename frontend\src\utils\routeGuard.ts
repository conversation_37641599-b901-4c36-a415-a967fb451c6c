import { User } from '../types';

export type UserRole = 'user' | 'admin' | 'super_admin';
export type Permission = 'read' | 'write' | 'delete' | 'admin' | 'super_admin';

// 权限配置
export const PERMISSIONS: Record<UserRole, Permission[]> = {
  user: ['read'],
  admin: ['read', 'write', 'admin'],
  super_admin: ['read', 'write', 'delete', 'admin', 'super_admin']
};

// 路由权限配置
export interface RoutePermission {
  path: string;
  requiredPermissions: Permission[];
  allowedRoles?: UserRole[];
  requireAuth?: boolean;
}

export const ROUTE_PERMISSIONS: RoutePermission[] = [
  // 公开路由
  { path: '/', requiredPermissions: [], requireAuth: false },
  { path: '/books', requiredPermissions: [], requireAuth: false },
  { path: '/books/:id', requiredPermissions: [], requireAuth: false },
  { path: '/auth', requiredPermissions: [], requireAuth: false },
  { path: '/login', requiredPermissions: [], requireAuth: false },
  { path: '/register', requiredPermissions: [], requireAuth: false },

  // 需要登录的用户路由
  { path: '/profile', requiredPermissions: ['read'], requireAuth: true },
  { path: '/orders', requiredPermissions: ['read'], requireAuth: true },
  { path: '/cart', requiredPermissions: ['read'], requireAuth: true },
  { path: '/checkout', requiredPermissions: ['read'], requireAuth: true },

  // 管理员路由
  { path: '/admin', requiredPermissions: ['admin'], allowedRoles: ['admin', 'super_admin'] },
  { path: '/admin/*', requiredPermissions: ['admin'], allowedRoles: ['admin', 'super_admin'] },

  // 超级管理员路由
  { path: '/super-admin', requiredPermissions: ['super_admin'], allowedRoles: ['super_admin'] },
  { path: '/super-admin/*', requiredPermissions: ['super_admin'], allowedRoles: ['super_admin'] }
];

/**
 * 检查用户是否有指定权限
 */
export function hasPermission(user: User | null, permission: Permission): boolean {
  if (!user) return false;
  
  const userPermissions = PERMISSIONS[user.role as UserRole] || [];
  return userPermissions.includes(permission);
}

/**
 * 检查用户是否有多个权限中的任意一个
 */
export function hasAnyPermission(user: User | null, permissions: Permission[]): boolean {
  return permissions.some(permission => hasPermission(user, permission));
}

/**
 * 检查用户是否有所有指定权限
 */
export function hasAllPermissions(user: User | null, permissions: Permission[]): boolean {
  return permissions.every(permission => hasPermission(user, permission));
}

/**
 * 检查用户角色
 */
export function hasRole(user: User | null, role: UserRole): boolean {
  return user?.role === role;
}

/**
 * 检查用户是否有任意一个指定角色
 */
export function hasAnyRole(user: User | null, roles: UserRole[]): boolean {
  if (!user) return false;
  return roles.includes(user.role as UserRole);
}

/**
 * 检查用户是否可以访问指定路由
 */
export function canAccessRoute(user: User | null, path: string): {
  canAccess: boolean;
  reason?: string;
} {
  // 查找匹配的路由权限配置
  const routePermission = ROUTE_PERMISSIONS.find(route => {
    if (route.path.includes('*')) {
      const basePath = route.path.replace('/*', '');
      return path.startsWith(basePath);
    }
    if (route.path.includes(':')) {
      const pattern = route.path.replace(/:[^/]+/g, '[^/]+');
      const regex = new RegExp(`^${pattern}$`);
      return regex.test(path);
    }
    return route.path === path;
  });

  if (!routePermission) {
    // 默认允许访问未配置的路由
    return { canAccess: true };
  }

  // 检查是否需要登录
  if (routePermission.requireAuth && !user) {
    return { canAccess: false, reason: 'login_required' };
  }

  // 检查角色权限
  if (routePermission.allowedRoles) {
    if (!user || !hasAnyRole(user, routePermission.allowedRoles)) {
      return { canAccess: false, reason: 'insufficient_role' };
    }
  }

  // 检查权限
  if (routePermission.requiredPermissions.length > 0) {
    if (!hasAllPermissions(user, routePermission.requiredPermissions)) {
      return { canAccess: false, reason: 'insufficient_permission' };
    }
  }

  return { canAccess: true };
}

/**
 * 获取用户可访问的菜单项
 */
export interface MenuItem {
  key: string;
  path: string;
  title: string;
  icon?: string;
  children?: MenuItem[];
  requiredPermissions?: Permission[];
  allowedRoles?: UserRole[];
}

export function getAccessibleMenuItems(user: User | null, menuItems: MenuItem[]): MenuItem[] {
  return menuItems.filter(item => {
    // 检查角色权限
    if (item.allowedRoles && (!user || !hasAnyRole(user, item.allowedRoles))) {
      return false;
    }

    // 检查权限
    if (item.requiredPermissions && !hasAllPermissions(user, item.requiredPermissions)) {
      return false;
    }

    // 递归过滤子菜单
    if (item.children) {
      item.children = getAccessibleMenuItems(user, item.children);
    }

    return true;
  });
}

/**
 * 路由重定向逻辑
 */
export function getRedirectPath(user: User | null, attemptedPath: string): string {
  const { canAccess, reason } = canAccessRoute(user, attemptedPath);

  if (canAccess) {
    return attemptedPath;
  }

  switch (reason) {
    case 'login_required':
      return `/login?redirect=${encodeURIComponent(attemptedPath)}`;
    case 'insufficient_role':
    case 'insufficient_permission':
      return user ? '/403' : '/login';
    default:
      return '/';
  }
}

/**
 * 权限验证装饰器（用于组件）
 */
export function withPermission<P extends object>(
  Component: React.ComponentType<P>,
  requiredPermissions: Permission[],
  fallbackComponent?: React.ComponentType<P>
) {
  return function PermissionWrappedComponent(props: P) {
    // 这里需要从context或props获取用户信息
    // 实际实现中需要配合useAuthContext使用
    const user = null; // 从context获取
    
    if (!hasAllPermissions(user, requiredPermissions)) {
      return fallbackComponent ? React.createElement(fallbackComponent, props) : null;
    }

    return React.createElement(Component, props);
  };
}

/**
 * 角色验证装饰器
 */
export function withRole<P extends object>(
  Component: React.ComponentType<P>,
  allowedRoles: UserRole[],
  fallbackComponent?: React.ComponentType<P>
) {
  return function RoleWrappedComponent(props: P) {
    const user = null; // 从context获取
    
    if (!hasAnyRole(user, allowedRoles)) {
      return fallbackComponent ? React.createElement(fallbackComponent, props) : null;
    }

    return React.createElement(Component, props);
  };
}