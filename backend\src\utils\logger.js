const winston = require('winston');
const path = require('path');
const fs = require('fs');
const DailyRotateFile = require('winston-daily-rotate-file');

// 确保日志目录存在
const logDir = process.env.LOG_DIR || path.join(__dirname, '../../logs');
if (!fs.existsSync(logDir)) {
  fs.mkdirSync(logDir, { recursive: true });
}

// 自定义日志格式
const logFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss.SSS' }),
  winston.format.errors({ stack: true }),
  winston.format.splat(),
  winston.format.json()
);

// 控制台输出格式（开发环境）
const consoleFormat = winston.format.combine(
  winston.format.colorize(),
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
  winston.format.printf(({ timestamp, level, message, ...metadata }) => {
    let msg = `${timestamp} [${level}]: ${message}`;
    if (Object.keys(metadata).length > 0) {
      msg += ` ${JSON.stringify(metadata)}`;
    }
    return msg;
  })
);

// 创建日志传输器
const transports = [];

// 控制台传输（所有环境）
transports.push(
  new winston.transports.Console({
    format: process.env.NODE_ENV === 'development' ? consoleFormat : logFormat,
    level: process.env.LOG_LEVEL || 'info'
  })
);

// 文件传输（生产环境）
if (process.env.NODE_ENV === 'production') {
  // 错误日志
  transports.push(
    new DailyRotateFile({
      filename: path.join(logDir, 'error-%DATE%.log'),
      datePattern: 'YYYY-MM-DD',
      level: 'error',
      maxSize: process.env.LOG_MAX_SIZE || '10m',
      maxFiles: process.env.LOG_MAX_FILES || '7d',
      format: logFormat
    })
  );

  // 组合日志
  transports.push(
    new DailyRotateFile({
      filename: path.join(logDir, 'combined-%DATE%.log'),
      datePattern: 'YYYY-MM-DD',
      maxSize: process.env.LOG_MAX_SIZE || '10m',
      maxFiles: process.env.LOG_MAX_FILES || '7d',
      format: logFormat
    })
  );

  // 访问日志
  transports.push(
    new DailyRotateFile({
      filename: path.join(logDir, 'access-%DATE%.log'),
      datePattern: 'YYYY-MM-DD',
      level: 'http',
      maxSize: process.env.LOG_MAX_SIZE || '10m',
      maxFiles: process.env.LOG_MAX_FILES || '7d',
      format: logFormat
    })
  );
}

// 创建logger实例
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: logFormat,
  transports: transports,
  exitOnError: false
});

// HTTP请求日志流（用于morgan）
logger.stream = {
  write: (message) => {
    logger.http(message.trim());
  }
};

// 扩展logger功能
class Logger {
  constructor() {
    this.winston = logger;
  }

  // 基础日志方法
  error(message, meta = {}) {
    logger.error(message, this._enrichMeta(meta));
  }

  warn(message, meta = {}) {
    logger.warn(message, this._enrichMeta(meta));
  }

  info(message, meta = {}) {
    logger.info(message, this._enrichMeta(meta));
  }

  http(message, meta = {}) {
    logger.http(message, this._enrichMeta(meta));
  }

  debug(message, meta = {}) {
    logger.debug(message, this._enrichMeta(meta));
  }

  // 错误日志（带堆栈）
  logError(error, context = {}) {
    const errorInfo = {
      message: error.message,
      stack: error.stack,
      code: error.code,
      statusCode: error.statusCode,
      ...context
    };

    logger.error('Application Error', errorInfo);
  }

  // API错误日志
  logApiError(req, error, additionalInfo = {}) {
    const errorInfo = {
      method: req.method,
      url: req.originalUrl,
      ip: req.ip,
      userId: req.user?.id,
      userAgent: req.get('user-agent'),
      error: {
        message: error.message,
        stack: error.stack,
        code: error.code
      },
      ...additionalInfo
    };

    logger.error('API Error', errorInfo);
  }

  // 访问日志
  logAccess(req, res, responseTime) {
    const accessInfo = {
      method: req.method,
      url: req.originalUrl,
      ip: req.ip,
      userId: req.user?.id,
      statusCode: res.statusCode,
      responseTime: `${responseTime}ms`,
      userAgent: req.get('user-agent'),
      referer: req.get('referer')
    };

    logger.http('API Access', accessInfo);
  }

  // 数据库查询日志
  logQuery(query, duration, success = true) {
    const queryInfo = {
      query: query.sql || query,
      duration: `${duration}ms`,
      success
    };

    if (duration > 1000) {
      logger.warn('Slow Query', queryInfo);
    } else {
      logger.debug('Database Query', queryInfo);
    }
  }

  // 安全事件日志
  logSecurityEvent(event, details = {}) {
    const securityInfo = {
      event,
      timestamp: new Date().toISOString(),
      ...details
    };

    logger.warn('Security Event', securityInfo);
  }

  // 业务事件日志
  logBusinessEvent(event, details = {}) {
    const eventInfo = {
      event,
      timestamp: new Date().toISOString(),
      ...details
    };

    logger.info('Business Event', eventInfo);
  }

  // 性能日志
  logPerformance(operation, duration, metadata = {}) {
    const perfInfo = {
      operation,
      duration: `${duration}ms`,
      ...metadata
    };

    if (duration > 3000) {
      logger.warn('Performance Issue', perfInfo);
    } else {
      logger.debug('Performance Metric', perfInfo);
    }
  }

  // 审计日志
  logAudit(action, userId, details = {}) {
    const auditInfo = {
      action,
      userId,
      timestamp: new Date().toISOString(),
      ...details
    };

    logger.info('Audit Log', auditInfo);
  }

  // 定时任务日志
  logCronJob(jobName, status, details = {}) {
    const cronInfo = {
      jobName,
      status,
      timestamp: new Date().toISOString(),
      ...details
    };

    logger.info('Cron Job', cronInfo);
  }

  // 第三方服务日志
  logExternalService(service, operation, success, details = {}) {
    const serviceInfo = {
      service,
      operation,
      success,
      timestamp: new Date().toISOString(),
      ...details
    };

    if (!success) {
      logger.error('External Service Error', serviceInfo);
    } else {
      logger.info('External Service Call', serviceInfo);
    }
  }

  // 私有方法：丰富元数据
  _enrichMeta(meta) {
    return {
      pid: process.pid,
      hostname: require('os').hostname(),
      environment: process.env.NODE_ENV,
      ...meta
    };
  }

  // 获取日志文件列表
  async getLogFiles() {
    return new Promise((resolve, reject) => {
      fs.readdir(logDir, (err, files) => {
        if (err) {
          reject(err);
        } else {
          const logFiles = files
            .filter(file => file.endsWith('.log'))
            .map(file => ({
              name: file,
              path: path.join(logDir, file),
              size: fs.statSync(path.join(logDir, file)).size
            }));
          resolve(logFiles);
        }
      });
    });
  }

  // 读取日志文件内容
  async readLogFile(filename, lines = 100) {
    const filePath = path.join(logDir, filename);
    
    return new Promise((resolve, reject) => {
      const stream = fs.createReadStream(filePath, { encoding: 'utf8' });
      const chunks = [];
      let lineCount = 0;
      
      stream.on('data', (chunk) => {
        chunks.push(chunk);
        lineCount += chunk.split('\n').length - 1;
        
        if (lineCount >= lines) {
          stream.destroy();
        }
      });
      
      stream.on('end', () => {
        const content = chunks.join('');
        const logLines = content.split('\n').slice(-lines).filter(line => line.trim());
        resolve(logLines);
      });
      
      stream.on('error', reject);
    });
  }

  // 清理旧日志
  async cleanOldLogs(daysToKeep = 30) {
    const cutoffTime = Date.now() - (daysToKeep * 24 * 60 * 60 * 1000);
    
    try {
      const files = await this.getLogFiles();
      let deletedCount = 0;
      
      for (const file of files) {
        const stats = fs.statSync(file.path);
        if (stats.mtime.getTime() < cutoffTime) {
          fs.unlinkSync(file.path);
          deletedCount++;
        }
      }
      
      this.info(`Cleaned ${deletedCount} old log files`);
      return deletedCount;
    } catch (error) {
      this.error('Error cleaning old logs', { error: error.message });
      throw error;
    }
  }
}

// 导出单例
module.exports = new Logger();