# 紧急修复指南

## 立即执行的修复步骤

### 第1步：修复数据库安全问题（10分钟）

1. **创建环境变量文件**
```bash
# 复制环境变量模板
copy .env.example .env

# 生成安全密钥
node -e "console.log(require('crypto').randomBytes(32).toString('base64'))"
```

2. **更新数据库配置**
编辑 `backend/src/config/database.js`：
```javascript
// 将硬编码的密码改为环境变量
password: process.env.DB_PASSWORD || 'postgres',
```

3. **修改docker-compose.yml**
```yaml
# 移除数据库端口暴露
postgres:
  # ports:
  #   - "5432:5432"  # 注释掉这行
```

### 第2步：修复前端内存泄漏（15分钟）

1. **创建安全的useState Hook**
创建文件 `frontend/src/hooks/useSafeState.ts`：
```typescript
import { useCallback, useEffect, useRef, useState } from 'react';

export function useSafeState<T>(initialState: T) {
  const [state, setState] = useState<T>(initialState);
  const mountedRef = useRef(true);

  useEffect(() => {
    return () => {
      mountedRef.current = false;
    };
  }, []);

  const setSafeState = useCallback((value: T | ((prev: T) => T)) => {
    if (mountedRef.current) {
      setState(value);
    }
  }, []);

  return [state, setSafeState] as const;
}
```

2. **修复useEffect清理问题**
在所有使用useEffect的组件中添加清理函数：
```typescript
useEffect(() => {
  const timer = setTimeout(() => {
    // 异步操作
  }, 1000);

  // 清理函数
  return () => {
    clearTimeout(timer);
  };
}, []);
```

### 第3步：修复文件上传安全（10分钟）

更新 `backend/src/middleware/upload.js`：
```javascript
const path = require('path');
const crypto = require('crypto');

// 安全的文件名生成
const filename = (req, file, cb) => {
  const ext = path.extname(file.originalname);
  const name = crypto.randomBytes(16).toString('hex');
  cb(null, `${name}${ext}`);
};

// 文件类型验证
const fileFilter = (req, file, cb) => {
  const allowedTypes = /jpeg|jpg|png|gif|webp/;
  const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
  const mimetype = allowedTypes.test(file.mimetype);
  
  if (extname && mimetype) {
    cb(null, true);
  } else {
    cb(new Error('只允许上传图片文件'));
  }
};
```

### 第4步：修复N+1查询问题（20分钟）

1. **优化图书列表查询**
修改 `backend/src/routes/books.js`：
```javascript
// 添加include避免N+1
const books = await Book.findAll({
  include: [
    {
      model: Category,
      as: 'category',
      attributes: ['id', 'name']
    },
    {
      model: User,
      as: 'creator',
      attributes: ['id', 'username']
    }
  ],
  // 添加索引提示
  order: [['created_at', 'DESC']],
  limit: 20
});
```

2. **添加数据库索引**
创建迁移文件添加索引：
```sql
-- 复合索引优化查询
CREATE INDEX idx_books_category_status ON books(category_id, status);
CREATE INDEX idx_books_created_at ON books(created_at DESC);
CREATE INDEX idx_orders_user_status ON orders(user_id, status);
```

### 第5步：启用基本缓存（15分钟）

1. **安装Redis**
```bash
docker run -d --name redis -p 6379:6379 redis:alpine
```

2. **添加缓存中间件**
创建 `backend/src/middleware/cache.js`：
```javascript
const redis = require('redis');
const client = redis.createClient({
  host: process.env.REDIS_HOST || 'localhost',
  port: process.env.REDIS_PORT || 6379
});

exports.cache = (key, ttl = 300) => {
  return async (req, res, next) => {
    try {
      const cached = await client.get(key);
      if (cached) {
        return res.json(JSON.parse(cached));
      }
      
      // 重写res.json以缓存响应
      const originalJson = res.json;
      res.json = function(data) {
        client.setex(key, ttl, JSON.stringify(data));
        originalJson.call(this, data);
      };
      
      next();
    } catch (err) {
      next();
    }
  };
};
```

### 第6步：添加错误监控（10分钟）

1. **增强错误边界**
更新 `frontend/src/components/ui/ErrorBoundary.tsx`：
```typescript
class ErrorBoundary extends React.Component {
  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    // 记录错误到控制台
    console.error('ErrorBoundary caught:', error, errorInfo);
    
    // 发送错误到监控服务
    if (process.env.NODE_ENV === 'production') {
      // 发送到Sentry或其他监控服务
      this.logErrorToService(error, errorInfo);
    }
  }
  
  logErrorToService(error: Error, errorInfo: React.ErrorInfo) {
    // 实现错误上报逻辑
  }
}
```

## 验证修复效果

### 1. 安全性验证
```bash
# 检查环境变量是否生效
echo $DB_PASSWORD

# 测试数据库端口是否关闭
telnet localhost 5432  # 应该连接失败
```

### 2. 性能验证
```bash
# 监控API响应时间
curl -w "@curl-format.txt" -o /dev/null -s http://localhost:3001/api/books

# 检查Redis缓存
redis-cli
> keys *
> ttl <key>
```

### 3. 内存泄漏验证
```javascript
// 在浏览器控制台运行
performance.memory.usedJSHeapSize

// 操作应用后再次检查
performance.memory.usedJSHeapSize
```

## 后续优化计划

### 本周完成
- [ ] 完善JWT黑名单机制
- [ ] 实现完整的输入验证
- [ ] 添加API限流

### 下周完成
- [ ] 配置自动备份
- [ ] 实现日志聚合
- [ ] 添加性能监控

### 本月完成
- [ ] 编写单元测试
- [ ] 实现CI/CD流程
- [ ] 完善文档

## 需要帮助？

如果在修复过程中遇到问题：
1. 查看详细的项目深度检测报告
2. 参考各个代理生成的专业建议
3. 逐步实施，不要一次性修改太多

记住：**安全第一，性能第二，功能第三**