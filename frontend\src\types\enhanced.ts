/**
 * 增强的类型定义文件
 * 用于替代项目中大量的any类型，提供严格的类型安全
 */

// ==================== API响应类型 ====================

/**
 * 通用API响应包装器
 */
export interface ApiResponse<T = any> {
  success: boolean;
  message?: string;
  data?: T;
  error?: ApiError;
  timestamp?: string;
  requestId?: string;
}

/**
 * API错误类型
 */
export interface ApiError {
  code: string;
  message: string;
  details?: Record<string, any>;
  stack?: string;
}

/**
 * 分页响应
 */
export interface PaginationResponse<T> extends ApiResponse<{
  items: T[];
  pagination: PaginationMeta;
}> {}

/**
 * 分页元数据
 */
export interface PaginationMeta {
  currentPage: number;
  totalPages: number;
  totalItems: number;
  itemsPerPage: number;
  hasNext: boolean;
  hasPrevious: boolean;
}

// ==================== 用户相关类型 ====================

/**
 * 用户实体（完整定义）
 */
export interface User {
  id: string;
  username: string;
  email: string;
  phone: string;
  avatar?: string;
  role: UserRole;
  status: UserStatus;
  lastLoginAt?: Date;
  createdAt: Date;
  updatedAt: Date;
  profile?: UserProfile;
}

/**
 * 用户角色枚举
 */
export enum UserRole {
  USER = 'user',
  ADMIN = 'admin',
  SUPER_ADMIN = 'super_admin'
}

/**
 * 用户状态枚举
 */
export enum UserStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  BANNED = 'banned',
  DELETED = 'deleted'
}

/**
 * 用户档案信息
 */
export interface UserProfile {
  realName?: string;
  gender?: 'male' | 'female' | 'other';
  birthDate?: Date;
  address?: string;
  bio?: string;
  preferences?: UserPreferences;
}

/**
 * 用户偏好设置
 */
export interface UserPreferences {
  notifications: NotificationSettings;
  privacy: PrivacySettings;
  display: DisplaySettings;
}

// ==================== 图书相关类型 ====================

/**
 * 图书实体（完整定义）
 */
export interface Book {
  id: string;
  title: string;
  author: string;
  isbn?: string;
  categoryId: string;
  category?: Category;
  description?: string;
  coverImage?: string;
  images?: string[];
  price: number;
  originalPrice?: number;
  stock: number;
  condition: BookCondition;
  status: BookStatus;
  publishYear?: number;
  publisher?: string;
  pages?: number;
  language?: string;
  tags?: string[];
  views: number;
  salesCount: number;
  rating?: number;
  reviewCount?: number;
  createdBy: string;
  creator?: User;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * 图书状态枚举
 */
export enum BookCondition {
  NEW = '全新',
  LIKE_NEW = '九成新',
  VERY_GOOD = '八成新',
  GOOD = '七成新',
  ACCEPTABLE = '六成新'
}

/**
 * 图书上架状态枚举
 */
export enum BookStatus {
  ON_SHELF = '上架',
  OFF_SHELF = '下架',
  OUT_OF_STOCK = '缺货',
  PRE_SALE = '预售'
}

/**
 * 分类实体
 */
export interface Category {
  id: string;
  name: string;
  description?: string;
  parentId?: string;
  parent?: Category;
  children?: Category[];
  icon?: string;
  sortOrder: number;
  bookCount?: number;
  createdAt: Date;
  updatedAt: Date;
}

// ==================== 订单相关类型 ====================

/**
 * 订单实体
 */
export interface Order {
  id: string;
  orderNumber: string;
  userId: string;
  user?: User;
  items: OrderItem[];
  totalAmount: number;
  paymentAmount?: number;
  discountAmount?: number;
  deliveryFee?: number;
  status: OrderStatus;
  paymentMethod?: PaymentMethod;
  paymentStatus: PaymentStatus;
  paymentTime?: Date;
  deliveryAddress: string;
  deliveryPhone: string;
  deliveryName?: string;
  deliveryTime?: Date;
  deliveryCompany?: string;
  deliveryTrackingNumber?: string;
  notes?: string;
  cancelReason?: string;
  cancelTime?: Date;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * 订单项
 */
export interface OrderItem {
  id: string;
  orderId: string;
  bookId: string;
  book?: Book;
  quantity: number;
  price: number;
  subtotal: number;
  discount?: number;
  finalPrice: number;
}

/**
 * 订单状态枚举
 */
export enum OrderStatus {
  PENDING = 'pending',
  PAID = 'paid',
  PROCESSING = 'processing',
  DELIVERING = 'delivering',
  DELIVERED = 'delivered',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
  REFUNDED = 'refunded'
}

/**
 * 支付状态枚举
 */
export enum PaymentStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  SUCCESS = 'success',
  FAILED = 'failed',
  CANCELLED = 'cancelled',
  REFUNDED = 'refunded'
}

/**
 * 支付方式枚举
 */
export enum PaymentMethod {
  ALIPAY = 'alipay',
  WECHAT = 'wechat',
  BANK_CARD = 'bank_card',
  BALANCE = 'balance'
}

// ==================== 购物车类型 ====================

/**
 * 购物车项
 */
export interface CartItem {
  bookId: string;
  book: Book;
  quantity: number;
  selected?: boolean;
  addedAt?: Date;
}

/**
 * 购物车状态
 */
export interface CartState {
  items: CartItem[];
  totalItems: number;
  totalAmount: number;
  selectedAmount: number;
  lastUpdated?: Date;
}

// ==================== 表单类型 ====================

/**
 * 登录表单
 */
export interface LoginForm {
  phone: string;
  password: string;
  rememberMe?: boolean;
}

/**
 * 注册表单
 */
export interface RegisterForm {
  phone: string;
  password: string;
  confirmPassword: string;
  username?: string;
  email?: string;
  agreeTerms: boolean;
}

/**
 * 图书表单
 */
export interface BookForm {
  title: string;
  author: string;
  isbn?: string;
  categoryId: string;
  description?: string;
  coverImage?: File | string;
  images?: (File | string)[];
  price: number;
  originalPrice?: number;
  stock: number;
  condition: BookCondition;
  status: BookStatus;
  publishYear?: number;
  publisher?: string;
  pages?: number;
  language?: string;
  tags?: string[];
}

/**
 * 订单表单
 */
export interface OrderForm {
  items: Array<{
    bookId: string;
    quantity: number;
  }>;
  deliveryAddress: string;
  deliveryPhone: string;
  deliveryName?: string;
  paymentMethod: PaymentMethod;
  notes?: string;
  couponCode?: string;
}

// ==================== 搜索和筛选类型 ====================

/**
 * 图书搜索参数
 */
export interface BookSearchParams {
  keyword?: string;
  categoryId?: string;
  author?: string;
  publisher?: string;
  minPrice?: number;
  maxPrice?: number;
  condition?: BookCondition;
  status?: BookStatus;
  tags?: string[];
  sortBy?: BookSortField;
  sortOrder?: 'ASC' | 'DESC';
  page?: number;
  limit?: number;
}

/**
 * 图书排序字段
 */
export type BookSortField = 
  | 'createdAt' 
  | 'price' 
  | 'salesCount' 
  | 'views' 
  | 'rating';

// ==================== 通知和消息类型 ====================

/**
 * 通知设置
 */
export interface NotificationSettings {
  email: boolean;
  sms: boolean;
  push: boolean;
  orderUpdates: boolean;
  promotions: boolean;
  newArrivals: boolean;
}

/**
 * 隐私设置
 */
export interface PrivacySettings {
  profileVisibility: 'public' | 'friends' | 'private';
  showPurchaseHistory: boolean;
  showFavorites: boolean;
  allowRecommendations: boolean;
}

/**
 * 显示设置
 */
export interface DisplaySettings {
  theme: 'light' | 'dark' | 'auto';
  language: string;
  itemsPerPage: number;
  defaultView: 'grid' | 'list';
}

// ==================== 评价和收藏类型 ====================

/**
 * 评价实体
 */
export interface Review {
  id: string;
  userId: string;
  user?: User;
  bookId: string;
  book?: Book;
  orderId?: string;
  rating: number;
  comment?: string;
  images?: string[];
  likes: number;
  helpful: number;
  verified: boolean;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * 收藏实体
 */
export interface Favorite {
  id: string;
  userId: string;
  bookId: string;
  book?: Book;
  createdAt: Date;
}

// ==================== 统计和分析类型 ====================

/**
 * 销售统计
 */
export interface SalesStats {
  date: string;
  revenue: number;
  orderCount: number;
  booksSold: number;
  averageOrderValue: number;
}

/**
 * 用户统计
 */
export interface UserStats {
  totalUsers: number;
  activeUsers: number;
  newUsers: number;
  userGrowthRate: number;
}

/**
 * 图书统计
 */
export interface BookStats {
  totalBooks: number;
  activeListings: number;
  outOfStock: number;
  averagePrice: number;
  topCategories: Array<{
    category: Category;
    count: number;
    percentage: number;
  }>;
}

// ==================== 工具类型 ====================

/**
 * 可为空类型
 */
export type Nullable<T> = T | null;

/**
 * 可选类型
 */
export type Optional<T> = T | undefined;

/**
 * 部分可选类型
 */
export type PartialBy<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;

/**
 * 深度部分类型
 */
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

/**
 * 只读深度类型
 */
export type DeepReadonly<T> = {
  readonly [P in keyof T]: T[P] extends object ? DeepReadonly<T[P]> : T[P];
};

/**
 * 异步函数返回类型
 */
export type AsyncReturnType<T extends (...args: any[]) => Promise<any>> =
  T extends (...args: any[]) => Promise<infer R> ? R : never;

/**
 * 提取Promise类型
 */
export type UnwrapPromise<T> = T extends Promise<infer U> ? U : T;

// ==================== React组件Props类型 ====================

/**
 * 通用列表组件Props
 */
export interface ListProps<T> {
  items: T[];
  loading?: boolean;
  error?: Error | null;
  emptyText?: string;
  renderItem: (item: T, index: number) => React.ReactNode;
  keyExtractor?: (item: T, index: number) => string;
  onRefresh?: () => void;
  onLoadMore?: () => void;
  hasMore?: boolean;
}

/**
 * 通用表单组件Props
 */
export interface FormProps<T> {
  initialValues?: Partial<T>;
  onSubmit: (values: T) => void | Promise<void>;
  onCancel?: () => void;
  loading?: boolean;
  submitText?: string;
  cancelText?: string;
  validation?: FormValidation<T>;
}

/**
 * 表单验证规则
 */
export type FormValidation<T> = {
  [K in keyof T]?: ValidationRule[];
};

/**
 * 验证规则
 */
export interface ValidationRule {
  required?: boolean;
  pattern?: RegExp;
  min?: number;
  max?: number;
  minLength?: number;
  maxLength?: number;
  validator?: (value: any) => boolean | string;
  message?: string;
}

// 导出所有类型
export * from './index';