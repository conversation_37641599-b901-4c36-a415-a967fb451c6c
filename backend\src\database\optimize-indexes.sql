-- 性能优化索引
-- 这些索引将显著改善N+1查询问题和整体查询性能

-- ==========================================
-- 图书表索引
-- ==========================================

-- 复合索引：状态+创建时间（用于列表查询）
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_books_status_created 
ON books(status, created_at DESC) 
WHERE status = '上架';

-- 复合索引：分类+状态（用于分类筛选）
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_books_category_status 
ON books(category_id, status) 
WHERE status = '上架';

-- 复合索引：价格范围查询
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_books_price_status 
ON books(price, status) 
WHERE status = '上架';

-- 全文搜索索引（PostgreSQL特有）
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_books_fulltext_search 
ON books USING gin(to_tsvector('simple', 
    COALESCE(title, '') || ' ' || 
    COALESCE(author, '') || ' ' || 
    COALESCE(publisher, '')
));

-- 热门图书索引
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_books_popularity 
ON books(sales_count DESC, views DESC) 
WHERE status = '上架';

-- 库存查询索引
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_books_stock 
ON books(stock) 
WHERE status = '上架' AND stock > 0;

-- ==========================================
-- 订单表索引
-- ==========================================

-- 用户订单查询索引
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_orders_user_created 
ON orders(user_id, created_at DESC);

-- 订单状态索引
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_orders_status 
ON orders(status) 
WHERE status IN ('pending', 'paid', 'delivering');

-- 支付状态索引
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_orders_payment_status 
ON orders(payment_status) 
WHERE payment_status = 'pending';

-- 日期范围查询索引
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_orders_date_range 
ON orders(created_at DESC);

-- ==========================================
-- 订单项表索引
-- ==========================================

-- 订单项查询索引
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_order_items_order_book 
ON order_items(order_id, book_id);

-- 图书销售统计索引
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_order_items_book_stats 
ON order_items(book_id);

-- ==========================================
-- 用户表索引
-- ==========================================

-- 手机号索引（唯一）
CREATE UNIQUE INDEX CONCURRENTLY IF NOT EXISTS idx_users_phone 
ON users(phone);

-- 用户状态索引
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_status 
ON users(status) 
WHERE status = 'active';

-- 用户角色索引
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_role 
ON users(role) 
WHERE role IN ('admin', 'super_admin');

-- ==========================================
-- 消息表索引
-- ==========================================

-- 发送者接收者复合索引
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_messages_sender_receiver 
ON messages(sender_id, receiver_id, created_at DESC);

-- 未读消息索引
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_messages_unread 
ON messages(receiver_id, read_status) 
WHERE read_status = false;

-- 消息类型索引
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_messages_type 
ON messages(type, created_at DESC);

-- ==========================================
-- 收藏表索引
-- ==========================================

-- 用户收藏索引
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_favorites_user_book 
ON favorites(user_id, book_id);

-- 图书收藏数统计索引
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_favorites_book_count 
ON favorites(book_id);

-- ==========================================
-- 评论表索引
-- ==========================================

-- 图书评论索引
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_reviews_book_created 
ON reviews(book_id, created_at DESC) 
WHERE is_deleted = false;

-- 用户评论索引
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_reviews_user 
ON reviews(user_id) 
WHERE is_deleted = false;

-- 评分索引（用于统计）
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_reviews_rating 
ON reviews(book_id, rating) 
WHERE is_deleted = false;

-- ==========================================
-- 分类表索引
-- ==========================================

-- 分类排序索引
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_categories_sort 
ON categories(sort_order, name) 
WHERE is_active = true;

-- 父分类索引
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_categories_parent 
ON categories(parent_id) 
WHERE is_active = true;

-- ==========================================
-- 通知表索引
-- ==========================================

-- 用户通知索引
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_notifications_user_read 
ON notifications(user_id, is_read, created_at DESC);

-- 未读通知索引
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_notifications_unread 
ON notifications(user_id) 
WHERE is_read = false;

-- ==========================================
-- 上传文件表索引（如果存在）
-- ==========================================

-- 文件类型索引
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_uploaded_files_type_user 
ON uploaded_files(type, uploaded_by);

-- 文件引用索引
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_uploaded_files_referenced 
ON uploaded_files(referenced) 
WHERE referenced = false;

-- ==========================================
-- 分析和维护
-- ==========================================

-- 更新表统计信息
ANALYZE books;
ANALYZE orders;
ANALYZE order_items;
ANALYZE users;
ANALYZE messages;
ANALYZE favorites;
ANALYZE reviews;
ANALYZE categories;
ANALYZE notifications;

-- 创建自定义统计信息（PostgreSQL 10+）
CREATE STATISTICS IF NOT EXISTS books_status_category 
ON status, category_id FROM books;

CREATE STATISTICS IF NOT EXISTS orders_user_status 
ON user_id, status FROM orders;

-- ==========================================
-- 查询优化建议
-- ==========================================

-- 1. 对于频繁的JOIN操作，考虑使用物化视图
-- CREATE MATERIALIZED VIEW mv_books_with_category AS
-- SELECT b.*, c.name as category_name
-- FROM books b
-- LEFT JOIN categories c ON b.category_id = c.id
-- WHERE b.status = '上架';

-- 2. 对于热数据，考虑使用部分索引
-- CREATE INDEX idx_recent_orders ON orders(created_at DESC)
-- WHERE created_at > CURRENT_DATE - INTERVAL '30 days';

-- 3. 监控慢查询
-- 在postgresql.conf中设置：
-- log_min_duration_statement = 100  # 记录超过100ms的查询
-- log_line_prefix = '%t [%p]: [%l-1] user=%u,db=%d,app=%a,client=%h '

-- ==========================================
-- 定期维护脚本
-- ==========================================

-- 创建维护函数
CREATE OR REPLACE FUNCTION maintain_indexes() RETURNS void AS $$
BEGIN
    -- 重建膨胀的索引
    REINDEX TABLE CONCURRENTLY books;
    REINDEX TABLE CONCURRENTLY orders;
    REINDEX TABLE CONCURRENTLY order_items;
    
    -- 更新统计信息
    ANALYZE;
    
    -- 清理死元组
    VACUUM ANALYZE;
END;
$$ LANGUAGE plpgsql;

-- 建议定期（每周）执行：
-- SELECT maintain_indexes();