{"ast": null, "code": "var _jsxFileName = \"D:\\\\claude\\u955C\\u50CF\\\\\\u6536\\u4E66\\u5356\\u4E66\\\\frontend\\\\src\\\\components\\\\business\\\\FavoriteButton.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Button, message, Tooltip } from 'antd';\nimport { HeartOutlined, HeartFilled } from '@ant-design/icons';\nimport { favoritesService } from '../../services/favorites';\nimport { useAuthStore } from '../../stores/authStore';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst FavoriteButton = ({\n  bookId,\n  size = 'middle',\n  type = 'default',\n  showText = false,\n  className,\n  style,\n  onFavoriteChange\n}) => {\n  _s();\n  const {\n    isAuthenticated\n  } = useAuthStore();\n  const [isFavorited, setIsFavorited] = useState(false);\n  const [loading, setLoading] = useState(false);\n  const [checking, setChecking] = useState(true);\n\n  // 检查收藏状态\n  useEffect(() => {\n    let mounted = true;\n    if (isAuthenticated && bookId) {\n      checkFavoriteStatus();\n    } else {\n      setChecking(false);\n    }\n    return () => {\n      mounted = false;\n    };\n    async function checkFavoriteStatus() {\n      try {\n        setChecking(true);\n        const response = await favoritesService.checkFavorite(bookId);\n        if (mounted && response.success && response.data) {\n          setIsFavorited(response.data.is_favorited);\n        }\n      } catch (error) {\n        if (mounted) {\n          console.error('检查收藏状态失败:', error);\n        }\n      } finally {\n        if (mounted) {\n          setChecking(false);\n        }\n      }\n    }\n  }, [isAuthenticated, bookId]);\n\n  // 移动到 useEffect 内部以避免依赖问题\n\n  const handleToggleFavorite = async () => {\n    if (!isAuthenticated) {\n      message.warning('请先登录');\n      return;\n    }\n    try {\n      setLoading(true);\n      if (isFavorited) {\n        // 取消收藏\n        const response = await favoritesService.removeFavorite(bookId);\n        if (response.success) {\n          setIsFavorited(false);\n          message.success('已取消收藏');\n          onFavoriteChange === null || onFavoriteChange === void 0 ? void 0 : onFavoriteChange(false);\n        }\n      } else {\n        // 添加收藏\n        const response = await favoritesService.addFavorite(bookId);\n        if (response.success) {\n          setIsFavorited(true);\n          message.success('收藏成功');\n          onFavoriteChange === null || onFavoriteChange === void 0 ? void 0 : onFavoriteChange(true);\n        }\n      }\n    } catch (error) {\n      var _error$response;\n      console.error('操作收藏失败:', error);\n      if (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status) === 409) {\n        message.warning('已经收藏过这本图书');\n        setIsFavorited(true);\n      } else {\n        message.error('操作失败，请稍后重试');\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n  const buttonProps = {\n    size,\n    type: isFavorited ? 'primary' : type,\n    loading: loading || checking,\n    onClick: handleToggleFavorite,\n    className,\n    style,\n    icon: isFavorited ? /*#__PURE__*/_jsxDEV(HeartFilled, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 112,\n      columnNumber: 25\n    }, this) : /*#__PURE__*/_jsxDEV(HeartOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 112,\n      columnNumber: 43\n    }, this)\n  };\n  const buttonText = showText ? isFavorited ? '已收藏' : '收藏' : '';\n  if (!isAuthenticated) {\n    return /*#__PURE__*/_jsxDEV(Tooltip, {\n      title: \"\\u8BF7\\u5148\\u767B\\u5F55\",\n      children: /*#__PURE__*/_jsxDEV(Button, {\n        ...buttonProps,\n        disabled: true,\n        loading: false,\n        children: buttonText\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 120,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 119,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Tooltip, {\n    title: isFavorited ? '取消收藏' : '收藏图书',\n    children: /*#__PURE__*/_jsxDEV(Button, {\n      ...buttonProps,\n      children: buttonText\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 133,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 132,\n    columnNumber: 5\n  }, this);\n};\n_s(FavoriteButton, \"pieVY7AZN4u0FCBFQFmcTMSxGaM=\", false, function () {\n  return [useAuthStore];\n});\n_c = FavoriteButton;\nexport default FavoriteButton;\nvar _c;\n$RefreshReg$(_c, \"FavoriteButton\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "<PERSON><PERSON>", "message", "<PERSON><PERSON><PERSON>", "HeartOutlined", "HeartFilled", "favoritesService", "useAuthStore", "jsxDEV", "_jsxDEV", "FavoriteButton", "bookId", "size", "type", "showText", "className", "style", "onFavoriteChange", "_s", "isAuthenticated", "isFavorited", "setIsFavorited", "loading", "setLoading", "checking", "<PERSON><PERSON><PERSON><PERSON>", "mounted", "checkFavoriteStatus", "response", "checkFavorite", "success", "data", "is_favorited", "error", "console", "handleToggleFavorite", "warning", "removeFavorite", "addFavorite", "_error$response", "status", "buttonProps", "onClick", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "buttonText", "title", "children", "disabled", "_c", "$RefreshReg$"], "sources": ["D:/claude镜像/收书卖书/frontend/src/components/business/FavoriteButton.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Button, message, Tooltip } from 'antd';\nimport { HeartOutlined, HeartFilled } from '@ant-design/icons';\nimport { favoritesService } from '../../services/favorites';\nimport { useAuthStore } from '../../stores/authStore';\n\ninterface FavoriteButtonProps {\n  bookId: string;\n  size?: 'small' | 'middle' | 'large';\n  type?: 'default' | 'primary' | 'text' | 'link';\n  showText?: boolean;\n  className?: string;\n  style?: React.CSSProperties;\n  onFavoriteChange?: (isFavorited: boolean) => void;\n}\n\nconst FavoriteButton: React.FC<FavoriteButtonProps> = ({\n  bookId,\n  size = 'middle',\n  type = 'default',\n  showText = false,\n  className,\n  style,\n  onFavoriteChange\n}) => {\n  const { isAuthenticated } = useAuthStore();\n  const [isFavorited, setIsFavorited] = useState(false);\n  const [loading, setLoading] = useState(false);\n  const [checking, setChecking] = useState(true);\n\n  // 检查收藏状态\n  useEffect(() => {\n    let mounted = true;\n    \n    if (isAuthenticated && bookId) {\n      checkFavoriteStatus();\n    } else {\n      setChecking(false);\n    }\n    \n    return () => {\n      mounted = false;\n    };\n    \n    async function checkFavoriteStatus() {\n      try {\n        setChecking(true);\n        const response = await favoritesService.checkFavorite(bookId);\n        if (mounted && response.success && response.data) {\n          setIsFavorited(response.data.is_favorited);\n        }\n      } catch (error) {\n        if (mounted) {\n          console.error('检查收藏状态失败:', error);\n        }\n      } finally {\n        if (mounted) {\n          setChecking(false);\n        }\n      }\n    }\n  }, [isAuthenticated, bookId]);\n\n  // 移动到 useEffect 内部以避免依赖问题\n\n  const handleToggleFavorite = async () => {\n    if (!isAuthenticated) {\n      message.warning('请先登录');\n      return;\n    }\n\n    try {\n      setLoading(true);\n      \n      if (isFavorited) {\n        // 取消收藏\n        const response = await favoritesService.removeFavorite(bookId);\n        if (response.success) {\n          setIsFavorited(false);\n          message.success('已取消收藏');\n          onFavoriteChange?.(false);\n        }\n      } else {\n        // 添加收藏\n        const response = await favoritesService.addFavorite(bookId);\n        if (response.success) {\n          setIsFavorited(true);\n          message.success('收藏成功');\n          onFavoriteChange?.(true);\n        }\n      }\n    } catch (error: any) {\n      console.error('操作收藏失败:', error);\n      if (error.response?.status === 409) {\n        message.warning('已经收藏过这本图书');\n        setIsFavorited(true);\n      } else {\n        message.error('操作失败，请稍后重试');\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const buttonProps = {\n    size,\n    type: isFavorited ? 'primary' : type,\n    loading: loading || checking,\n    onClick: handleToggleFavorite,\n    className,\n    style,\n    icon: isFavorited ? <HeartFilled /> : <HeartOutlined />\n  };\n\n  const buttonText = showText ? (isFavorited ? '已收藏' : '收藏') : '';\n\n  if (!isAuthenticated) {\n    return (\n      <Tooltip title=\"请先登录\">\n        <Button\n          {...buttonProps}\n          disabled\n          loading={false}\n        >\n          {buttonText}\n        </Button>\n      </Tooltip>\n    );\n  }\n\n  return (\n    <Tooltip title={isFavorited ? '取消收藏' : '收藏图书'}>\n      <Button {...buttonProps}>\n        {buttonText}\n      </Button>\n    </Tooltip>\n  );\n};\n\nexport default FavoriteButton;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,EAAEC,OAAO,EAAEC,OAAO,QAAQ,MAAM;AAC/C,SAASC,aAAa,EAAEC,WAAW,QAAQ,mBAAmB;AAC9D,SAASC,gBAAgB,QAAQ,0BAA0B;AAC3D,SAASC,YAAY,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAYtD,MAAMC,cAA6C,GAAGA,CAAC;EACrDC,MAAM;EACNC,IAAI,GAAG,QAAQ;EACfC,IAAI,GAAG,SAAS;EAChBC,QAAQ,GAAG,KAAK;EAChBC,SAAS;EACTC,KAAK;EACLC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM;IAAEC;EAAgB,CAAC,GAAGZ,YAAY,CAAC,CAAC;EAC1C,MAAM,CAACa,WAAW,EAAEC,cAAc,CAAC,GAAGtB,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACuB,OAAO,EAAEC,UAAU,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACyB,QAAQ,EAAEC,WAAW,CAAC,GAAG1B,QAAQ,CAAC,IAAI,CAAC;;EAE9C;EACAC,SAAS,CAAC,MAAM;IACd,IAAI0B,OAAO,GAAG,IAAI;IAElB,IAAIP,eAAe,IAAIR,MAAM,EAAE;MAC7BgB,mBAAmB,CAAC,CAAC;IACvB,CAAC,MAAM;MACLF,WAAW,CAAC,KAAK,CAAC;IACpB;IAEA,OAAO,MAAM;MACXC,OAAO,GAAG,KAAK;IACjB,CAAC;IAED,eAAeC,mBAAmBA,CAAA,EAAG;MACnC,IAAI;QACFF,WAAW,CAAC,IAAI,CAAC;QACjB,MAAMG,QAAQ,GAAG,MAAMtB,gBAAgB,CAACuB,aAAa,CAAClB,MAAM,CAAC;QAC7D,IAAIe,OAAO,IAAIE,QAAQ,CAACE,OAAO,IAAIF,QAAQ,CAACG,IAAI,EAAE;UAChDV,cAAc,CAACO,QAAQ,CAACG,IAAI,CAACC,YAAY,CAAC;QAC5C;MACF,CAAC,CAAC,OAAOC,KAAK,EAAE;QACd,IAAIP,OAAO,EAAE;UACXQ,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;QACnC;MACF,CAAC,SAAS;QACR,IAAIP,OAAO,EAAE;UACXD,WAAW,CAAC,KAAK,CAAC;QACpB;MACF;IACF;EACF,CAAC,EAAE,CAACN,eAAe,EAAER,MAAM,CAAC,CAAC;;EAE7B;;EAEA,MAAMwB,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI,CAAChB,eAAe,EAAE;MACpBjB,OAAO,CAACkC,OAAO,CAAC,MAAM,CAAC;MACvB;IACF;IAEA,IAAI;MACFb,UAAU,CAAC,IAAI,CAAC;MAEhB,IAAIH,WAAW,EAAE;QACf;QACA,MAAMQ,QAAQ,GAAG,MAAMtB,gBAAgB,CAAC+B,cAAc,CAAC1B,MAAM,CAAC;QAC9D,IAAIiB,QAAQ,CAACE,OAAO,EAAE;UACpBT,cAAc,CAAC,KAAK,CAAC;UACrBnB,OAAO,CAAC4B,OAAO,CAAC,OAAO,CAAC;UACxBb,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAG,KAAK,CAAC;QAC3B;MACF,CAAC,MAAM;QACL;QACA,MAAMW,QAAQ,GAAG,MAAMtB,gBAAgB,CAACgC,WAAW,CAAC3B,MAAM,CAAC;QAC3D,IAAIiB,QAAQ,CAACE,OAAO,EAAE;UACpBT,cAAc,CAAC,IAAI,CAAC;UACpBnB,OAAO,CAAC4B,OAAO,CAAC,MAAM,CAAC;UACvBb,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAG,IAAI,CAAC;QAC1B;MACF;IACF,CAAC,CAAC,OAAOgB,KAAU,EAAE;MAAA,IAAAM,eAAA;MACnBL,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/B,IAAI,EAAAM,eAAA,GAAAN,KAAK,CAACL,QAAQ,cAAAW,eAAA,uBAAdA,eAAA,CAAgBC,MAAM,MAAK,GAAG,EAAE;QAClCtC,OAAO,CAACkC,OAAO,CAAC,WAAW,CAAC;QAC5Bf,cAAc,CAAC,IAAI,CAAC;MACtB,CAAC,MAAM;QACLnB,OAAO,CAAC+B,KAAK,CAAC,YAAY,CAAC;MAC7B;IACF,CAAC,SAAS;MACRV,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMkB,WAAW,GAAG;IAClB7B,IAAI;IACJC,IAAI,EAAEO,WAAW,GAAG,SAAS,GAAGP,IAAI;IACpCS,OAAO,EAAEA,OAAO,IAAIE,QAAQ;IAC5BkB,OAAO,EAAEP,oBAAoB;IAC7BpB,SAAS;IACTC,KAAK;IACL2B,IAAI,EAAEvB,WAAW,gBAAGX,OAAA,CAACJ,WAAW;MAAAuC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,gBAAGtC,OAAA,CAACL,aAAa;MAAAwC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EACxD,CAAC;EAED,MAAMC,UAAU,GAAGlC,QAAQ,GAAIM,WAAW,GAAG,KAAK,GAAG,IAAI,GAAI,EAAE;EAE/D,IAAI,CAACD,eAAe,EAAE;IACpB,oBACEV,OAAA,CAACN,OAAO;MAAC8C,KAAK,EAAC,0BAAM;MAAAC,QAAA,eACnBzC,OAAA,CAACR,MAAM;QAAA,GACDwC,WAAW;QACfU,QAAQ;QACR7B,OAAO,EAAE,KAAM;QAAA4B,QAAA,EAEdF;MAAU;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAEd;EAEA,oBACEtC,OAAA,CAACN,OAAO;IAAC8C,KAAK,EAAE7B,WAAW,GAAG,MAAM,GAAG,MAAO;IAAA8B,QAAA,eAC5CzC,OAAA,CAACR,MAAM;MAAA,GAAKwC,WAAW;MAAAS,QAAA,EACpBF;IAAU;MAAAJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEd,CAAC;AAAC7B,EAAA,CAzHIR,cAA6C;EAAA,QASrBH,YAAY;AAAA;AAAA6C,EAAA,GATpC1C,cAA6C;AA2HnD,eAAeA,cAAc;AAAC,IAAA0C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}