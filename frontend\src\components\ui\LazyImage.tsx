import React, { useState, useRef, useEffect, useCallback } from 'react';
import { Skeleton } from 'antd';
import styled from 'styled-components';

const ImageContainer = styled.div<{ width?: string | number; height?: string | number }>`
  position: relative;
  width: ${props => typeof props.width === 'number' ? `${props.width}px` : props.width || '100%'};
  height: ${props => typeof props.height === 'number' ? `${props.height}px` : props.height || 'auto'};
  overflow: hidden;
  
  .lazy-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: opacity 0.3s ease;
    
    &.loading {
      opacity: 0;
    }
    
    &.loaded {
      opacity: 1;
    }
    
    &.error {
      opacity: 0;
    }
  }
  
  .placeholder {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f5f5f5;
    color: #999;
    font-size: 14px;
    
    &.hidden {
      display: none;
    }
  }
  
  .skeleton {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    
    &.hidden {
      display: none;
    }
  }
`;

interface LazyImageProps {
  src: string;
  alt?: string;
  width?: string | number;
  height?: string | number;
  placeholder?: React.ReactNode;
  fallback?: string;
  className?: string;
  style?: React.CSSProperties;
  onClick?: () => void;
  onLoad?: () => void;
  onError?: () => void;
  threshold?: number; // Intersection observer threshold
  rootMargin?: string; // Intersection observer root margin
}

const LazyImage: React.FC<LazyImageProps> = ({
  src,
  alt = '',
  width,
  height,
  placeholder,
  fallback = '/images/placeholder.png',
  className,
  style,
  onClick,
  onLoad,
  onError,
  threshold = 0.1,
  rootMargin = '50px'
}) => {
  const [imageState, setImageState] = useState<'loading' | 'loaded' | 'error'>('loading');
  const [imageSrc, setImageSrc] = useState<string>('');
  const [isInView, setIsInView] = useState(false);
  const mountedRef = useRef(true);
  const imgRef = useRef<HTMLImageElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // 组件清理
  useEffect(() => {
    return () => {
      mountedRef.current = false;
    };
  }, []);

  // Intersection Observer for lazy loading
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        const [entry] = entries;
        if (entry.isIntersecting && mountedRef.current) {
          setIsInView(true);
          observer.disconnect();
        }
      },
      {
        threshold,
        rootMargin
      }
    );

    if (containerRef.current) {
      observer.observe(containerRef.current);
    }

    return () => {
      observer.disconnect();
    };
  }, [threshold, rootMargin]);

  // Load image when in view
  useEffect(() => {
    if (isInView && src) {
      setImageSrc(src);
    }
  }, [isInView, src]);

  // Handle image load
  const handleImageLoad = useCallback(() => {
    if (mountedRef.current) {
      setImageState('loaded');
      onLoad?.();
    }
  }, [onLoad]);

  // Handle image error
  const handleImageError = useCallback(() => {
    if (mountedRef.current) {
      setImageState('error');
      if (fallback && fallback !== imageSrc) {
        setImageSrc(fallback);
        setImageState('loading');
      } else {
        onError?.();
      }
    }
  }, [fallback, imageSrc, onError]);

  // Preload image
  useEffect(() => {
    if (imageSrc) {
      const img = new Image();
      img.onload = handleImageLoad;
      img.onerror = handleImageError;
      img.src = imageSrc;
      
      return () => {
        img.onload = null;
        img.onerror = null;
      };
    }
  }, [imageSrc, handleImageLoad, handleImageError]);

  const showSkeleton = imageState === 'loading' && !placeholder;
  const showPlaceholder = imageState === 'loading' && placeholder;
  const showImage = imageState === 'loaded';
  const showErrorPlaceholder = imageState === 'error' && !fallback;

  return (
    <ImageContainer
      ref={containerRef}
      width={width}
      height={height}
      className={className}
      style={style}
      onClick={onClick}
    >
      {/* 骨架屏 */}
      {showSkeleton && (
        <div className="skeleton">
          <Skeleton.Image style={{ width: '100%', height: '100%' }} />
        </div>
      )}

      {/* 自定义占位符 */}
      {showPlaceholder && (
        <div className="placeholder">
          {placeholder}
        </div>
      )}

      {/* 错误占位符 */}
      {showErrorPlaceholder && (
        <div className="placeholder">
          图片加载失败
        </div>
      )}

      {/* 实际图片 */}
      {imageSrc && (
        <img
          ref={imgRef}
          src={imageSrc}
          alt={alt}
          className={`lazy-image ${imageState}`}
          style={{ display: showImage ? 'block' : 'none' }}
        />
      )}
    </ImageContainer>
  );
};

export default LazyImage;
