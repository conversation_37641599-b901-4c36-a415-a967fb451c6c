# 收书卖书平台 - 安全增强的 Nginx 配置
# 作者: DevOps Team
# 版本: 2.0

user nginx;
worker_processes auto;
worker_rlimit_nofile 65535;
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

events {
    worker_connections 4096;
    use epoll;
    multi_accept on;
    accept_mutex off;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # 日志格式 - 包含安全信息
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for" '
                    'rt=$request_time uct="$upstream_connect_time" '
                    'uht="$upstream_header_time" urt="$upstream_response_time"';

    log_format security '$remote_addr - $remote_user [$time_local] "$request" '
                       '$status "$http_user_agent" "$http_x_forwarded_for" '
                       '"$http_x_real_ip" "$sent_http_x_frame_options"';

    access_log /var/log/nginx/access.log main buffer=32k flush=5m;
    error_log /var/log/nginx/error.log warn;

    # 基础性能配置
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    keepalive_requests 1000;
    types_hash_max_size 2048;
    server_tokens off;
    more_clear_headers 'Server';

    # 客户端配置
    client_max_body_size 50M;
    client_body_buffer_size 128k;
    client_header_buffer_size 4k;
    large_client_header_buffers 4 16k;
    client_body_timeout 60;
    client_header_timeout 60;
    send_timeout 60;

    # 安全配置
    server_names_hash_bucket_size 128;
    server_names_hash_max_size 512;

    # SSL 安全配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers off;
    ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:DHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    ssl_session_tickets off;

    # OCSP Stapling
    ssl_stapling on;
    ssl_stapling_verify on;
    resolver ******* ******* valid=300s;
    resolver_timeout 5s;

    # Gzip 压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_comp_level 6;
    gzip_proxied any;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        text/csv
        application/javascript
        application/xml+rss
        application/atom+xml
        application/json
        application/ld+json
        application/manifest+json
        application/rss+xml
        application/vnd.geo+json
        application/vnd.ms-fontobject
        application/x-font-ttf
        application/x-web-app-manifest+json
        font/opentype
        image/bmp
        image/svg+xml
        image/x-icon;

    # Brotli 压缩 (如果可用)
    # brotli on;
    # brotli_comp_level 6;
    # brotli_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;

    # 缓存配置
    proxy_cache_path /var/cache/nginx/proxy levels=1:2 keys_zone=app_cache:10m max_size=1g 
                     inactive=60m use_temp_path=off;
    proxy_cache_path /var/cache/nginx/static levels=1:2 keys_zone=static_cache:10m max_size=2g 
                     inactive=24h use_temp_path=off;

    # 限流和安全配置
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=login:10m rate=5r/m;
    limit_req_zone $binary_remote_addr zone=register:10m rate=2r/m;
    limit_req_zone $binary_remote_addr zone=upload:10m rate=1r/s;
    limit_req_zone $binary_remote_addr zone=global:10m rate=20r/s;

    # 连接限制
    limit_conn_zone $binary_remote_addr zone=perip:10m;
    limit_conn_zone $server_name zone=perserver:10m;

    # IP 黑名单 (可以动态更新)
    include /etc/nginx/conf.d/blacklist.conf;

    # 上游服务器配置
    upstream backend {
        least_conn;
        server backend:3001 max_fails=3 fail_timeout=30s weight=1;
        # server backend2:3001 max_fails=3 fail_timeout=30s weight=1; # 负载均衡备用
        keepalive 32;
        keepalive_requests 100;
        keepalive_timeout 60s;
    }

    upstream frontend {
        least_conn;
        server frontend:80 max_fails=3 fail_timeout=30s weight=1;
        # server frontend2:80 max_fails=3 fail_timeout=30s weight=1; # 负载均衡备用
        keepalive 16;
    }

    # 地理位置限制 (可选，需要 GeoIP 模块)
    # geoip_country /usr/share/GeoIP/GeoIP.dat;
    # map $geoip_country_code $allowed_country {
    #     default yes;
    #     CN yes;
    #     US yes;
    #     ~^(RU|KP)$ no;  # 禁止俄罗斯和朝鲜
    # }

    # 机器人检测
    map $http_user_agent $blocked_agent {
        default 0;
        ~*bot 1;
        ~*spider 1;
        ~*crawler 1;
        ~*scanner 1;
        ~*nmap 1;
        ~*nikto 1;
        ~*sqlmap 1;
        "Mozilla/5.0 (compatible; MSIE 6.0; Windows NT 5.1)" 1;
    }

    # WAF规则 - 基本SQL注入防护
    map $args $blocked_sql {
        default 0;
        ~*(\%27|\'|\-\-|\%23|\#) 1;
        ~*(union|select|insert|delete|update|drop|create|alter|exec|declare) 1;
        ~*(\<script|\<iframe|\<object|\<embed) 1;
    }

    # 速率限制状态码
    limit_req_status 429;
    limit_conn_status 429;

    # HTTP 重定向到 HTTPS (生产环境)
    server {
        listen 80 default_server;
        listen [::]:80 default_server;
        server_name _;
        
        # 安全配置
        add_header X-Content-Type-Options nosniff always;
        add_header X-Frame-Options DENY always;
        add_header X-XSS-Protection "1; mode=block" always;
        
        # 健康检查端点
        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }
        
        # ACME 挑战 (Let's Encrypt)
        location /.well-known/acme-challenge/ {
            root /var/www/certbot;
        }
        
        # 重定向所有其他请求到 HTTPS
        location / {
            return 301 https://$host$request_uri;
        }
    }

    # 主站点 HTTPS 配置
    server {
        listen 443 ssl http2 default_server;
        listen [::]:443 ssl http2 default_server;
        server_name bookstore.example.com;
        
        # SSL 证书配置
        ssl_certificate /etc/nginx/ssl/bookstore.crt;
        ssl_certificate_key /etc/nginx/ssl/bookstore.key;
        ssl_trusted_certificate /etc/nginx/ssl/ca-bundle.crt;
        
        # 安全头配置
        add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload" always;
        add_header X-Frame-Options DENY always;
        add_header X-Content-Type-Options nosniff always;
        add_header X-XSS-Protection "1; mode=block" always;
        add_header Referrer-Policy "strict-origin-when-cross-origin" always;
        add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' https:; connect-src 'self' https:; media-src 'self'; object-src 'none'; child-src 'none'; worker-src 'none'; frame-ancestors 'none'; form-action 'self'; base-uri 'self';" always;
        add_header Permissions-Policy "camera=(), microphone=(), geolocation=(), interest-cohort=()" always;
        
        # 全局限流
        limit_req zone=global burst=50 nodelay;
        limit_conn perip 20;
        limit_conn perserver 1000;
        
        # 阻止恶意请求
        if ($blocked_agent) {
            return 403 "Access Denied";
        }
        
        if ($blocked_sql) {
            return 403 "SQL Injection Detected";
        }
        
        # 地理位置限制 (如果启用)
        # if ($allowed_country = no) {
        #     return 403 "Access from your country is not allowed";
        # }
        
        # 阻止常见攻击路径
        location ~* /(\.git|\.svn|\.env|wp-admin|phpmyadmin|admin|config|backup|\.htaccess) {
            deny all;
            return 404;
        }
        
        # 阻止敏感文件
        location ~* \.(bak|backup|old|orig|save|swo|swp|tmp|~)$ {
            deny all;
            return 404;
        }
        
        # 静态资源缓存
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot|otf)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
            add_header X-Cache-Status "STATIC";
            
            # 防盗链
            valid_referers none blocked server_names *.bookstore.example.com;
            if ($invalid_referer) {
                return 403;
            }
            
            proxy_pass http://frontend;
            proxy_cache static_cache;
            proxy_cache_valid 200 1d;
            proxy_cache_use_stale error timeout http_500 http_502 http_503 http_504;
        }
        
        # API 代理
        location /api/ {
            # 安全检查
            if ($request_method !~ ^(GET|POST|PUT|PATCH|DELETE|OPTIONS)$ ) {
                return 405;
            }
            
            # API 限流
            limit_req zone=api burst=20 nodelay;
            
            # 登录接口特殊限流
            location /api/auth/login {
                limit_req zone=login burst=5 nodelay;
                proxy_pass http://backend;
                include /etc/nginx/proxy_params;
            }
            
            # 注册接口限流
            location /api/auth/register {
                limit_req zone=register burst=2 nodelay;
                proxy_pass http://backend;
                include /etc/nginx/proxy_params;
            }
            
            # 文件上传限流
            location /api/upload {
                limit_req zone=upload burst=3 nodelay;
                client_max_body_size 50M;
                proxy_read_timeout 300;
                proxy_pass http://backend;
                include /etc/nginx/proxy_params;
            }
            
            # 默认API路由
            proxy_pass http://backend;
            include /etc/nginx/proxy_params;
            
            # API 缓存
            proxy_cache app_cache;
            proxy_cache_valid 200 302 5m;
            proxy_cache_valid 404 1m;
            proxy_cache_bypass $cookie_session $http_authorization;
            proxy_no_cache $cookie_session $http_authorization;
            add_header X-Cache-Status $upstream_cache_status;
        }
        
        # WebSocket 支持
        location /socket.io/ {
            proxy_pass http://backend;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_read_timeout 86400;
        }
        
        # 文件上传服务
        location /uploads/ {
            alias /var/www/uploads/;
            expires 1y;
            add_header Cache-Control "public";
            add_header X-Content-Type-Options nosniff;
            
            # 防盗链
            valid_referers none blocked server_names *.bookstore.example.com;
            if ($invalid_referer) {
                return 403;
            }
            
            # 限制文件类型
            location ~* \.(php|phtml|pl|py|jsp|asp|sh|cgi)$ {
                deny all;
                return 403;
            }
            
            # 图片优化 (需要 image_filter 模块)
            # location ~* \.(jpg|jpeg|png|gif)$ {
            #     image_filter_buffer 10M;
            #     image_filter_jpeg_quality 85;
            #     image_filter_transparency on;
            # }
        }
        
        # 前端应用
        location / {
            # 基本安全检查
            if ($request_method !~ ^(GET|POST|HEAD|OPTIONS)$ ) {
                return 405;
            }
            
            proxy_pass http://frontend;
            include /etc/nginx/proxy_params;
            
            # SPA 支持
            try_files $uri $uri/ /index.html;
            
            # 缓存配置
            location ~* \.(?:manifest|appcache|html?|xml|json)$ {
                expires -1;
                add_header Cache-Control "no-cache, no-store, must-revalidate";
            }
        }
        
        # 健康检查
        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }
        
        # Nginx 状态页面 (仅内网访问)
        location /nginx_status {
            stub_status on;
            allow 127.0.0.1;
            allow 10.0.0.0/8;
            allow 172.16.0.0/12;
            allow 192.168.0.0/16;
            deny all;
        }
        
        # 错误页面
        error_page 400 /error/400.html;
        error_page 401 /error/401.html;
        error_page 403 /error/403.html;
        error_page 404 /error/404.html;
        error_page 429 /error/429.html;
        error_page 500 502 503 504 /error/50x.html;
        
        location ^~ /error/ {
            internal;
            root /var/www/html;
        }
    }
    
    # API 专用子域名
    server {
        listen 443 ssl http2;
        server_name api.bookstore.example.com;
        
        # SSL 配置 (复用主站证书)
        ssl_certificate /etc/nginx/ssl/bookstore.crt;
        ssl_certificate_key /etc/nginx/ssl/bookstore.key;
        ssl_trusted_certificate /etc/nginx/ssl/ca-bundle.crt;
        
        # 安全头
        add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload" always;
        add_header X-Frame-Options DENY always;
        add_header X-Content-Type-Options nosniff always;
        add_header X-XSS-Protection "1; mode=block" always;
        
        # CORS 配置
        add_header Access-Control-Allow-Origin "https://bookstore.example.com" always;
        add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
        add_header Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept, Authorization, Cache-Control" always;
        add_header Access-Control-Allow-Credentials true always;
        add_header Access-Control-Max-Age 1728000 always;
        
        # 处理 OPTIONS 预检请求
        if ($request_method = 'OPTIONS') {
            add_header Access-Control-Allow-Origin "https://bookstore.example.com";
            add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS";
            add_header Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept, Authorization, Cache-Control";
            add_header Access-Control-Allow-Credentials true;
            add_header Access-Control-Max-Age 1728000;
            add_header Content-Type "text/plain charset=UTF-8";
            add_header Content-Length 0;
            return 204;
        }
        
        # API 路由
        location / {
            limit_req zone=api burst=30 nodelay;
            
            proxy_pass http://backend;
            include /etc/nginx/proxy_params;
        }
    }
}

# 流配置 (用于TCP/UDP代理，如果需要)
# stream {
#     upstream database {
#         server postgres:5432;
#     }
    
#     server {
#         listen 5432;
#         proxy_pass database;
#     }
# }