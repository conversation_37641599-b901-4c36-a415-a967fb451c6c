const express = require('express');
const jwt = require('jsonwebtoken');
const { User } = require('../models');
const { validate, registerSchema, loginSchema } = require('../utils/validation');

const router = express.Router();

// 安全的JWT令牌生成
const generateToken = (userId, tokenType = 'access') => {
  const payload = {
    userId,
    type: tokenType,
    iat: Math.floor(Date.now() / 1000),
    jti: require('crypto').randomBytes(16).toString('hex') // 唯一ID
  };
  
  const expiresIn = tokenType === 'refresh' 
    ? process.env.JWT_REFRESH_EXPIRES_IN || '30d'
    : process.env.JWT_EXPIRES_IN || '1h';
    
  return jwt.sign(
    payload,
    process.env.JWT_SECRET,
    { 
      expiresIn,
      algorithm: 'HS256',
      issuer: 'book-trading-platform'
    }
  );
};

// Token黑名单（生产环境应使用Redis）
const tokenBlacklist = new Set();

// 检查Token是否在黑名单中
const isTokenBlacklisted = (jti) => {
  return tokenBlacklist.has(jti);
};

// 将Token加入黑名单
const blacklistToken = (jti, expiresAt) => {
  tokenBlacklist.add(jti);
  // 设置定时清理（实际生产中应使用Redis的TTL）
  setTimeout(() => {
    tokenBlacklist.delete(jti);
  }, expiresAt - Date.now());
};

// 用户注册
router.post('/register', validate(registerSchema), async (req, res) => {
  try {
    const { phone, email, password, username } = req.body;

    // 检查手机号是否已存在
    const existingUser = await User.findOne({ where: { phone } });
    if (existingUser) {
      return res.status(400).json({
        success: false,
        message: '手机号已被注册'
      });
    }

    // 检查邮箱是否已存在（如果提供了邮箱）
    if (email) {
      const existingEmail = await User.findOne({ where: { email } });
      if (existingEmail) {
        return res.status(400).json({
          success: false,
          message: '邮箱已被注册'
        });
      }
    }

    // 检查用户名是否已存在（如果提供了用户名）
    if (username) {
      const existingUsername = await User.findOne({ where: { username } });
      if (existingUsername) {
        return res.status(400).json({
          success: false,
          message: '用户名已被使用'
        });
      }
    }

    // 创建用户
    const user = await User.create({
      phone,
      email,
      username,
      password_hash: password, // 会在模型的hook中自动加密
      register_type: 'phone'
    });

    // 生成访问令牌和刷新令牌
    const accessToken = generateToken(user.id, 'access');
    const refreshToken = generateToken(user.id, 'refresh');

    res.status(201).json({
      success: true,
      message: '注册成功',
      data: {
        user,
        access_token: accessToken,
        refresh_token: refreshToken,
        expires_in: 3600 // 1小时
      }
    });
  } catch (error) {
    console.error('注册错误:', error);
    res.status(500).json({
      success: false,
      message: '注册失败'
    });
  }
});

// 用户登录
router.post('/login', validate(loginSchema), async (req, res) => {
  try {
    const { phone, password } = req.body;

    // 查找用户
    const user = await User.findOne({ where: { phone } });
    if (!user) {
      return res.status(401).json({
        success: false,
        message: '手机号或密码错误'
      });
    }

    // 验证密码
    const isValidPassword = await user.validatePassword(password);
    if (!isValidPassword) {
      return res.status(401).json({
        success: false,
        message: '手机号或密码错误'
      });
    }

    // 检查账户状态
    if (user.status !== 'active') {
      return res.status(401).json({
        success: false,
        message: '账户已被禁用'
      });
    }

    // 更新最后登录时间
    await user.update({ last_login_at: new Date() });

    // 生成访问令牌和刷新令牌
    const accessToken = generateToken(user.id, 'access');
    const refreshToken = generateToken(user.id, 'refresh');

    res.json({
      success: true,
      message: '登录成功',
      data: {
        user,
        access_token: accessToken,
        refresh_token: refreshToken,
        expires_in: 3600 // 1小时
      }
    });
  } catch (error) {
    console.error('登录错误:', error);
    res.status(500).json({
      success: false,
      message: '登录失败'
    });
  }
});

// 安全的令牌刷新
router.post('/refresh', async (req, res) => {
  try {
    const { refresh_token } = req.body;
    
    if (!refresh_token) {
      return res.status(401).json({
        success: false,
        message: '刷新令牌缺失',
        code: 'REFRESH_TOKEN_MISSING'
      });
    }

    // 验证刷新令牌（不允许过期）
    const decoded = jwt.verify(refresh_token, process.env.JWT_SECRET, {
      algorithms: ['HS256']
    });
    
    // 检查token类型
    if (decoded.type !== 'refresh') {
      return res.status(401).json({
        success: false,
        message: '无效的刷新令牌',
        code: 'INVALID_REFRESH_TOKEN'
      });
    }
    
    // 检查是否在黑名单中
    if (isTokenBlacklisted(decoded.jti)) {
      return res.status(401).json({
        success: false,
        message: '令牌已失效',
        code: 'TOKEN_BLACKLISTED'
      });
    }
    
    // 查找用户
    const user = await User.findByPk(decoded.userId);
    if (!user || user.status !== 'active') {
      return res.status(401).json({
        success: false,
        message: '用户不存在或已被禁用',
        code: 'USER_INVALID'
      });
    }

    // 将旧的刷新令牌加入黑名单
    blacklistToken(decoded.jti, decoded.exp * 1000);

    // 生成新的访问令牌和刷新令牌
    const newAccessToken = generateToken(user.id, 'access');
    const newRefreshToken = generateToken(user.id, 'refresh');

    res.json({
      success: true,
      message: '令牌刷新成功',
      data: {
        access_token: newAccessToken,
        refresh_token: newRefreshToken,
        expires_in: 3600
      }
    });
  } catch (error) {
    console.error('令牌刷新错误:', error);
    
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        success: false,
        message: '刷新令牌已过期',
        code: 'REFRESH_TOKEN_EXPIRED'
      });
    }
    
    res.status(401).json({
      success: false,
      message: '令牌刷新失败',
      code: 'REFRESH_FAILED'
    });
  }
});

// 用户登出
router.post('/logout', async (req, res) => {
  try {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];
    const { refresh_token } = req.body;
    
    if (token) {
      try {
        const decoded = jwt.verify(token, process.env.JWT_SECRET, {
          ignoreExpiration: true
        });
        blacklistToken(decoded.jti, decoded.exp * 1000);
      } catch (error) {
        // 忽略token验证错误
      }
    }
    
    if (refresh_token) {
      try {
        const decoded = jwt.verify(refresh_token, process.env.JWT_SECRET, {
          ignoreExpiration: true
        });
        blacklistToken(decoded.jti, decoded.exp * 1000);
      } catch (error) {
        // 忽略token验证错误
      }
    }
    
    res.json({
      success: true,
      message: '登出成功'
    });
  } catch (error) {
    console.error('登出错误:', error);
    res.status(500).json({
      success: false,
      message: '登出失败'
    });
  }
});

// 第三方登录（微信）- 占位实现
router.post('/wechat', async (req, res) => {
  try {
    // TODO: 实现微信登录逻辑
    res.status(501).json({
      success: false,
      message: '微信登录功能暂未实现'
    });
  } catch (error) {
    console.error('微信登录错误:', error);
    res.status(500).json({
      success: false,
      message: '微信登录失败'
    });
  }
});

// 第三方登录（QQ）- 占位实现
router.post('/qq', async (req, res) => {
  try {
    // TODO: 实现QQ登录逻辑
    res.status(501).json({
      success: false,
      message: 'QQ登录功能暂未实现'
    });
  } catch (error) {
    console.error('QQ登录错误:', error);
    res.status(500).json({
      success: false,
      message: 'QQ登录失败'
    });
  }
});

module.exports = router;
