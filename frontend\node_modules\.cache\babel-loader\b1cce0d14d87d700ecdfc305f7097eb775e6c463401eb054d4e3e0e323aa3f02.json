{"ast": null, "code": "var _jsxFileName = \"D:\\\\claude\\u955C\\u50CF\\\\\\u6536\\u4E66\\u5356\\u4E66\\\\frontend\\\\src\\\\components\\\\business\\\\EnhancedBookCard.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { Tag, Typography, message, Tooltip } from 'antd';\nimport { ShoppingCartOutlined, EyeOutlined, FireOutlined, CrownOutlined, ThunderboltOutlined, StarFilled } from '@ant-design/icons';\nimport styled from 'styled-components';\nimport { useCartStore } from '../../stores/cartStore';\nimport { useAuthStore } from '../../stores/authStore';\nimport FavoriteButton from './FavoriteButton';\nimport LazyImage from '../ui/LazyImage';\nimport Button from '../ui/Button';\nimport Card from '../ui/Card';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst {\n  Text\n} = Typography;\nconst StyledCard = styled(Card)`\n  position: relative;\n  border-radius: 16px;\n  overflow: hidden;\n  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);\n  border: 1px solid #f0f0f0;\n  background: #ffffff;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\n  \n  &:hover {\n    transform: translateY(-12px) scale(1.02);\n    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);\n    border-color: #91caff;\n  }\n  \n  .ant-card-body {\n    padding: 20px;\n  }\n  \n  .ant-card-cover {\n    position: relative;\n    overflow: hidden;\n  }\n`;\n_c = StyledCard;\nconst ImageContainer = styled.div`\n  position: relative;\n  width: 100%;\n  height: 280px;\n  overflow: hidden;\n  background: linear-gradient(135deg, #f5f5f5 0%, #e8e8e8 100%);\n  \n  .book-image {\n    width: 100%;\n    height: 100%;\n    object-fit: cover;\n    transition: all 0.4s ease;\n    filter: brightness(1) saturate(1);\n  }\n  \n  &:hover .book-image {\n    transform: scale(1.08);\n    filter: brightness(1.1) saturate(1.2);\n  }\n  \n  .image-overlay {\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background: linear-gradient(\n      135deg,\n      rgba(22, 119, 255, 0.8) 0%,\n      rgba(64, 150, 255, 0.6) 100%\n    );\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    opacity: 0;\n    transition: all 0.3s ease;\n    backdrop-filter: blur(2px);\n    \n    .overlay-actions {\n      display: flex;\n      gap: 16px;\n      transform: translateY(20px);\n      transition: transform 0.3s ease;\n    }\n  }\n  \n  &:hover .image-overlay {\n    opacity: 1;\n    \n    .overlay-actions {\n      transform: translateY(0);\n    }\n  }\n`;\n_c2 = ImageContainer;\nconst BookInfo = styled.div`\n  .book-title {\n    font-size: 18px;\n    font-weight: 700;\n    color: #262626;\n    margin-bottom: 10px;\n    line-height: 1.4;\n    display: -webkit-box;\n    -webkit-line-clamp: 2;\n    -webkit-box-orient: vertical;\n    overflow: hidden;\n    min-height: 50px;\n    cursor: pointer;\n    transition: color 0.3s ease;\n    \n    &:hover {\n      color: #1677ff;\n    }\n  }\n  \n  .book-author {\n    font-size: 14px;\n    color: #595959;\n    margin-bottom: 16px;\n    font-weight: 500;\n  }\n  \n  .book-meta {\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    margin-bottom: 16px;\n    \n    .condition-tag {\n      font-weight: 600;\n      border-radius: 8px;\n      padding: 4px 12px;\n      font-size: 12px;\n    }\n    \n    .stats {\n      display: flex;\n      align-items: center;\n      gap: 12px;\n      font-size: 13px;\n      color: #8c8c8c;\n      \n      .stat-item {\n        display: flex;\n        align-items: center;\n        gap: 4px;\n        font-weight: 500;\n      }\n    }\n  }\n  \n  .price-section {\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    margin-bottom: 20px;\n    \n    .price-info {\n      display: flex;\n      align-items: baseline;\n      gap: 10px;\n      \n      .current-price {\n        font-size: 24px;\n        font-weight: 800;\n        color: #ff4d4f;\n        text-shadow: 0 1px 2px rgba(255, 77, 79, 0.2);\n      }\n      \n      .original-price {\n        font-size: 16px;\n        color: #8c8c8c;\n        text-decoration: line-through;\n        font-weight: 500;\n      }\n      \n      .discount {\n        background: linear-gradient(135deg, #ff4d4f, #ff7875);\n        color: white;\n        padding: 4px 8px;\n        border-radius: 12px;\n        font-size: 12px;\n        font-weight: 700;\n        box-shadow: 0 2px 8px rgba(255, 77, 79, 0.3);\n      }\n    }\n    \n    .rating {\n      display: flex;\n      align-items: center;\n      gap: 4px;\n      font-size: 13px;\n      color: #faad14;\n    }\n  }\n  \n  .actions {\n    display: flex;\n    gap: 12px;\n    \n    .add-cart-btn {\n      flex: 1;\n      height: 44px;\n      border-radius: 12px;\n      font-weight: 600;\n      font-size: 15px;\n      background: linear-gradient(135deg, #1677ff, #4096ff);\n      border: none;\n      box-shadow: 0 4px 12px rgba(22, 119, 255, 0.3);\n      \n      &:hover {\n        background: linear-gradient(135deg, #0958d9, #1677ff);\n        transform: translateY(-2px);\n        box-shadow: 0 8px 20px rgba(22, 119, 255, 0.4);\n      }\n      \n      &:active {\n        transform: translateY(0);\n      }\n    }\n    \n    .favorite-btn {\n      width: 44px;\n      height: 44px;\n      border-radius: 12px;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      border: 2px solid #f0f0f0;\n      background: white;\n      transition: all 0.3s ease;\n      \n      &:hover {\n        border-color: #ff4d4f;\n        background: #fff2f0;\n        transform: scale(1.1);\n      }\n    }\n  }\n`;\n_c3 = BookInfo;\nconst StatusBadge = styled.div`\n  position: absolute;\n  top: 16px;\n  left: 16px;\n  z-index: 2;\n  display: flex;\n  align-items: center;\n  gap: 6px;\n  padding: 6px 12px;\n  border-radius: 16px;\n  font-size: 12px;\n  font-weight: 700;\n  color: white;\n  backdrop-filter: blur(10px);\n  \n  ${props => {\n  switch (props.type) {\n    case 'hot':\n      return `\n          background: linear-gradient(135deg, #ff4d4f, #ff7875);\n          box-shadow: 0 4px 12px rgba(255, 77, 79, 0.4);\n        `;\n    case 'new':\n      return `\n          background: linear-gradient(135deg, #52c41a, #73d13d);\n          box-shadow: 0 4px 12px rgba(82, 196, 26, 0.4);\n        `;\n    case 'sale':\n      return `\n          background: linear-gradient(135deg, #faad14, #ffc069);\n          box-shadow: 0 4px 12px rgba(250, 173, 20, 0.4);\n        `;\n    case 'vip':\n      return `\n          background: linear-gradient(135deg, #722ed1, #9254de);\n          box-shadow: 0 4px 12px rgba(114, 46, 209, 0.4);\n        `;\n    default:\n      return '';\n  }\n}}\n`;\n_c4 = StatusBadge;\nconst StockIndicator = styled.div`\n  position: absolute;\n  bottom: 16px;\n  right: 16px;\n  z-index: 2;\n  padding: 4px 8px;\n  border-radius: 8px;\n  font-size: 11px;\n  font-weight: 600;\n  backdrop-filter: blur(10px);\n  \n  ${props => {\n  if (props.stock === 0) {\n    return `\n        background: rgba(255, 77, 79, 0.9);\n        color: white;\n      `;\n  } else if (props.stock <= 5) {\n    return `\n        background: rgba(250, 173, 20, 0.9);\n        color: white;\n      `;\n  } else {\n    return `\n        background: rgba(82, 196, 26, 0.9);\n        color: white;\n      `;\n  }\n}}\n`;\n_c5 = StockIndicator;\nconst EnhancedBookCard = ({\n  book,\n  showActions = true,\n  size = 'default',\n  className,\n  style\n}) => {\n  _s();\n  const navigate = useNavigate();\n  const {\n    addItem\n  } = useCartStore();\n  const {\n    isAuthenticated\n  } = useAuthStore();\n\n  // 计算折扣（安全版本）\n  const discount = useMemo(() => {\n    if (!book.original_price || book.original_price <= book.price) {\n      return null;\n    }\n    return Math.round((1 - book.price / book.original_price) * 100);\n  }, [book.original_price, book.price]);\n\n  // 判断图书状态（优化性能）\n  const bookStatus = useMemo(() => {\n    const isHot = book.sales_count > 50 || book.views > 1000;\n    const isNew = new Date(book.created_at).getTime() > Date.now() - 7 * 24 * 60 * 60 * 1000;\n    const isOnSale = discount && discount > 20;\n    const isVip = book.price > 100;\n    return {\n      isHot,\n      isNew,\n      isOnSale,\n      isVip\n    };\n  }, [book.sales_count, book.views, book.created_at, book.price, discount]);\n\n  // 评分（使用稳定的算法避免每次渲染都变化）\n  const rating = useMemo(() => {\n    // 基于图书ID生成稳定的评分\n    const seed = book.id.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0);\n    return 4.2 + seed % 80 / 100; // 4.2-4.99\n  }, [book.id]);\n\n  // 获取状况标签颜色\n  const getConditionColor = condition => {\n    const colorMap = {\n      '全新': '#52c41a',\n      '九成新': '#1677ff',\n      '八成新': '#faad14',\n      '七成新': '#ff7875',\n      '六成新': '#ff4d4f'\n    };\n    return colorMap[condition] || '#8c8c8c';\n  };\n  const handleAddToCart = e => {\n    e.stopPropagation();\n    if (!isAuthenticated) {\n      message.warning('请先登录');\n      return;\n    }\n    if (book.stock === 0) {\n      message.error('商品已售罄');\n      return;\n    }\n\n    // 直接传入完整的 book 对象\n    addItem(book);\n    message.success('已添加到购物车');\n  };\n  const handleViewDetail = () => {\n    navigate(`/books/${book.id}`);\n  };\n  return /*#__PURE__*/_jsxDEV(StyledCard, {\n    hoverable: true,\n    className: `enhanced-book-card ${className || ''}`,\n    style: style,\n    cover: /*#__PURE__*/_jsxDEV(ImageContainer, {\n      onClick: handleViewDetail,\n      children: [/*#__PURE__*/_jsxDEV(LazyImage, {\n        src: book.cover_image || '/images/book-placeholder.png',\n        alt: book.title,\n        className: \"book-image\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 412,\n        columnNumber: 11\n      }, this), bookStatus.isHot && /*#__PURE__*/_jsxDEV(StatusBadge, {\n        type: \"hot\",\n        children: [/*#__PURE__*/_jsxDEV(FireOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 421,\n          columnNumber: 15\n        }, this), \"\\u70ED\\u9500\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 420,\n        columnNumber: 13\n      }, this), bookStatus.isNew && !bookStatus.isHot && /*#__PURE__*/_jsxDEV(StatusBadge, {\n        type: \"new\",\n        children: [/*#__PURE__*/_jsxDEV(ThunderboltOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 427,\n          columnNumber: 15\n        }, this), \"\\u65B0\\u54C1\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 426,\n        columnNumber: 13\n      }, this), bookStatus.isOnSale && !bookStatus.isHot && !bookStatus.isNew && /*#__PURE__*/_jsxDEV(StatusBadge, {\n        type: \"sale\",\n        children: [\"\\u7279\\u60E0 \", discount, \"\\u6298\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 432,\n        columnNumber: 13\n      }, this), bookStatus.isVip && !bookStatus.isHot && !bookStatus.isNew && !bookStatus.isOnSale && /*#__PURE__*/_jsxDEV(StatusBadge, {\n        type: \"vip\",\n        children: [/*#__PURE__*/_jsxDEV(CrownOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 438,\n          columnNumber: 15\n        }, this), \"\\u7CBE\\u54C1\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 437,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(StockIndicator, {\n        stock: book.stock,\n        children: book.stock === 0 ? '售罄' : book.stock <= 5 ? `仅剩${book.stock}本` : '现货'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 444,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"image-overlay\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"overlay-actions\",\n          children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n            title: \"\\u67E5\\u770B\\u8BE6\\u60C5\",\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"gradient\",\n              gradient: \"primary\",\n              size: \"large\",\n              onClick: handleViewDetail,\n              rounded: true,\n              style: {\n                width: '48px',\n                height: '48px',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center'\n              },\n              children: /*#__PURE__*/_jsxDEV(EyeOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 466,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 452,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 451,\n            columnNumber: 15\n          }, this), showActions && /*#__PURE__*/_jsxDEV(FavoriteButton, {\n            bookId: book.id,\n            size: \"large\",\n            type: \"default\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 470,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 450,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 449,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 411,\n      columnNumber: 9\n    }, this),\n    children: /*#__PURE__*/_jsxDEV(BookInfo, {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"book-title\",\n        onClick: handleViewDetail,\n        children: book.title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 482,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"book-author\",\n        children: /*#__PURE__*/_jsxDEV(Text, {\n          type: \"secondary\",\n          children: book.author || '未知作者'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 487,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 486,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"book-meta\",\n        children: [/*#__PURE__*/_jsxDEV(Tag, {\n          className: \"condition-tag\",\n          color: getConditionColor(book.condition),\n          children: book.condition\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 491,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stats\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-item\",\n            children: [/*#__PURE__*/_jsxDEV(EyeOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 500,\n              columnNumber: 15\n            }, this), book.views]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 499,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-item\",\n            children: [\"\\u5DF2\\u552E \", book.sales_count]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 503,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 498,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 490,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"price-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"price-info\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"current-price\",\n            children: [\"\\xA5\", book.price]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 511,\n            columnNumber: 13\n          }, this), book.original_price && book.original_price > book.price && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"original-price\",\n              children: [\"\\xA5\", book.original_price]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 514,\n              columnNumber: 17\n            }, this), discount && /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"discount\",\n              children: [discount, \"\\u6298\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 516,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 510,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"rating\",\n          children: [/*#__PURE__*/_jsxDEV(StarFilled, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 523,\n            columnNumber: 13\n          }, this), rating.toFixed(1)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 522,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 509,\n        columnNumber: 9\n      }, this), showActions && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"actions\",\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"gradient\",\n          gradient: \"primary\",\n          size: \"medium\",\n          onClick: handleAddToCart,\n          disabled: book.stock === 0,\n          rounded: true,\n          elevated: true,\n          children: [/*#__PURE__*/_jsxDEV(ShoppingCartOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 539,\n            columnNumber: 15\n          }, this), \" \", book.stock === 0 ? '已售罄' : '加入购物车']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 530,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(FavoriteButton, {\n          bookId: book.id,\n          className: \"favorite-btn\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 542,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 529,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 481,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 406,\n    columnNumber: 5\n  }, this);\n};\n_s(EnhancedBookCard, \"EDoCfPCCRFoOI25GaBOowB3ciK8=\", false, function () {\n  return [useNavigate, useCartStore, useAuthStore];\n});\n_c6 = EnhancedBookCard;\nexport default EnhancedBookCard;\nvar _c, _c2, _c3, _c4, _c5, _c6;\n$RefreshReg$(_c, \"StyledCard\");\n$RefreshReg$(_c2, \"ImageContainer\");\n$RefreshReg$(_c3, \"BookInfo\");\n$RefreshReg$(_c4, \"StatusBadge\");\n$RefreshReg$(_c5, \"StockIndicator\");\n$RefreshReg$(_c6, \"EnhancedBookCard\");", "map": {"version": 3, "names": ["React", "useNavigate", "Tag", "Typography", "message", "<PERSON><PERSON><PERSON>", "ShoppingCartOutlined", "EyeOutlined", "FireOutlined", "CrownOutlined", "ThunderboltOutlined", "StarFilled", "styled", "useCartStore", "useAuthStore", "FavoriteButton", "LazyImage", "<PERSON><PERSON>", "Card", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Text", "StyledCard", "_c", "ImageContainer", "div", "_c2", "BookInfo", "_c3", "StatusBadge", "props", "type", "_c4", "StockIndicator", "stock", "_c5", "EnhancedBookCard", "book", "showActions", "size", "className", "style", "_s", "navigate", "addItem", "isAuthenticated", "discount", "useMemo", "original_price", "price", "Math", "round", "bookStatus", "isHot", "sales_count", "views", "isNew", "Date", "created_at", "getTime", "now", "isOnSale", "isVip", "rating", "seed", "id", "split", "reduce", "acc", "char", "charCodeAt", "getConditionColor", "condition", "colorMap", "handleAddToCart", "e", "stopPropagation", "warning", "error", "success", "handleViewDetail", "hoverable", "cover", "onClick", "children", "src", "cover_image", "alt", "title", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "gradient", "rounded", "width", "height", "display", "alignItems", "justifyContent", "bookId", "author", "color", "toFixed", "disabled", "elevated", "_c6", "$RefreshReg$"], "sources": ["D:/claude镜像/收书卖书/frontend/src/components/business/EnhancedBookCard.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { Tag, Space, Typography, message, Tooltip, Rate } from 'antd';\nimport {\n  ShoppingCartOutlined,\n  EyeOutlined,\n  FireOutlined,\n  CrownOutlined,\n  ThunderboltOutlined,\n  StarOutlined,\n  StarFilled,\n  HeartOutlined,\n  ShareAltOutlined\n} from '@ant-design/icons';\nimport styled from 'styled-components';\nimport { Book } from '../../types';\nimport { useCartStore } from '../../stores/cartStore';\nimport { useAuthStore } from '../../stores/authStore';\nimport FavoriteButton from './FavoriteButton';\nimport LazyImage from '../ui/LazyImage';\nimport Button from '../ui/Button';\nimport Card from '../ui/Card';\nimport { theme } from '../../styles/theme';\n\nconst { Text } = Typography;\n\nconst StyledCard = styled(Card)`\n  position: relative;\n  border-radius: 16px;\n  overflow: hidden;\n  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);\n  border: 1px solid #f0f0f0;\n  background: #ffffff;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\n  \n  &:hover {\n    transform: translateY(-12px) scale(1.02);\n    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);\n    border-color: #91caff;\n  }\n  \n  .ant-card-body {\n    padding: 20px;\n  }\n  \n  .ant-card-cover {\n    position: relative;\n    overflow: hidden;\n  }\n`;\n\nconst ImageContainer = styled.div`\n  position: relative;\n  width: 100%;\n  height: 280px;\n  overflow: hidden;\n  background: linear-gradient(135deg, #f5f5f5 0%, #e8e8e8 100%);\n  \n  .book-image {\n    width: 100%;\n    height: 100%;\n    object-fit: cover;\n    transition: all 0.4s ease;\n    filter: brightness(1) saturate(1);\n  }\n  \n  &:hover .book-image {\n    transform: scale(1.08);\n    filter: brightness(1.1) saturate(1.2);\n  }\n  \n  .image-overlay {\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background: linear-gradient(\n      135deg,\n      rgba(22, 119, 255, 0.8) 0%,\n      rgba(64, 150, 255, 0.6) 100%\n    );\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    opacity: 0;\n    transition: all 0.3s ease;\n    backdrop-filter: blur(2px);\n    \n    .overlay-actions {\n      display: flex;\n      gap: 16px;\n      transform: translateY(20px);\n      transition: transform 0.3s ease;\n    }\n  }\n  \n  &:hover .image-overlay {\n    opacity: 1;\n    \n    .overlay-actions {\n      transform: translateY(0);\n    }\n  }\n`;\n\nconst BookInfo = styled.div`\n  .book-title {\n    font-size: 18px;\n    font-weight: 700;\n    color: #262626;\n    margin-bottom: 10px;\n    line-height: 1.4;\n    display: -webkit-box;\n    -webkit-line-clamp: 2;\n    -webkit-box-orient: vertical;\n    overflow: hidden;\n    min-height: 50px;\n    cursor: pointer;\n    transition: color 0.3s ease;\n    \n    &:hover {\n      color: #1677ff;\n    }\n  }\n  \n  .book-author {\n    font-size: 14px;\n    color: #595959;\n    margin-bottom: 16px;\n    font-weight: 500;\n  }\n  \n  .book-meta {\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    margin-bottom: 16px;\n    \n    .condition-tag {\n      font-weight: 600;\n      border-radius: 8px;\n      padding: 4px 12px;\n      font-size: 12px;\n    }\n    \n    .stats {\n      display: flex;\n      align-items: center;\n      gap: 12px;\n      font-size: 13px;\n      color: #8c8c8c;\n      \n      .stat-item {\n        display: flex;\n        align-items: center;\n        gap: 4px;\n        font-weight: 500;\n      }\n    }\n  }\n  \n  .price-section {\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    margin-bottom: 20px;\n    \n    .price-info {\n      display: flex;\n      align-items: baseline;\n      gap: 10px;\n      \n      .current-price {\n        font-size: 24px;\n        font-weight: 800;\n        color: #ff4d4f;\n        text-shadow: 0 1px 2px rgba(255, 77, 79, 0.2);\n      }\n      \n      .original-price {\n        font-size: 16px;\n        color: #8c8c8c;\n        text-decoration: line-through;\n        font-weight: 500;\n      }\n      \n      .discount {\n        background: linear-gradient(135deg, #ff4d4f, #ff7875);\n        color: white;\n        padding: 4px 8px;\n        border-radius: 12px;\n        font-size: 12px;\n        font-weight: 700;\n        box-shadow: 0 2px 8px rgba(255, 77, 79, 0.3);\n      }\n    }\n    \n    .rating {\n      display: flex;\n      align-items: center;\n      gap: 4px;\n      font-size: 13px;\n      color: #faad14;\n    }\n  }\n  \n  .actions {\n    display: flex;\n    gap: 12px;\n    \n    .add-cart-btn {\n      flex: 1;\n      height: 44px;\n      border-radius: 12px;\n      font-weight: 600;\n      font-size: 15px;\n      background: linear-gradient(135deg, #1677ff, #4096ff);\n      border: none;\n      box-shadow: 0 4px 12px rgba(22, 119, 255, 0.3);\n      \n      &:hover {\n        background: linear-gradient(135deg, #0958d9, #1677ff);\n        transform: translateY(-2px);\n        box-shadow: 0 8px 20px rgba(22, 119, 255, 0.4);\n      }\n      \n      &:active {\n        transform: translateY(0);\n      }\n    }\n    \n    .favorite-btn {\n      width: 44px;\n      height: 44px;\n      border-radius: 12px;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      border: 2px solid #f0f0f0;\n      background: white;\n      transition: all 0.3s ease;\n      \n      &:hover {\n        border-color: #ff4d4f;\n        background: #fff2f0;\n        transform: scale(1.1);\n      }\n    }\n  }\n`;\n\nconst StatusBadge = styled.div<{ type: 'hot' | 'new' | 'sale' | 'vip' }>`\n  position: absolute;\n  top: 16px;\n  left: 16px;\n  z-index: 2;\n  display: flex;\n  align-items: center;\n  gap: 6px;\n  padding: 6px 12px;\n  border-radius: 16px;\n  font-size: 12px;\n  font-weight: 700;\n  color: white;\n  backdrop-filter: blur(10px);\n  \n  ${props => {\n    switch (props.type) {\n      case 'hot':\n        return `\n          background: linear-gradient(135deg, #ff4d4f, #ff7875);\n          box-shadow: 0 4px 12px rgba(255, 77, 79, 0.4);\n        `;\n      case 'new':\n        return `\n          background: linear-gradient(135deg, #52c41a, #73d13d);\n          box-shadow: 0 4px 12px rgba(82, 196, 26, 0.4);\n        `;\n      case 'sale':\n        return `\n          background: linear-gradient(135deg, #faad14, #ffc069);\n          box-shadow: 0 4px 12px rgba(250, 173, 20, 0.4);\n        `;\n      case 'vip':\n        return `\n          background: linear-gradient(135deg, #722ed1, #9254de);\n          box-shadow: 0 4px 12px rgba(114, 46, 209, 0.4);\n        `;\n      default:\n        return '';\n    }\n  }}\n`;\n\nconst StockIndicator = styled.div<{ stock: number }>`\n  position: absolute;\n  bottom: 16px;\n  right: 16px;\n  z-index: 2;\n  padding: 4px 8px;\n  border-radius: 8px;\n  font-size: 11px;\n  font-weight: 600;\n  backdrop-filter: blur(10px);\n  \n  ${props => {\n    if (props.stock === 0) {\n      return `\n        background: rgba(255, 77, 79, 0.9);\n        color: white;\n      `;\n    } else if (props.stock <= 5) {\n      return `\n        background: rgba(250, 173, 20, 0.9);\n        color: white;\n      `;\n    } else {\n      return `\n        background: rgba(82, 196, 26, 0.9);\n        color: white;\n      `;\n    }\n  }}\n`;\n\ninterface EnhancedBookCardProps {\n  book: Book;\n  layout?: 'grid' | 'list';\n  showActions?: boolean;\n  size?: 'small' | 'default' | 'large';\n  className?: string;\n  style?: React.CSSProperties;\n}\n\nconst EnhancedBookCard: React.FC<EnhancedBookCardProps> = ({\n  book,\n  showActions = true,\n  size = 'default',\n  className,\n  style\n}) => {\n  const navigate = useNavigate();\n  const { addItem } = useCartStore();\n  const { isAuthenticated } = useAuthStore();\n\n  // 计算折扣（安全版本）\n  const discount = useMemo(() => {\n    if (!book.original_price || book.original_price <= book.price) {\n      return null;\n    }\n    return Math.round((1 - book.price / book.original_price) * 100);\n  }, [book.original_price, book.price]);\n\n  // 判断图书状态（优化性能）\n  const bookStatus = useMemo(() => {\n    const isHot = book.sales_count > 50 || book.views > 1000;\n    const isNew = new Date(book.created_at).getTime() > Date.now() - 7 * 24 * 60 * 60 * 1000;\n    const isOnSale = discount && discount > 20;\n    const isVip = book.price > 100;\n    \n    return { isHot, isNew, isOnSale, isVip };\n  }, [book.sales_count, book.views, book.created_at, book.price, discount]);\n\n  // 评分（使用稳定的算法避免每次渲染都变化）\n  const rating = useMemo(() => {\n    // 基于图书ID生成稳定的评分\n    const seed = book.id.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0);\n    return 4.2 + (seed % 80) / 100; // 4.2-4.99\n  }, [book.id]);\n\n  // 获取状况标签颜色\n  const getConditionColor = (condition: string) => {\n    const colorMap: Record<string, string> = {\n      '全新': '#52c41a',\n      '九成新': '#1677ff',\n      '八成新': '#faad14',\n      '七成新': '#ff7875',\n      '六成新': '#ff4d4f'\n    };\n    return colorMap[condition] || '#8c8c8c';\n  };\n\n  const handleAddToCart = (e: React.MouseEvent) => {\n    e.stopPropagation();\n    if (!isAuthenticated) {\n      message.warning('请先登录');\n      return;\n    }\n    \n    if (book.stock === 0) {\n      message.error('商品已售罄');\n      return;\n    }\n    \n    // 直接传入完整的 book 对象\n    addItem(book);\n    message.success('已添加到购物车');\n  };\n\n  const handleViewDetail = () => {\n    navigate(`/books/${book.id}`);\n  };\n\n  return (\n    <StyledCard\n      hoverable\n      className={`enhanced-book-card ${className || ''}`}\n      style={style}\n      cover={\n        <ImageContainer onClick={handleViewDetail}>\n          <LazyImage\n            src={book.cover_image || '/images/book-placeholder.png'}\n            alt={book.title}\n            className=\"book-image\"\n          />\n          \n          {/* 状态徽章 */}\n          {bookStatus.isHot && (\n            <StatusBadge type=\"hot\">\n              <FireOutlined />\n              热销\n            </StatusBadge>\n          )}\n          {bookStatus.isNew && !bookStatus.isHot && (\n            <StatusBadge type=\"new\">\n              <ThunderboltOutlined />\n              新品\n            </StatusBadge>\n          )}\n          {bookStatus.isOnSale && !bookStatus.isHot && !bookStatus.isNew && (\n            <StatusBadge type=\"sale\">\n              特惠 {discount}折\n            </StatusBadge>\n          )}\n          {bookStatus.isVip && !bookStatus.isHot && !bookStatus.isNew && !bookStatus.isOnSale && (\n            <StatusBadge type=\"vip\">\n              <CrownOutlined />\n              精品\n            </StatusBadge>\n          )}\n          \n          {/* 库存指示器 */}\n          <StockIndicator stock={book.stock}>\n            {book.stock === 0 ? '售罄' : book.stock <= 5 ? `仅剩${book.stock}本` : '现货'}\n          </StockIndicator>\n          \n          {/* 悬浮操作 */}\n          <div className=\"image-overlay\">\n            <div className=\"overlay-actions\">\n              <Tooltip title=\"查看详情\">\n                <Button\n                  variant=\"gradient\"\n                  gradient=\"primary\"\n                  size=\"large\"\n                  onClick={handleViewDetail}\n                  rounded\n                  style={{\n                    width: '48px',\n                    height: '48px',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center'\n                  }}\n                >\n                  <EyeOutlined />\n                </Button>\n              </Tooltip>\n              {showActions && (\n                <FavoriteButton\n                  bookId={book.id}\n                  size=\"large\"\n                  type=\"default\"\n                />\n              )}\n            </div>\n          </div>\n        </ImageContainer>\n      }\n    >\n      <BookInfo>\n        <div className=\"book-title\" onClick={handleViewDetail}>\n          {book.title}\n        </div>\n        \n        <div className=\"book-author\">\n          <Text type=\"secondary\">{book.author || '未知作者'}</Text>\n        </div>\n        \n        <div className=\"book-meta\">\n          <Tag \n            className=\"condition-tag\"\n            color={getConditionColor(book.condition)}\n          >\n            {book.condition}\n          </Tag>\n          \n          <div className=\"stats\">\n            <div className=\"stat-item\">\n              <EyeOutlined />\n              {book.views}\n            </div>\n            <div className=\"stat-item\">\n              已售 {book.sales_count}\n            </div>\n          </div>\n        </div>\n        \n        <div className=\"price-section\">\n          <div className=\"price-info\">\n            <span className=\"current-price\">¥{book.price}</span>\n            {book.original_price && book.original_price > book.price && (\n              <>\n                <span className=\"original-price\">¥{book.original_price}</span>\n                {discount && (\n                  <span className=\"discount\">{discount}折</span>\n                )}\n              </>\n            )}\n          </div>\n          \n          <div className=\"rating\">\n            <StarFilled />\n            {rating.toFixed(1)}\n          </div>\n        </div>\n        \n        {showActions && (\n          <div className=\"actions\">\n            <Button\n              variant=\"gradient\"\n              gradient=\"primary\"\n              size=\"medium\"\n              onClick={handleAddToCart}\n              disabled={book.stock === 0}\n              rounded\n              elevated\n            >\n              <ShoppingCartOutlined /> {book.stock === 0 ? '已售罄' : '加入购物车'}\n            </Button>\n            \n            <FavoriteButton\n              bookId={book.id}\n              className=\"favorite-btn\"\n            />\n          </div>\n        )}\n      </BookInfo>\n    </StyledCard>\n  );\n};\n\nexport default EnhancedBookCard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAoB,OAAO;AACvC,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,GAAG,EAASC,UAAU,EAAEC,OAAO,EAAEC,OAAO,QAAc,MAAM;AACrE,SACEC,oBAAoB,EACpBC,WAAW,EACXC,YAAY,EACZC,aAAa,EACbC,mBAAmB,EAEnBC,UAAU,QAGL,mBAAmB;AAC1B,OAAOC,MAAM,MAAM,mBAAmB;AAEtC,SAASC,YAAY,QAAQ,wBAAwB;AACrD,SAASC,YAAY,QAAQ,wBAAwB;AACrD,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,OAAOC,SAAS,MAAM,iBAAiB;AACvC,OAAOC,MAAM,MAAM,cAAc;AACjC,OAAOC,IAAI,MAAM,YAAY;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAG9B,MAAM;EAAEC;AAAK,CAAC,GAAGpB,UAAU;AAE3B,MAAMqB,UAAU,GAAGZ,MAAM,CAACM,IAAI,CAAC;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACO,EAAA,GAvBID,UAAU;AAyBhB,MAAME,cAAc,GAAGd,MAAM,CAACe,GAAG;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GArDIF,cAAc;AAuDpB,MAAMG,QAAQ,GAAGjB,MAAM,CAACe,GAAG;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACG,GAAA,GAhJID,QAAQ;AAkJd,MAAME,WAAW,GAAGnB,MAAM,CAACe,GAA6C;AACxE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIK,KAAK,IAAI;EACT,QAAQA,KAAK,CAACC,IAAI;IAChB,KAAK,KAAK;MACR,OAAO;AACf;AACA;AACA,SAAS;IACH,KAAK,KAAK;MACR,OAAO;AACf;AACA;AACA,SAAS;IACH,KAAK,MAAM;MACT,OAAO;AACf;AACA;AACA,SAAS;IACH,KAAK,KAAK;MACR,OAAO;AACf;AACA;AACA,SAAS;IACH;MACE,OAAO,EAAE;EACb;AACF,CAAC;AACH,CAAC;AAACC,GAAA,GAzCIH,WAAW;AA2CjB,MAAMI,cAAc,GAAGvB,MAAM,CAACe,GAAsB;AACpD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIK,KAAK,IAAI;EACT,IAAIA,KAAK,CAACI,KAAK,KAAK,CAAC,EAAE;IACrB,OAAO;AACb;AACA;AACA,OAAO;EACH,CAAC,MAAM,IAAIJ,KAAK,CAACI,KAAK,IAAI,CAAC,EAAE;IAC3B,OAAO;AACb;AACA;AACA,OAAO;EACH,CAAC,MAAM;IACL,OAAO;AACb;AACA;AACA,OAAO;EACH;AACF,CAAC;AACH,CAAC;AAACC,GAAA,GA7BIF,cAAc;AAwCpB,MAAMG,gBAAiD,GAAGA,CAAC;EACzDC,IAAI;EACJC,WAAW,GAAG,IAAI;EAClBC,IAAI,GAAG,SAAS;EAChBC,SAAS;EACTC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAMC,QAAQ,GAAG5C,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAE6C;EAAQ,CAAC,GAAGjC,YAAY,CAAC,CAAC;EAClC,MAAM;IAAEkC;EAAgB,CAAC,GAAGjC,YAAY,CAAC,CAAC;;EAE1C;EACA,MAAMkC,QAAQ,GAAGC,OAAO,CAAC,MAAM;IAC7B,IAAI,CAACV,IAAI,CAACW,cAAc,IAAIX,IAAI,CAACW,cAAc,IAAIX,IAAI,CAACY,KAAK,EAAE;MAC7D,OAAO,IAAI;IACb;IACA,OAAOC,IAAI,CAACC,KAAK,CAAC,CAAC,CAAC,GAAGd,IAAI,CAACY,KAAK,GAAGZ,IAAI,CAACW,cAAc,IAAI,GAAG,CAAC;EACjE,CAAC,EAAE,CAACX,IAAI,CAACW,cAAc,EAAEX,IAAI,CAACY,KAAK,CAAC,CAAC;;EAErC;EACA,MAAMG,UAAU,GAAGL,OAAO,CAAC,MAAM;IAC/B,MAAMM,KAAK,GAAGhB,IAAI,CAACiB,WAAW,GAAG,EAAE,IAAIjB,IAAI,CAACkB,KAAK,GAAG,IAAI;IACxD,MAAMC,KAAK,GAAG,IAAIC,IAAI,CAACpB,IAAI,CAACqB,UAAU,CAAC,CAACC,OAAO,CAAC,CAAC,GAAGF,IAAI,CAACG,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI;IACxF,MAAMC,QAAQ,GAAGf,QAAQ,IAAIA,QAAQ,GAAG,EAAE;IAC1C,MAAMgB,KAAK,GAAGzB,IAAI,CAACY,KAAK,GAAG,GAAG;IAE9B,OAAO;MAAEI,KAAK;MAAEG,KAAK;MAAEK,QAAQ;MAAEC;IAAM,CAAC;EAC1C,CAAC,EAAE,CAACzB,IAAI,CAACiB,WAAW,EAAEjB,IAAI,CAACkB,KAAK,EAAElB,IAAI,CAACqB,UAAU,EAAErB,IAAI,CAACY,KAAK,EAAEH,QAAQ,CAAC,CAAC;;EAEzE;EACA,MAAMiB,MAAM,GAAGhB,OAAO,CAAC,MAAM;IAC3B;IACA,MAAMiB,IAAI,GAAG3B,IAAI,CAAC4B,EAAE,CAACC,KAAK,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAACC,GAAG,EAAEC,IAAI,KAAKD,GAAG,GAAGC,IAAI,CAACC,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACjF,OAAO,GAAG,GAAIN,IAAI,GAAG,EAAE,GAAI,GAAG,CAAC,CAAC;EAClC,CAAC,EAAE,CAAC3B,IAAI,CAAC4B,EAAE,CAAC,CAAC;;EAEb;EACA,MAAMM,iBAAiB,GAAIC,SAAiB,IAAK;IAC/C,MAAMC,QAAgC,GAAG;MACvC,IAAI,EAAE,SAAS;MACf,KAAK,EAAE,SAAS;MAChB,KAAK,EAAE,SAAS;MAChB,KAAK,EAAE,SAAS;MAChB,KAAK,EAAE;IACT,CAAC;IACD,OAAOA,QAAQ,CAACD,SAAS,CAAC,IAAI,SAAS;EACzC,CAAC;EAED,MAAME,eAAe,GAAIC,CAAmB,IAAK;IAC/CA,CAAC,CAACC,eAAe,CAAC,CAAC;IACnB,IAAI,CAAC/B,eAAe,EAAE;MACpB3C,OAAO,CAAC2E,OAAO,CAAC,MAAM,CAAC;MACvB;IACF;IAEA,IAAIxC,IAAI,CAACH,KAAK,KAAK,CAAC,EAAE;MACpBhC,OAAO,CAAC4E,KAAK,CAAC,OAAO,CAAC;MACtB;IACF;;IAEA;IACAlC,OAAO,CAACP,IAAI,CAAC;IACbnC,OAAO,CAAC6E,OAAO,CAAC,SAAS,CAAC;EAC5B,CAAC;EAED,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;IAC7BrC,QAAQ,CAAC,UAAUN,IAAI,CAAC4B,EAAE,EAAE,CAAC;EAC/B,CAAC;EAED,oBACE/C,OAAA,CAACI,UAAU;IACT2D,SAAS;IACTzC,SAAS,EAAE,sBAAsBA,SAAS,IAAI,EAAE,EAAG;IACnDC,KAAK,EAAEA,KAAM;IACbyC,KAAK,eACHhE,OAAA,CAACM,cAAc;MAAC2D,OAAO,EAAEH,gBAAiB;MAAAI,QAAA,gBACxClE,OAAA,CAACJ,SAAS;QACRuE,GAAG,EAAEhD,IAAI,CAACiD,WAAW,IAAI,8BAA+B;QACxDC,GAAG,EAAElD,IAAI,CAACmD,KAAM;QAChBhD,SAAS,EAAC;MAAY;QAAAiD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvB,CAAC,EAGDxC,UAAU,CAACC,KAAK,iBACfnC,OAAA,CAACW,WAAW;QAACE,IAAI,EAAC,KAAK;QAAAqD,QAAA,gBACrBlE,OAAA,CAACZ,YAAY;UAAAmF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAElB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CACd,EACAxC,UAAU,CAACI,KAAK,IAAI,CAACJ,UAAU,CAACC,KAAK,iBACpCnC,OAAA,CAACW,WAAW;QAACE,IAAI,EAAC,KAAK;QAAAqD,QAAA,gBACrBlE,OAAA,CAACV,mBAAmB;UAAAiF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAEzB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CACd,EACAxC,UAAU,CAACS,QAAQ,IAAI,CAACT,UAAU,CAACC,KAAK,IAAI,CAACD,UAAU,CAACI,KAAK,iBAC5DtC,OAAA,CAACW,WAAW;QAACE,IAAI,EAAC,MAAM;QAAAqD,QAAA,GAAC,eACpB,EAACtC,QAAQ,EAAC,QACf;MAAA;QAAA2C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CACd,EACAxC,UAAU,CAACU,KAAK,IAAI,CAACV,UAAU,CAACC,KAAK,IAAI,CAACD,UAAU,CAACI,KAAK,IAAI,CAACJ,UAAU,CAACS,QAAQ,iBACjF3C,OAAA,CAACW,WAAW;QAACE,IAAI,EAAC,KAAK;QAAAqD,QAAA,gBACrBlE,OAAA,CAACX,aAAa;UAAAkF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAEnB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CACd,eAGD1E,OAAA,CAACe,cAAc;QAACC,KAAK,EAAEG,IAAI,CAACH,KAAM;QAAAkD,QAAA,EAC/B/C,IAAI,CAACH,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGG,IAAI,CAACH,KAAK,IAAI,CAAC,GAAG,KAAKG,IAAI,CAACH,KAAK,GAAG,GAAG;MAAI;QAAAuD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxD,CAAC,eAGjB1E,OAAA;QAAKsB,SAAS,EAAC,eAAe;QAAA4C,QAAA,eAC5BlE,OAAA;UAAKsB,SAAS,EAAC,iBAAiB;UAAA4C,QAAA,gBAC9BlE,OAAA,CAACf,OAAO;YAACqF,KAAK,EAAC,0BAAM;YAAAJ,QAAA,eACnBlE,OAAA,CAACH,MAAM;cACL8E,OAAO,EAAC,UAAU;cAClBC,QAAQ,EAAC,SAAS;cAClBvD,IAAI,EAAC,OAAO;cACZ4C,OAAO,EAAEH,gBAAiB;cAC1Be,OAAO;cACPtD,KAAK,EAAE;gBACLuD,KAAK,EAAE,MAAM;gBACbC,MAAM,EAAE,MAAM;gBACdC,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpBC,cAAc,EAAE;cAClB,CAAE;cAAAhB,QAAA,eAEFlE,OAAA,CAACb,WAAW;gBAAAoF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,EACTtD,WAAW,iBACVpB,OAAA,CAACL,cAAc;YACbwF,MAAM,EAAEhE,IAAI,CAAC4B,EAAG;YAChB1B,IAAI,EAAC,OAAO;YACZR,IAAI,EAAC;UAAS;YAAA0D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CACF;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACQ,CACjB;IAAAR,QAAA,eAEDlE,OAAA,CAACS,QAAQ;MAAAyD,QAAA,gBACPlE,OAAA;QAAKsB,SAAS,EAAC,YAAY;QAAC2C,OAAO,EAAEH,gBAAiB;QAAAI,QAAA,EACnD/C,IAAI,CAACmD;MAAK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC,eAEN1E,OAAA;QAAKsB,SAAS,EAAC,aAAa;QAAA4C,QAAA,eAC1BlE,OAAA,CAACG,IAAI;UAACU,IAAI,EAAC,WAAW;UAAAqD,QAAA,EAAE/C,IAAI,CAACiE,MAAM,IAAI;QAAM;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClD,CAAC,eAEN1E,OAAA;QAAKsB,SAAS,EAAC,WAAW;QAAA4C,QAAA,gBACxBlE,OAAA,CAAClB,GAAG;UACFwC,SAAS,EAAC,eAAe;UACzB+D,KAAK,EAAEhC,iBAAiB,CAAClC,IAAI,CAACmC,SAAS,CAAE;UAAAY,QAAA,EAExC/C,IAAI,CAACmC;QAAS;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CAAC,eAEN1E,OAAA;UAAKsB,SAAS,EAAC,OAAO;UAAA4C,QAAA,gBACpBlE,OAAA;YAAKsB,SAAS,EAAC,WAAW;YAAA4C,QAAA,gBACxBlE,OAAA,CAACb,WAAW;cAAAoF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,EACdvD,IAAI,CAACkB,KAAK;UAAA;YAAAkC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eACN1E,OAAA;YAAKsB,SAAS,EAAC,WAAW;YAAA4C,QAAA,GAAC,eACtB,EAAC/C,IAAI,CAACiB,WAAW;UAAA;YAAAmC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN1E,OAAA;QAAKsB,SAAS,EAAC,eAAe;QAAA4C,QAAA,gBAC5BlE,OAAA;UAAKsB,SAAS,EAAC,YAAY;UAAA4C,QAAA,gBACzBlE,OAAA;YAAMsB,SAAS,EAAC,eAAe;YAAA4C,QAAA,GAAC,MAAC,EAAC/C,IAAI,CAACY,KAAK;UAAA;YAAAwC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,EACnDvD,IAAI,CAACW,cAAc,IAAIX,IAAI,CAACW,cAAc,GAAGX,IAAI,CAACY,KAAK,iBACtD/B,OAAA,CAAAE,SAAA;YAAAgE,QAAA,gBACElE,OAAA;cAAMsB,SAAS,EAAC,gBAAgB;cAAA4C,QAAA,GAAC,MAAC,EAAC/C,IAAI,CAACW,cAAc;YAAA;cAAAyC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,EAC7D9C,QAAQ,iBACP5B,OAAA;cAAMsB,SAAS,EAAC,UAAU;cAAA4C,QAAA,GAAEtC,QAAQ,EAAC,QAAC;YAAA;cAAA2C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAC7C;UAAA,eACD,CACH;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAEN1E,OAAA;UAAKsB,SAAS,EAAC,QAAQ;UAAA4C,QAAA,gBACrBlE,OAAA,CAACT,UAAU;YAAAgF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EACb7B,MAAM,CAACyC,OAAO,CAAC,CAAC,CAAC;QAAA;UAAAf,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAELtD,WAAW,iBACVpB,OAAA;QAAKsB,SAAS,EAAC,SAAS;QAAA4C,QAAA,gBACtBlE,OAAA,CAACH,MAAM;UACL8E,OAAO,EAAC,UAAU;UAClBC,QAAQ,EAAC,SAAS;UAClBvD,IAAI,EAAC,QAAQ;UACb4C,OAAO,EAAET,eAAgB;UACzB+B,QAAQ,EAAEpE,IAAI,CAACH,KAAK,KAAK,CAAE;UAC3B6D,OAAO;UACPW,QAAQ;UAAAtB,QAAA,gBAERlE,OAAA,CAACd,oBAAoB;YAAAqF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,KAAC,EAACvD,IAAI,CAACH,KAAK,KAAK,CAAC,GAAG,KAAK,GAAG,OAAO;QAAA;UAAAuD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtD,CAAC,eAET1E,OAAA,CAACL,cAAc;UACbwF,MAAM,EAAEhE,IAAI,CAAC4B,EAAG;UAChBzB,SAAS,EAAC;QAAc;UAAAiD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACO;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEjB,CAAC;AAAClD,EAAA,CAvNIN,gBAAiD;EAAA,QAOpCrC,WAAW,EACRY,YAAY,EACJC,YAAY;AAAA;AAAA+F,GAAA,GATpCvE,gBAAiD;AAyNvD,eAAeA,gBAAgB;AAAC,IAAAb,EAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAI,GAAA,EAAAG,GAAA,EAAAwE,GAAA;AAAAC,YAAA,CAAArF,EAAA;AAAAqF,YAAA,CAAAlF,GAAA;AAAAkF,YAAA,CAAAhF,GAAA;AAAAgF,YAAA,CAAA5E,GAAA;AAAA4E,YAAA,CAAAzE,GAAA;AAAAyE,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}