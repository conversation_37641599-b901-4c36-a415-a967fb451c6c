const express = require('express');
const { authenticateToken, requireAdmin } = require('../middleware/authEnhanced');
const { getCacheStats } = require('../middleware/cache');
const cacheService = require('../services/cacheService');
const { cacheManager } = require('../config/redis');

const router = express.Router();

// 获取缓存统计信息
router.get('/stats', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { date } = req.query;
    const stats = await getCacheStats(date);
    
    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    console.error('获取缓存统计错误:', error);
    res.status(500).json({
      success: false,
      message: '获取缓存统计失败'
    });
  }
});

// 获取缓存信息
router.get('/info', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const info = await cacheService.getCacheInfo();
    
    res.json({
      success: true,
      data: info
    });
  } catch (error) {
    console.error('获取缓存信息错误:', error);
    res.status(500).json({
      success: false,
      message: '获取缓存信息失败'
    });
  }
});

// 获取热点数据
router.get('/hot/:type', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { type } = req.params;
    const { limit = 10 } = req.query;
    
    const hotItems = await cacheManager.getHotItems(type, parseInt(limit));
    
    res.json({
      success: true,
      data: {
        type,
        items: hotItems
      }
    });
  } catch (error) {
    console.error('获取热点数据错误:', error);
    res.status(500).json({
      success: false,
      message: '获取热点数据失败'
    });
  }
});

// 清除特定缓存
router.delete('/clear/:pattern', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { pattern } = req.params;
    
    // 安全检查，防止清除所有缓存
    const allowedPatterns = ['book:', 'user:', 'order:', 'search:', 'trending:'];
    const isAllowed = allowedPatterns.some(p => pattern.startsWith(p));
    
    if (!isAllowed) {
      return res.status(400).json({
        success: false,
        message: '不允许的缓存模式'
      });
    }
    
    const result = await cacheManager.delPattern(pattern + '*');
    
    res.json({
      success: true,
      message: '缓存已清除',
      data: { pattern }
    });
  } catch (error) {
    console.error('清除缓存错误:', error);
    res.status(500).json({
      success: false,
      message: '清除缓存失败'
    });
  }
});

// 预热缓存
router.post('/warmup', authenticateToken, requireAdmin, async (req, res) => {
  try {
    await cacheService.warmupCache();
    
    res.json({
      success: true,
      message: '缓存预热完成'
    });
  } catch (error) {
    console.error('缓存预热错误:', error);
    res.status(500).json({
      success: false,
      message: '缓存预热失败'
    });
  }
});

// 刷新特定缓存
router.post('/refresh/:type/:id', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { type, id } = req.params;
    
    let refreshed = false;
    
    switch (type) {
      case 'book':
        await cacheService.deleteBook(id);
        const book = await require('../models').Book.findByPk(id);
        if (book) {
          await cacheService.setBook(id, book);
          refreshed = true;
        }
        break;
        
      case 'user':
        await cacheService.deleteUser(id);
        const user = await require('../models').User.findByPk(id);
        if (user) {
          await cacheService.setUser(id, user);
          refreshed = true;
        }
        break;
        
      case 'category':
        await cacheService.invalidateCategories();
        refreshed = true;
        break;
        
      default:
        return res.status(400).json({
          success: false,
          message: '不支持的缓存类型'
        });
    }
    
    res.json({
      success: true,
      message: refreshed ? '缓存已刷新' : '未找到数据',
      data: { type, id }
    });
  } catch (error) {
    console.error('刷新缓存错误:', error);
    res.status(500).json({
      success: false,
      message: '刷新缓存失败'
    });
  }
});

// 获取缓存键列表
router.get('/keys/:pattern', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { pattern } = req.params;
    const { limit = 100 } = req.query;
    
    const keys = await cacheManager.redis.keys(pattern);
    const limitedKeys = keys.slice(0, parseInt(limit));
    
    res.json({
      success: true,
      data: {
        pattern,
        total: keys.length,
        keys: limitedKeys
      }
    });
  } catch (error) {
    console.error('获取缓存键错误:', error);
    res.status(500).json({
      success: false,
      message: '获取缓存键失败'
    });
  }
});

// 获取特定缓存值
router.get('/value/:key', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { key } = req.params;
    
    const value = await cacheManager.get(key);
    const ttl = await cacheManager.ttl(key);
    
    res.json({
      success: true,
      data: {
        key,
        value,
        ttl,
        exists: value !== null
      }
    });
  } catch (error) {
    console.error('获取缓存值错误:', error);
    res.status(500).json({
      success: false,
      message: '获取缓存值失败'
    });
  }
});

module.exports = router;