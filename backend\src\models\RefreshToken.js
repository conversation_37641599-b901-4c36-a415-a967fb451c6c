const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');
const crypto = require('crypto');

const RefreshToken = sequelize.define('RefreshToken', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  token: {
    type: DataTypes.STRING(512),
    unique: true,
    allowNull: false,
    comment: '刷新令牌（哈希值）'
  },
  user_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    },
    comment: '用户ID'
  },
  device_id: {
    type: DataTypes.STRING(255),
    comment: '设备标识符'
  },
  device_name: {
    type: DataTypes.STRING(255),
    comment: '设备名称'
  },
  ip_address: {
    type: DataTypes.STRING(45),
    comment: 'IP地址'
  },
  user_agent: {
    type: DataTypes.TEXT,
    comment: 'User Agent'
  },
  last_used_at: {
    type: DataTypes.DATE,
    comment: '最后使用时间'
  },
  expires_at: {
    type: DataTypes.DATE,
    allowNull: false,
    comment: '过期时间'
  },
  is_active: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
    comment: '是否有效'
  },
  created_by_refresh: {
    type: DataTypes.UUID,
    comment: '由哪个刷新令牌创建（用于链式追踪）'
  }
}, {
  tableName: 'refresh_tokens',
  timestamps: true,
  underscored: true,
  indexes: [
    {
      fields: ['token'],
      unique: true
    },
    {
      fields: ['user_id']
    },
    {
      fields: ['device_id']
    },
    {
      fields: ['expires_at']
    },
    {
      fields: ['is_active']
    }
  ]
});

// 定义关联
RefreshToken.associate = (models) => {
  RefreshToken.belongsTo(models.User, {
    foreignKey: 'user_id',
    as: 'user'
  });
};

// 实例方法：验证令牌
RefreshToken.prototype.verify = function(plainToken) {
  const hash = crypto.createHash('sha256').update(plainToken).digest('hex');
  return this.token === hash;
};

// 实例方法：标记为已使用
RefreshToken.prototype.markAsUsed = async function() {
  this.last_used_at = new Date();
  await this.save();
};

// 实例方法：撤销令牌
RefreshToken.prototype.revoke = async function() {
  this.is_active = false;
  await this.save();
};

// 类方法：生成新的刷新令牌
RefreshToken.generateToken = function() {
  return crypto.randomBytes(64).toString('hex');
};

// 类方法：创建刷新令牌记录
RefreshToken.createToken = async function(userId, deviceInfo = {}) {
  const plainToken = this.generateToken();
  const hashedToken = crypto.createHash('sha256').update(plainToken).digest('hex');
  
  const expiresAt = new Date();
  expiresAt.setDate(expiresAt.getDate() + 30); // 30天有效期
  
  const refreshToken = await this.create({
    token: hashedToken,
    user_id: userId,
    device_id: deviceInfo.deviceId,
    device_name: deviceInfo.deviceName,
    ip_address: deviceInfo.ipAddress,
    user_agent: deviceInfo.userAgent,
    expires_at: expiresAt
  });
  
  return {
    token: plainToken,
    tokenRecord: refreshToken
  };
};

// 类方法：清理过期的刷新令牌
RefreshToken.cleanupExpired = async function() {
  const count = await this.destroy({
    where: {
      expires_at: {
        [Op.lt]: new Date()
      }
    }
  });
  return count;
};

// 类方法：获取用户的活跃设备
RefreshToken.getUserDevices = async function(userId) {
  return await this.findAll({
    where: {
      user_id: userId,
      is_active: true,
      expires_at: {
        [Op.gt]: new Date()
      }
    },
    attributes: ['id', 'device_id', 'device_name', 'ip_address', 'last_used_at', 'created_at'],
    order: [['last_used_at', 'DESC']]
  });
};

// 类方法：撤销用户的所有刷新令牌
RefreshToken.revokeAllUserTokens = async function(userId) {
  const count = await this.update(
    { is_active: false },
    {
      where: {
        user_id: userId,
        is_active: true
      }
    }
  );
  return count[0];
};

// 类方法：撤销特定设备的令牌
RefreshToken.revokeDeviceTokens = async function(userId, deviceId) {
  const count = await this.update(
    { is_active: false },
    {
      where: {
        user_id: userId,
        device_id: deviceId,
        is_active: true
      }
    }
  );
  return count[0];
};

module.exports = RefreshToken;