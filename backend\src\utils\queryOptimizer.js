const { Op } = require('sequelize');

/**
 * 查询优化工具集
 * 解决N+1查询问题，优化数据库性能
 */

// 批量加载器基类
class DataLoader {
  constructor(batchLoadFn, options = {}) {
    this.batchLoadFn = batchLoadFn;
    this.options = {
      cache: true,
      maxBatchSize: 100,
      batchScheduleFn: callback => process.nextTick(callback),
      ...options
    };
    
    this.cache = new Map();
    this.queue = [];
    this.batchPromise = null;
  }

  async load(key) {
    if (this.options.cache && this.cache.has(key)) {
      return this.cache.get(key);
    }

    return new Promise((resolve, reject) => {
      this.queue.push({ key, resolve, reject });
      
      if (!this.batchPromise) {
        this.batchPromise = new Promise(batchResolve => {
          this.options.batchScheduleFn(() => {
            this.dispatchBatch();
            batchResolve();
          });
        });
      }
    });
  }

  async loadMany(keys) {
    return Promise.all(keys.map(key => this.load(key)));
  }

  async dispatchBatch() {
    const batch = this.queue.splice(0, this.options.maxBatchSize);
    this.batchPromise = null;

    if (batch.length === 0) return;

    const keys = batch.map(item => item.key);
    
    try {
      const results = await this.batchLoadFn(keys);
      
      // 创建键值映射
      const resultMap = new Map();
      results.forEach(result => {
        const key = this.getKey(result);
        resultMap.set(key, result);
      });
      
      // 解析每个请求
      batch.forEach(({ key, resolve }) => {
        const result = resultMap.get(key) || null;
        if (this.options.cache) {
          this.cache.set(key, result);
        }
        resolve(result);
      });
    } catch (error) {
      batch.forEach(({ reject }) => reject(error));
    }
  }

  getKey(result) {
    return result.id;
  }

  clearCache() {
    this.cache.clear();
  }
}

// 创建常用的数据加载器
const createLoaders = (models) => {
  const { User, Book, Category, Order, OrderItem } = models;

  // 用户加载器
  const userLoader = new DataLoader(async (userIds) => {
    return User.findAll({
      where: { id: userIds },
      attributes: ['id', 'username', 'phone', 'email', 'avatar', 'role', 'status']
    });
  });

  // 图书加载器
  const bookLoader = new DataLoader(async (bookIds) => {
    return Book.findAll({
      where: { id: bookIds },
      attributes: ['id', 'title', 'author', 'cover_image', 'price', 'condition', 'status']
    });
  });

  // 分类加载器
  const categoryLoader = new DataLoader(async (categoryIds) => {
    return Category.findAll({
      where: { id: categoryIds },
      attributes: ['id', 'name', 'description']
    });
  });

  // 订单项加载器（按订单ID）
  const orderItemsByOrderLoader = new DataLoader(
    async (orderIds) => {
      const items = await OrderItem.findAll({
        where: { order_id: orderIds },
        attributes: ['id', 'order_id', 'book_id', 'quantity', 'price', 'subtotal']
      });
      
      // 按订单ID分组
      const itemsByOrder = {};
      items.forEach(item => {
        if (!itemsByOrder[item.order_id]) {
          itemsByOrder[item.order_id] = [];
        }
        itemsByOrder[item.order_id].push(item);
      });
      
      // 返回与orderIds相同顺序的结果
      return orderIds.map(orderId => itemsByOrder[orderId] || []);
    },
    {
      getKey: (items) => items[0]?.order_id // 使用第一个项目的order_id作为键
    }
  );

  return {
    userLoader,
    bookLoader,
    categoryLoader,
    orderItemsByOrderLoader
  };
};

// 优化的查询构建器
class QueryBuilder {
  constructor(model, options = {}) {
    this.model = model;
    this.query = {
      where: {},
      attributes: options.defaultAttributes || undefined,
      order: options.defaultOrder || [['created_at', 'DESC']],
      limit: 20,
      offset: 0
    };
    this.includes = [];
  }

  where(conditions) {
    this.query.where = { ...this.query.where, ...conditions };
    return this;
  }

  select(attributes) {
    this.query.attributes = attributes;
    return this;
  }

  include(association) {
    this.includes.push(association);
    return this;
  }

  order(order) {
    this.query.order = order;
    return this;
  }

  paginate(page = 1, limit = 20) {
    this.query.limit = parseInt(limit);
    this.query.offset = (parseInt(page) - 1) * this.query.limit;
    return this;
  }

  // 优化的关联查询
  optimizedInclude(includes) {
    includes.forEach(inc => {
      if (inc.separate === undefined && inc.model) {
        // 对于一对多关系，使用separate可以避免笛卡尔积
        if (inc.association && ['hasMany', 'belongsToMany'].includes(inc.association.associationType)) {
          inc.separate = true;
        }
      }
      
      // 确保有属性选择以减少数据传输
      if (!inc.attributes) {
        inc.attributes = inc.defaultAttributes || [];
      }
      
      this.includes.push(inc);
    });
    return this;
  }

  async execute() {
    if (this.includes.length > 0) {
      this.query.include = this.includes;
    }
    
    return this.model.findAndCountAll(this.query);
  }

  async findOne() {
    if (this.includes.length > 0) {
      this.query.include = this.includes;
    }
    
    return this.model.findOne(this.query);
  }
}

// 预加载关联数据的辅助函数
async function preloadAssociations(records, associations) {
  const loadPromises = [];
  
  for (const [field, loader] of Object.entries(associations)) {
    const ids = [...new Set(records.map(r => r[field]).filter(Boolean))];
    if (ids.length > 0) {
      loadPromises.push(
        loader.loadMany(ids).then(results => ({ field, results }))
      );
    }
  }
  
  const loadedData = await Promise.all(loadPromises);
  
  // 将加载的数据附加到记录上
  loadedData.forEach(({ field, results }) => {
    const dataMap = new Map(results.filter(Boolean).map(r => [r.id, r]));
    records.forEach(record => {
      if (record[field]) {
        record.dataValues[field + '_data'] = dataMap.get(record[field]);
      }
    });
  });
  
  return records;
}

// 批量更新优化
async function bulkUpdateOptimized(model, updates, options = {}) {
  const { transaction, chunkSize = 100 } = options;
  
  // 将更新分块以避免超时
  const chunks = [];
  for (let i = 0; i < updates.length; i += chunkSize) {
    chunks.push(updates.slice(i, i + chunkSize));
  }
  
  const results = [];
  for (const chunk of chunks) {
    const chunkResults = await Promise.all(
      chunk.map(({ where, data }) => 
        model.update(data, { where, transaction })
      )
    );
    results.push(...chunkResults);
  }
  
  return results;
}

// 创建索引建议
const indexSuggestions = {
  books: [
    'CREATE INDEX idx_books_status_created ON books(status, created_at DESC);',
    'CREATE INDEX idx_books_category_status ON books(category_id, status);',
    'CREATE INDEX idx_books_price_status ON books(price, status) WHERE status = \'上架\';',
    'CREATE INDEX idx_books_search ON books USING gin(to_tsvector(\'simple\', title || \' \' || COALESCE(author, \'\')));'
  ],
  orders: [
    'CREATE INDEX idx_orders_user_status ON orders(user_id, status);',
    'CREATE INDEX idx_orders_created_status ON orders(created_at DESC, status);',
    'CREATE INDEX idx_order_items_order_book ON order_items(order_id, book_id);'
  ],
  users: [
    'CREATE INDEX idx_users_phone ON users(phone);',
    'CREATE INDEX idx_users_status ON users(status) WHERE status = \'active\';'
  ],
  messages: [
    'CREATE INDEX idx_messages_sender_receiver ON messages(sender_id, receiver_id);',
    'CREATE INDEX idx_messages_book_order ON messages(book_id, order_id) WHERE book_id IS NOT NULL OR order_id IS NOT NULL;'
  ]
};

// 查询性能监控
class QueryMonitor {
  constructor() {
    this.queries = [];
    this.slowQueryThreshold = 100; // 毫秒
  }

  async monitor(queryFn, queryName) {
    const start = Date.now();
    
    try {
      const result = await queryFn();
      const duration = Date.now() - start;
      
      this.queries.push({
        name: queryName,
        duration,
        timestamp: new Date(),
        slow: duration > this.slowQueryThreshold
      });
      
      if (duration > this.slowQueryThreshold) {
        console.warn(`Slow query detected: ${queryName} took ${duration}ms`);
      }
      
      return result;
    } catch (error) {
      const duration = Date.now() - start;
      this.queries.push({
        name: queryName,
        duration,
        timestamp: new Date(),
        error: error.message
      });
      throw error;
    }
  }

  getReport() {
    const totalQueries = this.queries.length;
    const slowQueries = this.queries.filter(q => q.slow).length;
    const errorQueries = this.queries.filter(q => q.error).length;
    const avgDuration = this.queries.reduce((sum, q) => sum + q.duration, 0) / totalQueries;
    
    return {
      totalQueries,
      slowQueries,
      errorQueries,
      avgDuration,
      queries: this.queries.slice(-100) // 最近100个查询
    };
  }

  reset() {
    this.queries = [];
  }
}

module.exports = {
  DataLoader,
  createLoaders,
  QueryBuilder,
  preloadAssociations,
  bulkUpdateOptimized,
  indexSuggestions,
  QueryMonitor
};