const Joi = require('joi');
const createDOMPurify = require('isomorphic-dompurify');

// HTML清洗函数
const sanitizeHtml = (value) => {
  if (typeof value !== 'string') return value;
  return createDOMPurify.sanitize(value, { 
    ALLOWED_TAGS: [],
    ALLOWED_ATTR: []
  });
};

// 用户注册验证
const registerSchema = Joi.object({
  phone: Joi.string()
    .pattern(/^1[3-9]\d{9}$/)
    .required()
    .messages({
      'string.pattern.base': '请输入有效的手机号码',
      'any.required': '手机号码是必填项'
    }),
  email: Joi.string()
    .email()
    .optional()
    .messages({
      'string.email': '请输入有效的邮箱地址'
    }),
  password: Joi.string()
    .min(6)
    .max(20)
    .required()
    .messages({
      'string.min': '密码长度至少6位',
      'string.max': '密码长度不能超过20位',
      'any.required': '密码是必填项'
    }),
  username: Joi.string()
    .alphanum()
    .min(3)
    .max(30)
    .optional()
    .messages({
      'string.alphanum': '用户名只能包含字母和数字',
      'string.min': '用户名长度至少3位',
      'string.max': '用户名长度不能超过30位'
    })
});

// 用户登录验证
const loginSchema = Joi.object({
  phone: Joi.string()
    .pattern(/^1[3-9]\d{9}$/)
    .required()
    .messages({
      'string.pattern.base': '请输入有效的手机号码',
      'any.required': '手机号码是必填项'
    }),
  password: Joi.string()
    .required()
    .messages({
      'any.required': '密码是必填项'
    })
});

// 图书创建验证
const createBookSchema = Joi.object({
  title: Joi.string()
    .max(200)
    .required()
    .messages({
      'string.max': '书名长度不能超过200字符',
      'any.required': '书名是必填项'
    }),
  author: Joi.string()
    .max(100)
    .optional()
    .messages({
      'string.max': '作者名长度不能超过100字符'
    }),
  publisher: Joi.string()
    .max(100)
    .optional()
    .messages({
      'string.max': '出版社名长度不能超过100字符'
    }),
  isbn: Joi.string()
    .pattern(/^(?:ISBN(?:-1[03])?:? )?(?=[0-9X]{10}$|(?=(?:[0-9]+[- ]){3})[- 0-9X]{13}$|97[89][0-9]{10}$|(?=(?:[0-9]+[- ]){4})[- 0-9]{17}$)(?:97[89][- ]?)?[0-9]{1,5}[- ]?[0-9]+[- ]?[0-9]+[- ]?[0-9X]$/)
    .optional()
    .messages({
      'string.pattern.base': '请输入有效的ISBN号码'
    }),
  category_id: Joi.string()
    .uuid()
    .required()
    .messages({
      'string.uuid': '分类ID格式不正确',
      'any.required': '分类是必填项'
    }),
  description: Joi.string()
    .max(1000)
    .optional()
    .messages({
      'string.max': '描述长度不能超过1000字符'
    }),
  condition: Joi.string()
    .valid('全新', '九成新', '八成新', '七成新', '六成新')
    .required()
    .messages({
      'any.only': '请选择有效的图书状况',
      'any.required': '图书状况是必填项'
    }),
  price: Joi.number()
    .positive()
    .precision(2)
    .required()
    .messages({
      'number.positive': '价格必须大于0',
      'any.required': '价格是必填项'
    }),
  original_price: Joi.number()
    .positive()
    .precision(2)
    .optional()
    .messages({
      'number.positive': '原价必须大于0'
    }),
  stock: Joi.number()
    .integer()
    .min(0)
    .required()
    .messages({
      'number.integer': '库存必须是整数',
      'number.min': '库存不能小于0',
      'any.required': '库存是必填项'
    })
});

// 订单创建验证
const createOrderSchema = Joi.object({
  items: Joi.array()
    .items(
      Joi.object({
        book_id: Joi.string().uuid().required(),
        quantity: Joi.number().integer().min(1).required()
      })
    )
    .min(1)
    .required()
    .messages({
      'array.min': '订单至少包含一个商品',
      'any.required': '商品列表是必填项'
    }),
  delivery_address: Joi.string()
    .max(500)
    .required()
    .messages({
      'string.max': '配送地址长度不能超过500字符',
      'any.required': '配送地址是必填项'
    }),
  delivery_phone: Joi.string()
    .pattern(/^1[3-9]\d{9}$/)
    .required()
    .messages({
      'string.pattern.base': '请输入有效的联系电话',
      'any.required': '联系电话是必填项'
    })
});

// 消息发送验证
const sendMessageSchema = Joi.object({
  content: Joi.string()
    .max(1000)
    .required()
    .messages({
      'string.max': '消息内容不能超过1000字符',
      'any.required': '消息内容是必填项'
    }),
  receiver_id: Joi.string()
    .uuid()
    .optional(),
  book_id: Joi.string()
    .uuid()
    .optional(),
  order_id: Joi.string()
    .uuid()
    .optional(),
  type: Joi.string()
    .valid('chat', 'book_inquiry', 'order_inquiry')
    .default('chat')
});

// 增强的验证中间件生成器
const validate = (schema) => {
  return (req, res, next) => {
    // 清洗输入数据
    const sanitizedBody = {};
    for (const [key, value] of Object.entries(req.body)) {
      if (typeof value === 'string') {
        sanitizedBody[key] = sanitizeHtml(value.trim());
      } else {
        sanitizedBody[key] = value;
      }
    }
    
    const { error, value } = schema.validate(sanitizedBody, {
      abortEarly: false,
      stripUnknown: true
    });
    
    if (error) {
      return res.status(400).json({
        success: false,
        message: '数据验证失败',
        errors: error.details.map(detail => ({
          field: detail.path.join('.'),
          message: detail.message
        }))
      });
    }
    
    // 使用清洗后的数据
    req.body = value;
    next();
  };
};

module.exports = {
  registerSchema,
  loginSchema,
  createBookSchema,
  createOrderSchema,
  sendMessageSchema,
  validate
};
