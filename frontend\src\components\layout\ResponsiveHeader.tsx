import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import MobileNav from './MobileNav';
import MobileSearch from '../ui/MobileSearch';
import { useAuth } from '../../stores/authStore';
import { useCart } from '../../stores/cartStore';
import '../../styles/responsive.css';

interface ResponsiveHeaderProps {
  onSearch?: (query: string) => void;
}

const ResponsiveHeader: React.FC<ResponsiveHeaderProps> = ({ onSearch }) => {
  const navigate = useNavigate();
  const { user, logout } = useAuth();
  const { items } = useCart();
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileNavOpen, setIsMobileNavOpen] = useState(false);
  const [showMobileSearch, setShowMobileSearch] = useState(false);

  const cartItemCount = items.reduce((total, item) => total + item.quantity, 0);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const handleSearch = (query: string) => {
    onSearch?.(query);
    navigate(`/books?search=${encodeURIComponent(query)}`);
    setShowMobileSearch(false);
  };

  return (
    <>
      {/* 桌面端 Header */}
      <header 
        className="d-none d-lg-block"
        style={{
          position: 'sticky',
          top: 0,
          zIndex: 100,
          backgroundColor: '#fff',
          borderBottom: '1px solid #e0e0e0',
          transition: 'box-shadow 0.3s ease',
          boxShadow: isScrolled ? '0 2px 8px rgba(0,0,0,0.1)' : 'none'
        }}
      >
        <div className="container-responsive">
          <div style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            height: '70px',
            padding: '0 20px'
          }}>
            {/* Logo */}
            <Link to="/" style={{
              display: 'flex',
              alignItems: 'center',
              gap: '10px',
              fontSize: '24px',
              fontWeight: 'bold',
              color: '#007bff',
              textDecoration: 'none'
            }}>
              <span>📚</span>
              <span>旧书买卖</span>
            </Link>

            {/* 搜索框 */}
            <div style={{
              flex: 1,
              maxWidth: '500px',
              margin: '0 30px'
            }}>
              <MobileSearch
                placeholder="搜索图书、作者、ISBN..."
                onSearch={handleSearch}
                suggestions={['高等数学', '线性代数', '大学英语', '数据结构']}
              />
            </div>

            {/* 导航链接 */}
            <nav style={{
              display: 'flex',
              alignItems: 'center',
              gap: '30px'
            }}>
              <Link to="/books" style={{
                color: '#333',
                textDecoration: 'none',
                fontWeight: '500',
                padding: '5px 0',
                borderBottom: '2px solid transparent',
                transition: 'all 0.3s'
              }}>
                图书分类
              </Link>
              <Link to="/about" style={{
                color: '#333',
                textDecoration: 'none',
                fontWeight: '500',
                padding: '5px 0',
                borderBottom: '2px solid transparent',
                transition: 'all 0.3s'
              }}>
                关于我们
              </Link>
            </nav>

            {/* 用户操作 */}
            <div style={{
              display: 'flex',
              alignItems: 'center',
              gap: '20px'
            }}>
              {/* 购物车 */}
              <Link to="/cart" style={{
                position: 'relative',
                color: '#333',
                textDecoration: 'none'
              }}>
                <span style={{ fontSize: '24px' }}>🛒</span>
                {cartItemCount > 0 && (
                  <span style={{
                    position: 'absolute',
                    top: '-8px',
                    right: '-8px',
                    backgroundColor: '#dc3545',
                    color: '#fff',
                    borderRadius: '10px',
                    padding: '2px 6px',
                    fontSize: '12px',
                    fontWeight: 'bold'
                  }}>
                    {cartItemCount > 99 ? '99+' : cartItemCount}
                  </span>
                )}
              </Link>

              {/* 用户信息 */}
              {user ? (
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '10px'
                }}>
                  <Link to="/profile" style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '8px',
                    color: '#333',
                    textDecoration: 'none'
                  }}>
                    <span style={{ fontSize: '20px' }}>👤</span>
                    <span>{user.username}</span>
                  </Link>
                  <button
                    onClick={logout}
                    style={{
                      backgroundColor: 'transparent',
                      border: '1px solid #dc3545',
                      color: '#dc3545',
                      padding: '6px 12px',
                      borderRadius: '4px',
                      cursor: 'pointer',
                      fontSize: '14px'
                    }}
                  >
                    退出
                  </button>
                </div>
              ) : (
                <div style={{
                  display: 'flex',
                  gap: '10px'
                }}>
                  <Link
                    to="/auth/login"
                    style={{
                      padding: '8px 16px',
                      color: '#007bff',
                      textDecoration: 'none',
                      fontWeight: '500'
                    }}
                  >
                    登录
                  </Link>
                  <Link
                    to="/auth/register"
                    style={{
                      padding: '8px 16px',
                      backgroundColor: '#007bff',
                      color: '#fff',
                      borderRadius: '4px',
                      textDecoration: 'none',
                      fontWeight: '500'
                    }}
                  >
                    注册
                  </Link>
                </div>
              )}
            </div>
          </div>
        </div>
      </header>

      {/* 移动端 Header */}
      <header 
        className="d-block d-lg-none"
        style={{
          position: 'sticky',
          top: 0,
          zIndex: 100,
          backgroundColor: '#fff',
          borderBottom: '1px solid #e0e0e0',
          transition: 'all 0.3s ease',
          boxShadow: isScrolled ? '0 2px 8px rgba(0,0,0,0.1)' : 'none'
        }}
      >
        <div style={{
          padding: '12px 16px',
          paddingTop: `calc(12px + var(--safe-area-inset-top))`
        }}>
          {!showMobileSearch ? (
            <div style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between'
            }}>
              {/* 菜单按钮 */}
              <button
                onClick={() => setIsMobileNavOpen(true)}
                className="btn-touch"
                style={{
                  backgroundColor: 'transparent',
                  border: 'none',
                  padding: '8px',
                  fontSize: '24px'
                }}
              >
                ☰
              </button>

              {/* Logo */}
              <Link to="/" style={{
                display: 'flex',
                alignItems: 'center',
                gap: '8px',
                fontSize: '20px',
                fontWeight: 'bold',
                color: '#007bff',
                textDecoration: 'none'
              }}>
                <span>📚</span>
                <span>旧书买卖</span>
              </Link>

              {/* 右侧操作 */}
              <div style={{
                display: 'flex',
                alignItems: 'center',
                gap: '12px'
              }}>
                {/* 搜索按钮 */}
                <button
                  onClick={() => setShowMobileSearch(true)}
                  className="btn-touch"
                  style={{
                    backgroundColor: 'transparent',
                    border: 'none',
                    padding: '8px',
                    fontSize: '20px'
                  }}
                >
                  🔍
                </button>

                {/* 购物车 */}
                <Link to="/cart" style={{
                  position: 'relative',
                  padding: '8px',
                  color: '#333'
                }}>
                  <span style={{ fontSize: '20px' }}>🛒</span>
                  {cartItemCount > 0 && (
                    <span style={{
                      position: 'absolute',
                      top: '0',
                      right: '0',
                      backgroundColor: '#dc3545',
                      color: '#fff',
                      borderRadius: '10px',
                      padding: '2px 6px',
                      fontSize: '11px',
                      fontWeight: 'bold',
                      minWidth: '18px',
                      textAlign: 'center'
                    }}>
                      {cartItemCount > 99 ? '99+' : cartItemCount}
                    </span>
                  )}
                </Link>
              </div>
            </div>
          ) : (
            <div style={{
              display: 'flex',
              alignItems: 'center',
              gap: '12px'
            }}>
              <button
                onClick={() => setShowMobileSearch(false)}
                className="btn-touch"
                style={{
                  backgroundColor: 'transparent',
                  border: 'none',
                  padding: '8px',
                  fontSize: '18px'
                }}
              >
                ←
              </button>
              <div style={{ flex: 1 }}>
                <MobileSearch
                  placeholder="搜索图书..."
                  onSearch={handleSearch}
                  suggestions={['高等数学', '线性代数', '大学英语', '数据结构']}
                  autoFocus
                />
              </div>
            </div>
          )}
        </div>
      </header>

      {/* 移动端导航抽屉 */}
      <MobileNav 
        isOpen={isMobileNavOpen}
        onClose={() => setIsMobileNavOpen(false)}
      />
    </>
  );
};

export default ResponsiveHeader;