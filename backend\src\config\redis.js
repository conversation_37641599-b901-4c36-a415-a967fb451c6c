const redis = require('redis');
const { promisify } = require('util');

// Redis 客户端配置
const redisConfig = {
  host: process.env.REDIS_HOST || 'localhost',
  port: process.env.REDIS_PORT || 6379,
  password: process.env.REDIS_PASSWORD || undefined,
  db: process.env.REDIS_DB || 0,
  retry_strategy: (options) => {
    if (options.error && options.error.code === 'ECONNREFUSED') {
      console.error('Redis 连接被拒绝');
      return new Error('Redis 服务不可用');
    }
    if (options.total_retry_time > 1000 * 60 * 60) {
      console.error('Redis 重试超时');
      return new Error('Redis 重试超时');
    }
    if (options.attempt > 10) {
      return undefined;
    }
    // 重连间隔
    return Math.min(options.attempt * 100, 3000);
  }
};

// 创建Redis客户端
const client = redis.createClient(redisConfig);

// 错误处理
client.on('error', (err) => {
  console.error('Redis 错误:', err);
});

client.on('connect', () => {
  console.log('Redis 已连接');
});

client.on('ready', () => {
  console.log('Redis 准备就绪');
});

// Promise化Redis方法
const getAsync = promisify(client.get).bind(client);
const setAsync = promisify(client.set).bind(client);
const delAsync = promisify(client.del).bind(client);
const existsAsync = promisify(client.exists).bind(client);
const expireAsync = promisify(client.expire).bind(client);
const ttlAsync = promisify(client.ttl).bind(client);
const mgetAsync = promisify(client.mget).bind(client);
const msetAsync = promisify(client.mset).bind(client);
const keysAsync = promisify(client.keys).bind(client);
const scanAsync = promisify(client.scan).bind(client);
const incrAsync = promisify(client.incr).bind(client);
const decrAsync = promisify(client.decr).bind(client);
const hgetAsync = promisify(client.hget).bind(client);
const hsetAsync = promisify(client.hset).bind(client);
const hgetallAsync = promisify(client.hgetall).bind(client);
const hdelAsync = promisify(client.hdel).bind(client);
const saddAsync = promisify(client.sadd).bind(client);
const sremAsync = promisify(client.srem).bind(client);
const smembersAsync = promisify(client.smembers).bind(client);
const sismemberAsync = promisify(client.sismember).bind(client);
const zaddAsync = promisify(client.zadd).bind(client);
const zremAsync = promisify(client.zrem).bind(client);
const zrangeAsync = promisify(client.zrange).bind(client);
const zrevrangeAsync = promisify(client.zrevrange).bind(client);
const flushdbAsync = promisify(client.flushdb).bind(client);

// 缓存键前缀
const CACHE_PREFIX = {
  BOOK: 'book:',
  USER: 'user:',
  CATEGORY: 'category:',
  ORDER: 'order:',
  SESSION: 'session:',
  BLACKLIST: 'blacklist:',
  RATE_LIMIT: 'ratelimit:',
  TRENDING: 'trending:',
  SEARCH: 'search:',
  STATS: 'stats:',
  LOCK: 'lock:'
};

// 缓存过期时间（秒）
const CACHE_TTL = {
  SHORT: 60,           // 1分钟
  MEDIUM: 300,         // 5分钟
  LONG: 3600,          // 1小时
  DAY: 86400,          // 1天
  WEEK: 604800,        // 1周
  MONTH: 2592000       // 30天
};

// 缓存管理类
class CacheManager {
  constructor() {
    this.client = client;
  }

  // 生成缓存键
  generateKey(prefix, id, suffix = '') {
    return `${prefix}${id}${suffix ? ':' + suffix : ''}`;
  }

  // 获取缓存
  async get(key) {
    try {
      const data = await getAsync(key);
      return data ? JSON.parse(data) : null;
    } catch (error) {
      console.error('缓存获取错误:', error);
      return null;
    }
  }

  // 设置缓存
  async set(key, value, ttl = CACHE_TTL.MEDIUM) {
    try {
      const data = JSON.stringify(value);
      await setAsync(key, data, 'EX', ttl);
      return true;
    } catch (error) {
      console.error('缓存设置错误:', error);
      return false;
    }
  }

  // 删除缓存
  async del(key) {
    try {
      await delAsync(key);
      return true;
    } catch (error) {
      console.error('缓存删除错误:', error);
      return false;
    }
  }

  // 批量删除匹配的键
  async delPattern(pattern) {
    try {
      const keys = await keysAsync(pattern);
      if (keys.length > 0) {
        await delAsync(keys);
      }
      return true;
    } catch (error) {
      console.error('批量删除缓存错误:', error);
      return false;
    }
  }

  // 检查缓存是否存在
  async exists(key) {
    try {
      return await existsAsync(key);
    } catch (error) {
      console.error('缓存检查错误:', error);
      return false;
    }
  }

  // 设置过期时间
  async expire(key, seconds) {
    try {
      return await expireAsync(key, seconds);
    } catch (error) {
      console.error('设置过期时间错误:', error);
      return false;
    }
  }

  // 获取剩余过期时间
  async ttl(key) {
    try {
      return await ttlAsync(key);
    } catch (error) {
      console.error('获取TTL错误:', error);
      return -1;
    }
  }

  // 缓存计数器增加
  async incr(key) {
    try {
      return await incrAsync(key);
    } catch (error) {
      console.error('计数器增加错误:', error);
      return null;
    }
  }

  // 缓存计数器减少
  async decr(key) {
    try {
      return await decrAsync(key);
    } catch (error) {
      console.error('计数器减少错误:', error);
      return null;
    }
  }

  // 分布式锁
  async acquireLock(resource, ttl = 30) {
    const lockKey = `${CACHE_PREFIX.LOCK}${resource}`;
    const identifier = Date.now() + ':' + Math.random();
    
    try {
      const result = await setAsync(lockKey, identifier, 'NX', 'EX', ttl);
      return result ? identifier : false;
    } catch (error) {
      console.error('获取锁错误:', error);
      return false;
    }
  }

  // 释放锁
  async releaseLock(resource, identifier) {
    const lockKey = `${CACHE_PREFIX.LOCK}${resource}`;
    
    try {
      const current = await getAsync(lockKey);
      if (current === identifier) {
        await delAsync(lockKey);
        return true;
      }
      return false;
    } catch (error) {
      console.error('释放锁错误:', error);
      return false;
    }
  }

  // 缓存穿透保护（布隆过滤器简化版）
  async checkExists(type, id) {
    const key = `${CACHE_PREFIX[type.toUpperCase()]}exists:${id}`;
    return await sismemberAsync(key, id);
  }

  async markExists(type, id) {
    const key = `${CACHE_PREFIX[type.toUpperCase()]}exists:${id}`;
    await saddAsync(key, id);
    await expireAsync(key, CACHE_TTL.DAY);
  }

  // 热点数据统计
  async recordAccess(type, id) {
    const key = `${CACHE_PREFIX.STATS}hot:${type}`;
    const score = Date.now();
    await zaddAsync(key, score, id);
    
    // 只保留最近1000个访问记录
    const count = await zrangeAsync(key, 0, -1);
    if (count.length > 1000) {
      await zremAsync(key, 0, count.length - 1000);
    }
  }

  // 获取热点数据
  async getHotItems(type, limit = 10) {
    const key = `${CACHE_PREFIX.STATS}hot:${type}`;
    return await zrevrangeAsync(key, 0, limit - 1);
  }

  // 清空缓存（仅开发环境）
  async flush() {
    if (process.env.NODE_ENV === 'development') {
      await flushdbAsync();
      console.log('Redis 缓存已清空');
    }
  }
}

// 导出实例
const cacheManager = new CacheManager();

module.exports = {
  redisClient: client,
  cacheManager,
  CACHE_PREFIX,
  CACHE_TTL,
  // 导出Promise化的方法
  redis: {
    get: getAsync,
    set: setAsync,
    del: delAsync,
    exists: existsAsync,
    expire: expireAsync,
    ttl: ttlAsync,
    mget: mgetAsync,
    mset: msetAsync,
    keys: keysAsync,
    scan: scanAsync,
    incr: incrAsync,
    decr: decrAsync,
    hget: hgetAsync,
    hset: hsetAsync,
    hgetall: hgetallAsync,
    hdel: hdelAsync,
    sadd: saddAsync,
    srem: sremAsync,
    smembers: smembersAsync,
    sismember: sismemberAsync,
    zadd: zaddAsync,
    zrem: zremAsync,
    zrange: zrangeAsync,
    zrevrange: zrevrangeAsync
  }
};