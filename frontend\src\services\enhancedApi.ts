import axios, { 
  AxiosInstance, 
  InternalAxiosRequestConfig, 
  AxiosResponse, 
  AxiosError,
  CancelTokenSource
} from 'axios';
import { handleApiError } from '../utils/errorHandler';
import { StrictApiResponse, StrictPaginationResponse } from '../types/enhanced';

// 请求重试配置
interface RetryConfig {
  retries: number;
  retryDelay: number;
  retryCondition?: (error: AxiosError) => boolean;
}

// API 客户端配置
interface ApiClientConfig {
  baseURL?: string;
  timeout?: number;
  retryConfig?: RetryConfig;
  enableRequestId?: boolean;
  enablePerformanceTracking?: boolean;
}

class EnhancedApiClient {
  private instance: AxiosInstance;
  private pendingRequests = new Map<string, CancelTokenSource>();
  private retryConfig: RetryConfig;
  private enablePerformanceTracking: boolean;

  constructor(config: ApiClientConfig = {}) {
    const {
      baseURL = process.env.REACT_APP_API_URL || 'http://localhost:3001/api',
      timeout = 15000,
      retryConfig = { retries: 3, retryDelay: 1000 },
      enableRequestId = true,
      enablePerformanceTracking = process.env.NODE_ENV === 'development'
    } = config;

    this.retryConfig = retryConfig;
    this.enablePerformanceTracking = enablePerformanceTracking;

    this.instance = axios.create({
      baseURL,
      timeout,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    this.setupInterceptors(enableRequestId);
  }

  private setupInterceptors(enableRequestId: boolean) {
    // 请求拦截器
    this.instance.interceptors.request.use(
      (config: InternalAxiosRequestConfig) => {
        // 添加认证token
        const token = localStorage.getItem('token');
        if (token && config.headers) {
          config.headers.Authorization = `Bearer ${token}`;
        }

        // 添加请求ID
        if (enableRequestId && config.headers) {
          const requestId = `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
          config.headers['X-Request-ID'] = requestId;
          
          // 性能追踪
          if (this.enablePerformanceTracking) {
            (config as any)._startTime = performance.now();
            (config as any)._requestId = requestId;
          }
        }

        // 取消重复请求
        const requestKey = this.getRequestKey(config);
        this.cancelPendingRequest(requestKey);
        
        const cancelToken = axios.CancelToken.source();
        this.pendingRequests.set(requestKey, cancelToken);
        config.cancelToken = cancelToken.token;

        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // 响应拦截器
    this.instance.interceptors.response.use(
      (response: AxiosResponse) => {
        // 性能追踪
        if (this.enablePerformanceTracking && (response.config as any)._startTime) {
          const duration = performance.now() - (response.config as any)._startTime;
          const requestId = (response.config as any)._requestId;
          
          console.log(`API Request [${requestId}]: ${response.config.method?.toUpperCase()} ${response.config.url} - ${duration.toFixed(2)}ms`);
          
          if (duration > 1000) {
            console.warn(`⚠️ Slow API request detected: ${duration.toFixed(2)}ms`);
          }
        }

        // 移除已完成的请求
        const requestKey = this.getRequestKey(response.config);
        this.pendingRequests.delete(requestKey);

        // 验证响应格式
        this.validateResponse(response);

        return response;
      },
      async (error: AxiosError) => {
        const originalRequest = error.config as any;

        // 移除失败的请求
        if (originalRequest) {
          const requestKey = this.getRequestKey(originalRequest);
          this.pendingRequests.delete(requestKey);
        }

        // 请求被取消
        if (axios.isCancel(error)) {
          return Promise.reject(error);
        }

        // 重试逻辑
        if (this.shouldRetry(error) && originalRequest && !originalRequest._retry) {
          originalRequest._retry = true;
          originalRequest._retryCount = (originalRequest._retryCount || 0) + 1;

          if (originalRequest._retryCount <= this.retryConfig.retries) {
            console.log(`Retrying request (${originalRequest._retryCount}/${this.retryConfig.retries}):`, originalRequest.url);
            
            await this.delay(this.retryConfig.retryDelay * originalRequest._retryCount);
            return this.instance(originalRequest);
          }
        }

        // 处理特殊错误状态
        this.handleSpecialErrors(error);

        // 使用统一的错误处理器
        handleApiError(error);

        return Promise.reject(error);
      }
    );
  }

  private getRequestKey(config: InternalAxiosRequestConfig): string {
    return `${config.method}:${config.url}:${JSON.stringify(config.params)}`;
  }

  private cancelPendingRequest(requestKey: string) {
    const pendingRequest = this.pendingRequests.get(requestKey);
    if (pendingRequest) {
      pendingRequest.cancel(`Duplicate request cancelled: ${requestKey}`);
      this.pendingRequests.delete(requestKey);
    }
  }

  private shouldRetry(error: AxiosError): boolean {
    if (this.retryConfig.retryCondition) {
      return this.retryConfig.retryCondition(error);
    }

    // 默认重试条件：网络错误或5xx服务器错误
    return !error.response || 
           (error.response.status >= 500 && error.response.status < 600) ||
           error.code === 'ECONNABORTED' ||
           error.code === 'NETWORK_ERROR';
  }

  private async delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  private validateResponse(response: AxiosResponse) {
    // 验证响应数据结构
    if (response.data && typeof response.data === 'object') {
      if (!('success' in response.data)) {
        console.warn('API response missing success field:', response.data);
      }
    }
  }

  private handleSpecialErrors(error: AxiosError) {
    if (error.response?.status === 401) {
      // Token 过期，清除本地存储
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      
      // 如果不在登录页，跳转到登录页
      if (window.location.pathname !== '/login') {
        window.location.href = '/login?redirect=' + encodeURIComponent(window.location.pathname);
      }
    }
  }

  // 泛型GET请求
  async get<T = unknown>(url: string, config?: any): Promise<StrictApiResponse<T>> {
    const response = await this.instance.get(url, config);
    return response.data;
  }

  // 泛型POST请求
  async post<T = unknown>(url: string, data?: any, config?: any): Promise<StrictApiResponse<T>> {
    const response = await this.instance.post(url, data, config);
    return response.data;
  }

  // 泛型PUT请求
  async put<T = unknown>(url: string, data?: any, config?: any): Promise<StrictApiResponse<T>> {
    const response = await this.instance.put(url, data, config);
    return response.data;
  }

  // 泛型DELETE请求
  async delete<T = unknown>(url: string, config?: any): Promise<StrictApiResponse<T>> {
    const response = await this.instance.delete(url, config);
    return response.data;
  }

  // 分页查询
  async getPaginated<T = unknown>(url: string, config?: any): Promise<StrictPaginationResponse<T>> {
    const response = await this.instance.get(url, config);
    return response.data;
  }

  // 文件上传
  async upload<T = unknown>(url: string, file: File, onProgress?: (progress: number) => void): Promise<StrictApiResponse<T>> {
    const formData = new FormData();
    formData.append('file', file);

    const response = await this.instance.post(url, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress: (progressEvent) => {
        if (onProgress && progressEvent.total) {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          onProgress(progress);
        }
      },
    });

    return response.data;
  }

  // 取消所有待处理的请求
  cancelAllRequests() {
    this.pendingRequests.forEach((cancelToken) => {
      cancelToken.cancel('All requests cancelled');
    });
    this.pendingRequests.clear();
  }

  // 获取实例（用于直接使用axios功能）
  getInstance(): AxiosInstance {
    return this.instance;
  }
}

// 创建默认实例
const enhancedApi = new EnhancedApiClient({
  enableRequestId: true,
  enablePerformanceTracking: process.env.NODE_ENV === 'development',
  retryConfig: {
    retries: 2,
    retryDelay: 1000,
    retryCondition: (error: AxiosError) => {
      // 只对网络错误和5xx错误重试
      return !error.response || error.response.status >= 500;
    }
  }
});

export default enhancedApi;
export { EnhancedApiClient };