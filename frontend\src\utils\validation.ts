import { FormValidationResult } from '../types/enhanced';

// 验证规则类型
export interface ValidationRule {
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp;
  validator?: (value: any) => boolean | string;
  message?: string;
}

export interface ValidationSchema {
  [key: string]: ValidationRule | ValidationRule[];
}

// 内置验证规则
export const validationRules = {
  required: (message = '此字段是必填的'): ValidationRule => ({
    required: true,
    message
  }),

  minLength: (min: number, message?: string): ValidationRule => ({
    minLength: min,
    message: message || `最少需要${min}个字符`
  }),

  maxLength: (max: number, message?: string): ValidationRule => ({
    maxLength: max,
    message: message || `最多允许${max}个字符`
  }),

  email: (message = '请输入有效的邮箱地址'): ValidationRule => ({
    pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    message
  }),

  phone: (message = '请输入有效的手机号'): ValidationRule => ({
    pattern: /^1[3-9]\d{9}$/,
    message
  }),

  password: (message = '密码必须包含字母和数字，长度6-20位'): ValidationRule => ({
    pattern: /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d@$!%*?&]{6,20}$/,
    message
  }),

  url: (message = '请输入有效的URL'): ValidationRule => ({
    pattern: /^https?:\/\/.+/,
    message
  }),

  number: (message = '请输入有效的数字'): ValidationRule => ({
    validator: (value) => !isNaN(Number(value)),
    message
  }),

  positiveNumber: (message = '请输入大于0的数字'): ValidationRule => ({
    validator: (value) => !isNaN(Number(value)) && Number(value) > 0,
    message
  }),

  integer: (message = '请输入整数'): ValidationRule => ({
    validator: (value) => Number.isInteger(Number(value)),
    message
  }),

  range: (min: number, max: number, message?: string): ValidationRule => ({
    validator: (value) => {
      const num = Number(value);
      return !isNaN(num) && num >= min && num <= max;
    },
    message: message || `请输入${min}到${max}之间的数字`
  }),

  custom: (validator: (value: any) => boolean | string, message?: string): ValidationRule => ({
    validator,
    message
  })
};

// 验证单个字段
export function validateField(value: any, rules: ValidationRule | ValidationRule[]): string | null {
  const ruleArray = Array.isArray(rules) ? rules : [rules];

  for (const rule of ruleArray) {
    // 必填验证
    if (rule.required && (value === null || value === undefined || value === '')) {
      return rule.message || '此字段是必填的';
    }

    // 如果值为空且不是必填，跳过其他验证
    if (!rule.required && (value === null || value === undefined || value === '')) {
      continue;
    }

    // 最小长度验证
    if (rule.minLength && String(value).length < rule.minLength) {
      return rule.message || `最少需要${rule.minLength}个字符`;
    }

    // 最大长度验证
    if (rule.maxLength && String(value).length > rule.maxLength) {
      return rule.message || `最多允许${rule.maxLength}个字符`;
    }

    // 正则验证
    if (rule.pattern && !rule.pattern.test(String(value))) {
      return rule.message || '格式不正确';
    }

    // 自定义验证
    if (rule.validator) {
      const result = rule.validator(value);
      if (result !== true) {
        return typeof result === 'string' ? result : (rule.message || '验证失败');
      }
    }
  }

  return null;
}

// 验证整个表单
export function validateForm(data: Record<string, any>, schema: ValidationSchema): FormValidationResult {
  const errors: Record<string, string> = {};
  let isValid = true;

  for (const [field, rules] of Object.entries(schema)) {
    const error = validateField(data[field], rules);
    if (error) {
      errors[field] = error;
      isValid = false;
    }
  }

  return { isValid, errors };
}

// 常用表单验证模式
export const formSchemas = {
  // 登录表单
  login: {
    phone: [validationRules.required(), validationRules.phone()],
    password: [validationRules.required(), validationRules.minLength(6)]
  },

  // 注册表单
  register: {
    phone: [validationRules.required(), validationRules.phone()],
    password: [validationRules.required(), validationRules.password()],
    username: [
      validationRules.required(),
      validationRules.minLength(2),
      validationRules.maxLength(30)
    ],
    email: validationRules.email()
  },

  // 图书表单
  book: {
    title: [validationRules.required(), validationRules.maxLength(200)],
    author: validationRules.maxLength(100),
    publisher: validationRules.maxLength(100),
    isbn: {
      pattern: /^(?:ISBN(?:-1[03])?:? )?(?=[0-9X]{10}$|(?=(?:[0-9]+[- ]){3})[- 0-9X]{13}$|97[89][0-9]{10}$|(?=(?:[0-9]+[- ]){4})[- 0-9]{17}$)(?:97[89][- ]?)?[0-9]{1,5}[- ]?[0-9]+[- ]?[0-9]+[- ]?[0-9X]$/,
      message: '请输入有效的ISBN号'
    },
    category_id: validationRules.required('请选择图书分类'),
    condition: validationRules.required('请选择图书状况'),
    price: [
      validationRules.required(),
      validationRules.positiveNumber('价格必须大于0')
    ],
    stock: [
      validationRules.required(),
      validationRules.integer('库存必须是整数'),
      validationRules.range(0, 9999, '库存范围0-9999')
    ]
  },

  // 订单表单
  order: {
    delivery_address: [validationRules.required(), validationRules.maxLength(200)],
    delivery_phone: [validationRules.required(), validationRules.phone()],
    notes: validationRules.maxLength(500)
  },

  // 用户资料表单
  profile: {
    username: [
      validationRules.required(),
      validationRules.minLength(2),
      validationRules.maxLength(30)
    ],
    email: validationRules.email(),
    contact_wechat: validationRules.maxLength(50),
    contact_qq: {
      pattern: /^[1-9][0-9]{4,10}$/,
      message: '请输入有效的QQ号'
    },
    contact_phone_public: validationRules.phone()
  }
};

// 实时验证Hook
import { useState, useCallback, useEffect } from 'react';

export function useFormValidation(schema: ValidationSchema, initialData: Record<string, any> = {}) {
  const [data, setData] = useState(initialData);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [touched, setTouched] = useState<Record<string, boolean>>({});

  // 验证单个字段
  const validateSingleField = useCallback((field: string, value: any) => {
    if (schema[field]) {
      const error = validateField(value, schema[field]);
      setErrors(prev => ({
        ...prev,
        [field]: error || ''
      }));
      return !error;
    }
    return true;
  }, [schema]);

  // 设置字段值并验证
  const setFieldValue = useCallback((field: string, value: any) => {
    setData(prev => ({ ...prev, [field]: value }));
    if (touched[field]) {
      validateSingleField(field, value);
    }
  }, [touched, validateSingleField]);

  // 设置字段为已触摸状态
  const setFieldTouched = useCallback((field: string) => {
    setTouched(prev => ({ ...prev, [field]: true }));
    validateSingleField(field, data[field]);
  }, [data, validateSingleField]);

  // 验证所有字段
  const validateAllFields = useCallback(() => {
    const result = validateForm(data, schema);
    setErrors(result.errors);
    setTouched(
      Object.keys(schema).reduce((acc, key) => {
        acc[key] = true;
        return acc;
      }, {} as Record<string, boolean>)
    );
    return result.isValid;
  }, [data, schema]);

  // 重置表单
  const resetForm = useCallback(() => {
    setData(initialData);
    setErrors({});
    setTouched({});
  }, [initialData]);

  // 检查表单是否有效
  const isValid = Object.values(errors).every(error => !error) && 
                   Object.keys(touched).length > 0;

  return {
    data,
    errors,
    touched,
    isValid,
    setFieldValue,
    setFieldTouched,
    validateAllFields,
    resetForm
  };
}