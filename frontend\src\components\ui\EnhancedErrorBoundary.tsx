import React, { Component, ErrorInfo, ReactNode } from 'react';
import { Result, Button, Typography, Space, Collapse } from 'antd';
import { ReloadOutlined, BugOutlined, HomeOutlined } from '@ant-design/icons';
import styled from 'styled-components';

const { Text, Paragraph } = Typography;
const { Panel } = Collapse;

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  showDetails?: boolean;
  enableRecovery?: boolean;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  errorCount: number;
  lastErrorTime: number;
}

const ErrorContainer = styled.div`
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
`;

const ErrorCard = styled.div`
  background: white;
  border-radius: 16px;
  padding: 40px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
  max-width: 600px;
  width: 100%;
`;

const ErrorDetails = styled.div`
  margin-top: 20px;
  
  .error-stack {
    background: #f5f5f5;
    padding: 16px;
    border-radius: 8px;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 12px;
    overflow-x: auto;
    white-space: pre-wrap;
    word-break: break-word;
  }
  
  .error-info {
    margin-top: 16px;
    
    .info-item {
      margin-bottom: 8px;
      
      .label {
        font-weight: 600;
        margin-right: 8px;
      }
    }
  }
`;

class EnhancedErrorBoundary extends Component<Props, State> {
  private resetTimeoutId: NodeJS.Timeout | null = null;

  constructor(props: Props) {
    super(props);
    
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorCount: 0,
      lastErrorTime: 0
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    const { onError } = this.props;
    const { errorCount } = this.state;
    const currentTime = Date.now();
    
    // 更新错误状态
    this.setState({
      error,
      errorInfo,
      errorCount: errorCount + 1,
      lastErrorTime: currentTime
    });
    
    // 记录错误到控制台
    console.error('ErrorBoundary caught:', error, errorInfo);
    
    // 调用错误处理回调
    if (onError) {
      onError(error, errorInfo);
    }
    
    // 发送错误到监控服务（生产环境）
    if (process.env.NODE_ENV === 'production') {
      this.logErrorToService(error, errorInfo);
    }
    
    // 如果短时间内错误次数过多，可能是死循环
    if (errorCount > 5 && currentTime - this.state.lastErrorTime < 1000) {
      console.error('Too many errors in a short time, possible infinite loop');
    }
  }

  componentWillUnmount() {
    // 清理定时器，防止内存泄漏
    if (this.resetTimeoutId) {
      clearTimeout(this.resetTimeoutId);
    }
  }

  logErrorToService = (error: Error, errorInfo: ErrorInfo) => {
    // 实现错误上报逻辑
    const errorData = {
      message: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href,
      errorCount: this.state.errorCount
    };
    
    // 这里可以集成 Sentry、LogRocket 等错误监控服务
    // 示例：window.Sentry?.captureException(error, { extra: errorData });
    
    // 或者发送到自己的错误收集API
    fetch('/api/errors', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(errorData)
    }).catch(err => {
      console.error('Failed to log error:', err);
    });
  };

  handleReset = () => {
    // 重置错误状态
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null
    });
    
    // 如果启用了恢复功能，延迟一段时间后重置错误计数
    if (this.props.enableRecovery) {
      this.resetTimeoutId = setTimeout(() => {
        this.setState({ errorCount: 0 });
      }, 5000);
    }
  };

  handleGoHome = () => {
    window.location.href = '/';
  };

  render() {
    const { hasError, error, errorInfo, errorCount } = this.state;
    const { children, fallback, showDetails = true } = this.props;

    if (hasError && error) {
      // 如果提供了自定义fallback，使用它
      if (fallback) {
        return <>{fallback}</>;
      }

      // 默认错误UI
      return (
        <ErrorContainer>
          <ErrorCard>
            <Result
              status="error"
              icon={<BugOutlined style={{ fontSize: 72 }} />}
              title="哎呀，出错了！"
              subTitle="页面遇到了一些问题，我们正在努力修复。"
              extra={
                <Space>
                  <Button
                    type="primary"
                    icon={<ReloadOutlined />}
                    onClick={this.handleReset}
                  >
                    重试
                  </Button>
                  <Button
                    icon={<HomeOutlined />}
                    onClick={this.handleGoHome}
                  >
                    返回首页
                  </Button>
                </Space>
              }
            >
              {showDetails && (
                <ErrorDetails>
                  <Collapse ghost>
                    <Panel header="错误详情" key="1">
                      <div className="error-info">
                        <div className="info-item">
                          <Text className="label">错误信息:</Text>
                          <Text type="danger">{error.message}</Text>
                        </div>
                        <div className="info-item">
                          <Text className="label">错误次数:</Text>
                          <Text>{errorCount}</Text>
                        </div>
                        <div className="info-item">
                          <Text className="label">发生时间:</Text>
                          <Text>{new Date().toLocaleString()}</Text>
                        </div>
                      </div>
                      
                      {error.stack && (
                        <div className="error-stack">
                          {error.stack}
                        </div>
                      )}
                      
                      {errorInfo && errorInfo.componentStack && (
                        <>
                          <Paragraph type="secondary" style={{ marginTop: 16 }}>
                            组件栈:
                          </Paragraph>
                          <div className="error-stack">
                            {errorInfo.componentStack}
                          </div>
                        </>
                      )}
                    </Panel>
                  </Collapse>
                </ErrorDetails>
              )}
            </Result>
          </ErrorCard>
        </ErrorContainer>
      );
    }

    return children;
  }
}

// 创建一个Hook版本的错误处理
export const useErrorHandler = () => {
  const [error, setError] = React.useState<Error | null>(null);

  React.useEffect(() => {
    if (error) {
      throw error;
    }
  }, [error]);

  const resetError = () => setError(null);
  const captureError = (error: Error) => setError(error);

  return { resetError, captureError };
};

// 错误恢复组件
export const ErrorRecovery: React.FC<{
  children: ReactNode;
  fallback?: (props: { error: Error; retry: () => void }) => ReactNode;
}> = ({ children, fallback }) => {
  const [error, setError] = React.useState<Error | null>(null);
  const [key, setKey] = React.useState(0);

  const retry = () => {
    setError(null);
    setKey(prev => prev + 1);
  };

  if (error && fallback) {
    return <>{fallback({ error, retry })}</>;
  }

  return (
    <EnhancedErrorBoundary
      key={key}
      onError={(err) => setError(err)}
      enableRecovery
    >
      {children}
    </EnhancedErrorBoundary>
  );
};

export default EnhancedErrorBoundary;