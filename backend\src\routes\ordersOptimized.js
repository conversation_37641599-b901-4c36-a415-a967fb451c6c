const express = require('express');
const { Op } = require('sequelize');
const sequelize = require('../config/database');
const { Order, OrderItem, Book, User } = require('../models');
const { validate, createOrderSchema } = require('../utils/validation');
const { requireOwnerOrAdmin } = require('../middleware/authEnhanced');
const { createLoaders, QueryBuilder, QueryMonitor } = require('../utils/queryOptimizer');

const router = express.Router();
const queryMonitor = new QueryMonitor();

// 创建数据加载器
const loaders = createLoaders({ User, Book, Category: null, Order, OrderItem });

// 创建订单（优化版）
router.post('/', validate(createOrderSchema), async (req, res) => {
  let transaction;
  
  try {
    const { items, delivery_address, delivery_phone } = req.body;
    const userId = req.user.id;

    await queryMonitor.monitor(async () => {
      // 使用READ COMMITTED隔离级别减少锁竞争
      transaction = await sequelize.transaction({
        isolationLevel: sequelize.Transaction.ISOLATION_LEVELS.READ_COMMITTED
      });

      // 批量获取并锁定图书
      const bookIds = items.map(item => item.book_id);
      const books = await Book.findAll({
        where: { 
          id: bookIds,
          status: '上架'
        },
        attributes: ['id', 'title', 'price', 'stock', 'status'],
        lock: transaction.LOCK.UPDATE,
        transaction
      });

      // 快速验证
      if (books.length !== bookIds.length) {
        throw new Error('部分图书不存在或已下架');
      }

      // 创建图书映射
      const bookMap = new Map(books.map(book => [book.id, book]));
      
      // 验证库存并准备批量更新
      let totalAmount = 0;
      const stockUpdates = [];
      const orderItems = [];

      for (const item of items) {
        const book = bookMap.get(item.book_id);
        
        if (book.stock < item.quantity) {
          throw new Error(`图书 "${book.title}" 库存不足，剩余: ${book.stock}`);
        }

        const subtotal = book.price * item.quantity;
        totalAmount += subtotal;

        stockUpdates.push({
          id: book.id,
          stock: book.stock - item.quantity
        });

        orderItems.push({
          book_id: item.book_id,
          quantity: item.quantity,
          price: book.price,
          subtotal
        });
      }

      // 创建订单
      const order = await Order.create({
        user_id: userId,
        total_amount: totalAmount,
        delivery_address,
        delivery_phone,
        delivery_method: 'platform',
        status: 'pending',
        payment_status: 'pending'
      }, { transaction });

      // 批量创建订单项
      const orderItemsData = orderItems.map(item => ({
        ...item,
        order_id: order.id
      }));
      await OrderItem.bulkCreate(orderItemsData, { transaction });

      // 批量更新库存
      await Promise.all(
        stockUpdates.map(update =>
          Book.update(
            { stock: update.stock },
            { 
              where: { id: update.id },
              transaction
            }
          )
        )
      );

      await transaction.commit();
      
      // 返回基本订单信息
      return {
        id: order.id,
        order_number: order.order_number,
        total_amount: order.total_amount,
        items: orderItemsData
      };
    }, 'createOrder');

    res.status(201).json({
      success: true,
      message: '订单创建成功',
      data: {
        id: order.id,
        order_number: order.order_number,
        total_amount: order.total_amount,
        status: 'pending'
      }
    });
  } catch (error) {
    if (transaction) {
      await transaction.rollback();
    }
    
    console.error('创建订单错误:', error);
    
    if (error.message.includes('不存在') || error.message.includes('库存不足')) {
      return res.status(400).json({
        success: false,
        message: error.message
      });
    }
    
    res.status(500).json({
      success: false,
      message: '创建订单失败，请稍后重试'
    });
  }
});

// 获取用户订单列表（优化版）
router.get('/', async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      status,
      start_date,
      end_date
    } = req.query;

    const result = await queryMonitor.monitor(async () => {
      // 构建查询条件
      const where = { user_id: req.user.id };
      
      if (status) {
        where.status = status;
      }
      
      if (start_date || end_date) {
        where.created_at = {};
        if (start_date) where.created_at[Op.gte] = new Date(start_date);
        if (end_date) where.created_at[Op.lte] = new Date(end_date);
      }

      // 查询订单
      const queryBuilder = new QueryBuilder(Order, {
        defaultAttributes: [
          'id', 'order_number', 'total_amount', 'status',
          'payment_status', 'delivery_method', 'created_at'
        ]
      });

      const { count, rows: orders } = await queryBuilder
        .where(where)
        .paginate(page, limit)
        .execute();

      if (orders.length > 0) {
        // 收集订单ID
        const orderIds = orders.map(o => o.id);
        
        // 批量查询订单项
        const orderItems = await OrderItem.findAll({
          where: { order_id: orderIds },
          attributes: ['id', 'order_id', 'book_id', 'quantity', 'price', 'subtotal']
        });

        // 收集图书ID并批量加载
        const bookIds = [...new Set(orderItems.map(item => item.book_id))];
        const books = await loaders.bookLoader.loadMany(bookIds);
        const bookMap = new Map(books.filter(Boolean).map(b => [b.id, b]));

        // 组装数据
        const itemsByOrder = {};
        orderItems.forEach(item => {
          if (!itemsByOrder[item.order_id]) {
            itemsByOrder[item.order_id] = [];
          }
          
          const itemData = item.toJSON();
          itemData.book = bookMap.get(item.book_id) || null;
          itemsByOrder[item.order_id].push(itemData);
        });

        // 附加订单项到订单
        orders.forEach(order => {
          order.dataValues.items = itemsByOrder[order.id] || [];
          order.dataValues.item_count = order.dataValues.items.length;
        });
      }

      return { count, orders };
    }, 'getUserOrders');

    res.json({
      success: true,
      data: {
        orders: result.orders,
        pagination: {
          current_page: parseInt(page),
          total_pages: Math.ceil(result.count / limit),
          total_items: result.count,
          items_per_page: parseInt(limit)
        }
      }
    });
  } catch (error) {
    console.error('获取订单列表错误:', error);
    res.status(500).json({
      success: false,
      message: '获取订单列表失败'
    });
  }
});

// 获取订单详情（优化版）
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const order = await queryMonitor.monitor(async () => {
      // 基础订单查询
      const order = await Order.findByPk(id);
      
      if (!order) return null;

      // 权限检查
      if (req.user.role === 'user' && order.user_id !== req.user.id) {
        throw new Error('FORBIDDEN');
      }

      // 并行加载所有关联数据
      const [user, orderItems] = await Promise.all([
        loaders.userLoader.load(order.user_id),
        OrderItem.findAll({
          where: { order_id: id },
          attributes: ['id', 'book_id', 'quantity', 'price', 'subtotal']
        })
      ]);

      // 加载图书信息
      if (orderItems.length > 0) {
        const bookIds = orderItems.map(item => item.book_id);
        const books = await loaders.bookLoader.loadMany(bookIds);
        const bookMap = new Map(books.filter(Boolean).map(b => [b.id, b]));

        // 组装订单项数据
        order.dataValues.items = orderItems.map(item => {
          const itemData = item.toJSON();
          itemData.book = bookMap.get(item.book_id) || null;
          return itemData;
        });
      } else {
        order.dataValues.items = [];
      }

      order.dataValues.user = user;
      
      return order;
    }, 'getOrderDetail');

    if (!order) {
      return res.status(404).json({
        success: false,
        message: '订单不存在'
      });
    }

    if (order === 'FORBIDDEN') {
      return res.status(403).json({
        success: false,
        message: '权限不足'
      });
    }

    res.json({
      success: true,
      data: order
    });
  } catch (error) {
    console.error('获取订单详情错误:', error);
    res.status(500).json({
      success: false,
      message: '获取订单详情失败'
    });
  }
});

// 批量更新订单状态（管理员）
router.patch('/batch/status', requireOwnerOrAdmin(), async (req, res) => {
  let transaction;
  
  try {
    const { order_ids, status, reason } = req.body;

    if (!order_ids || !Array.isArray(order_ids) || order_ids.length === 0) {
      return res.status(400).json({
        success: false,
        message: '请提供订单ID列表'
      });
    }

    const result = await queryMonitor.monitor(async () => {
      transaction = await sequelize.transaction();

      // 批量查询订单验证权限
      const orders = await Order.findAll({
        where: { id: order_ids },
        attributes: ['id', 'status', 'user_id'],
        transaction,
        lock: transaction.LOCK.UPDATE
      });

      // 验证权限
      if (req.user.role === 'user') {
        const unauthorized = orders.some(order => order.user_id !== req.user.id);
        if (unauthorized) {
          throw new Error('包含无权操作的订单');
        }
      }

      // 批量更新
      const updateData = { status };
      if (reason) updateData.notes = reason;

      const [updateCount] = await Order.update(updateData, {
        where: { id: order_ids },
        transaction
      });

      // 如果是取消订单，恢复库存
      if (status === 'cancelled') {
        const orderItems = await OrderItem.findAll({
          where: { order_id: order_ids },
          attributes: ['book_id', 'quantity'],
          transaction
        });

        // 按图书ID汇总数量
        const stockRestores = {};
        orderItems.forEach(item => {
          stockRestores[item.book_id] = (stockRestores[item.book_id] || 0) + item.quantity;
        });

        // 批量恢复库存
        await Promise.all(
          Object.entries(stockRestores).map(([bookId, quantity]) =>
            Book.increment('stock', {
              by: quantity,
              where: { id: bookId },
              transaction
            })
          )
        );
      }

      await transaction.commit();
      
      return { updateCount };
    }, 'batchUpdateOrderStatus');

    res.json({
      success: true,
      message: `成功更新 ${result.updateCount} 个订单`,
      data: { updated: result.updateCount }
    });
  } catch (error) {
    if (transaction) {
      await transaction.rollback();
    }
    
    console.error('批量更新订单错误:', error);
    res.status(500).json({
      success: false,
      message: error.message || '批量更新失败'
    });
  }
});

// 订单统计（优化版）
router.get('/stats/summary', async (req, res) => {
  try {
    const stats = await queryMonitor.monitor(async () => {
      const userId = req.user.id;
      
      // 并行执行多个统计查询
      const [statusCounts, monthlyStats, recentOrders] = await Promise.all([
        // 按状态统计
        Order.findAll({
          where: { user_id: userId },
          attributes: [
            'status',
            [sequelize.fn('COUNT', sequelize.col('id')), 'count'],
            [sequelize.fn('SUM', sequelize.col('total_amount')), 'total_amount']
          ],
          group: ['status']
        }),
        
        // 月度统计
        Order.findOne({
          where: {
            user_id: userId,
            created_at: {
              [Op.gte]: new Date(new Date().setDate(1)) // 本月第一天
            }
          },
          attributes: [
            [sequelize.fn('COUNT', sequelize.col('id')), 'order_count'],
            [sequelize.fn('SUM', sequelize.col('total_amount')), 'total_spent']
          ]
        }),
        
        // 最近订单
        Order.findAll({
          where: { user_id: userId },
          attributes: ['id', 'order_number', 'total_amount', 'status', 'created_at'],
          order: [['created_at', 'DESC']],
          limit: 5
        })
      ]);

      return {
        byStatus: statusCounts,
        monthly: monthlyStats?.dataValues || { order_count: 0, total_spent: 0 },
        recent: recentOrders
      };
    }, 'getOrderStats');

    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    console.error('获取订单统计错误:', error);
    res.status(500).json({
      success: false,
      message: '获取订单统计失败'
    });
  }
});

// 导出查询性能报告
router.get('/admin/performance', requireOwnerOrAdmin(), (req, res) => {
  res.json({
    success: true,
    data: queryMonitor.getReport()
  });
});

module.exports = router;