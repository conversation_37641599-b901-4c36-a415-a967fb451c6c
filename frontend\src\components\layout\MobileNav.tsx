import React, { useState } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { useAuth } from '../../stores/authStore';
import '../../styles/responsive.css';

interface MobileNavProps {
  isOpen: boolean;
  onClose: () => void;
}

const MobileNav: React.FC<MobileNavProps> = ({ isOpen, onClose }) => {
  const location = useLocation();
  const navigate = useNavigate();
  const { user, logout } = useAuth();
  const [showUserMenu, setShowUserMenu] = useState(false);

  const handleLogout = async () => {
    await logout();
    navigate('/');
    onClose();
  };

  const navItems = [
    { path: '/', label: '首页', icon: '🏠' },
    { path: '/books', label: '书籍', icon: '📚' },
    { path: '/cart', label: '购物车', icon: '🛒' },
    { path: '/orders', label: '订单', icon: '📋' },
  ];

  const isActive = (path: string) => location.pathname === path;

  return (
    <>
      {/* 遮罩层 */}
      <div 
        className={`mobile-nav-overlay ${isOpen ? 'active' : ''}`}
        onClick={onClose}
        style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.5)',
          opacity: isOpen ? 1 : 0,
          visibility: isOpen ? 'visible' : 'hidden',
          transition: 'all 0.3s ease',
          zIndex: 998
        }}
      />

      {/* 侧边导航栏 */}
      <nav 
        className={`mobile-nav-drawer ${isOpen ? 'open' : ''}`}
        style={{
          position: 'fixed',
          top: 0,
          left: isOpen ? 0 : '-80%',
          width: '80%',
          maxWidth: '320px',
          height: '100%',
          backgroundColor: '#fff',
          boxShadow: '2px 0 10px rgba(0, 0, 0, 0.1)',
          transition: 'left 0.3s ease',
          zIndex: 999,
          display: 'flex',
          flexDirection: 'column'
        }}
      >
        {/* 用户信息 */}
        <div className="mobile-nav-header" style={{
          padding: '20px',
          backgroundColor: '#f8f9fa',
          borderBottom: '1px solid #dee2e6'
        }}>
          {user ? (
            <div>
              <div style={{
                display: 'flex',
                alignItems: 'center',
                marginBottom: '10px'
              }}>
                <div style={{
                  width: '50px',
                  height: '50px',
                  borderRadius: '50%',
                  backgroundColor: '#007bff',
                  color: '#fff',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  fontSize: '20px',
                  marginRight: '15px'
                }}>
                  {user.username.charAt(0).toUpperCase()}
                </div>
                <div>
                  <div style={{ fontWeight: 'bold' }}>{user.username}</div>
                  <div style={{ fontSize: '14px', color: '#6c757d' }}>{user.email}</div>
                </div>
              </div>
              <button
                onClick={() => setShowUserMenu(!showUserMenu)}
                className="btn-touch"
                style={{
                  width: '100%',
                  padding: '8px',
                  backgroundColor: '#f8f9fa',
                  border: '1px solid #dee2e6',
                  borderRadius: '4px',
                  fontSize: '14px'
                }}
              >
                账户设置 {showUserMenu ? '▲' : '▼'}
              </button>
            </div>
          ) : (
            <div>
              <Link
                to="/auth/login"
                onClick={onClose}
                className="btn-touch"
                style={{
                  display: 'block',
                  width: '100%',
                  padding: '12px',
                  backgroundColor: '#007bff',
                  color: '#fff',
                  textAlign: 'center',
                  borderRadius: '4px',
                  textDecoration: 'none',
                  marginBottom: '10px'
                }}
              >
                登录
              </Link>
              <Link
                to="/auth/register"
                onClick={onClose}
                className="btn-touch"
                style={{
                  display: 'block',
                  width: '100%',
                  padding: '12px',
                  backgroundColor: '#fff',
                  color: '#007bff',
                  border: '1px solid #007bff',
                  textAlign: 'center',
                  borderRadius: '4px',
                  textDecoration: 'none'
                }}
              >
                注册
              </Link>
            </div>
          )}
        </div>

        {/* 用户菜单 */}
        {user && showUserMenu && (
          <div style={{
            backgroundColor: '#f8f9fa',
            borderBottom: '1px solid #dee2e6',
            padding: '0 20px'
          }}>
            <Link
              to="/profile"
              onClick={onClose}
              className="mobile-nav-link"
              style={{
                display: 'block',
                padding: '12px 0',
                color: '#333',
                textDecoration: 'none',
                borderBottom: '1px solid #e9ecef'
              }}
            >
              个人资料
            </Link>
            {user.role === 'admin' && (
              <Link
                to="/admin"
                onClick={onClose}
                className="mobile-nav-link"
                style={{
                  display: 'block',
                  padding: '12px 0',
                  color: '#333',
                  textDecoration: 'none',
                  borderBottom: '1px solid #e9ecef'
                }}
              >
                管理后台
              </Link>
            )}
          </div>
        )}

        {/* 导航链接 */}
        <div className="mobile-nav-body" style={{
          flex: 1,
          overflowY: 'auto',
          padding: '20px'
        }}>
          {navItems.map((item) => (
            <Link
              key={item.path}
              to={item.path}
              onClick={onClose}
              className={`mobile-nav-item ${isActive(item.path) ? 'active' : ''}`}
              style={{
                display: 'flex',
                alignItems: 'center',
                padding: '15px',
                marginBottom: '10px',
                color: isActive(item.path) ? '#007bff' : '#333',
                backgroundColor: isActive(item.path) ? '#e7f3ff' : 'transparent',
                borderRadius: '8px',
                textDecoration: 'none',
                transition: 'all 0.2s'
              }}
            >
              <span style={{ 
                fontSize: '20px', 
                marginRight: '15px',
                width: '24px',
                textAlign: 'center'
              }}>
                {item.icon}
              </span>
              <span style={{ fontSize: '16px' }}>{item.label}</span>
            </Link>
          ))}
        </div>

        {/* 底部操作 */}
        {user && (
          <div style={{
            padding: '20px',
            borderTop: '1px solid #dee2e6'
          }}>
            <button
              onClick={handleLogout}
              className="btn-touch"
              style={{
                width: '100%',
                padding: '12px',
                backgroundColor: '#dc3545',
                color: '#fff',
                border: 'none',
                borderRadius: '4px',
                fontSize: '16px'
              }}
            >
              退出登录
            </button>
          </div>
        )}
      </nav>
    </>
  );
};

export default MobileNav;