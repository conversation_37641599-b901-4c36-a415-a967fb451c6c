-- 创建文件上传记录表
CREATE TABLE IF NOT EXISTS uploaded_files (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    file_id VARCHAR(64) UNIQUE NOT NULL,
    filename VARCHAR(255) NOT NULL,
    original_name VARCHAR(255) NOT NULL,
    mimetype VARCHAR(100) NOT NULL,
    size INTEGER NOT NULL,
    path VARCHAR(500) NOT NULL,
    url VARCHAR(255) NOT NULL,
    type VARCHAR(20) DEFAULT 'other' CHECK (type IN ('avatar', 'book', 'other')),
    uploaded_by UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    referenced BOOLEAN DEFAULT FALSE,
    reference_count INTEGER DEFAULT 0,
    metadata JSONB DEFAULT '{}',
    virus_scanned BOOLEAN DEFAULT FALSE,
    virus_scan_result VARCHAR(50),
    access_count INTEGER DEFAULT 0,
    last_accessed_at TIMESTAMP,
    expires_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX idx_uploaded_files_file_id ON uploaded_files(file_id);
CREATE INDEX idx_uploaded_files_uploaded_by ON uploaded_files(uploaded_by);
CREATE INDEX idx_uploaded_files_type ON uploaded_files(type);
CREATE INDEX idx_uploaded_files_referenced ON uploaded_files(referenced);
CREATE INDEX idx_uploaded_files_created_at ON uploaded_files(created_at);
CREATE INDEX idx_uploaded_files_expires_at ON uploaded_files(expires_at);

-- 创建更新时间触发器
CREATE OR REPLACE FUNCTION update_uploaded_files_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER set_uploaded_files_updated_at
BEFORE UPDATE ON uploaded_files
FOR EACH ROW
EXECUTE FUNCTION update_uploaded_files_updated_at();

-- 为用户表添加头像文件ID字段（如果不存在）
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'users' 
                   AND column_name = 'avatar_file_id') THEN
        ALTER TABLE users ADD COLUMN avatar_file_id VARCHAR(64);
        CREATE INDEX idx_users_avatar_file_id ON users(avatar_file_id);
    END IF;
END $$;

-- 添加注释
COMMENT ON TABLE uploaded_files IS '文件上传记录表';
COMMENT ON COLUMN uploaded_files.file_id IS '文件唯一标识符';
COMMENT ON COLUMN uploaded_files.filename IS '存储的文件名';
COMMENT ON COLUMN uploaded_files.original_name IS '原始文件名';
COMMENT ON COLUMN uploaded_files.mimetype IS 'MIME类型';
COMMENT ON COLUMN uploaded_files.size IS '文件大小（字节）';
COMMENT ON COLUMN uploaded_files.path IS '文件存储路径';
COMMENT ON COLUMN uploaded_files.url IS '访问URL';
COMMENT ON COLUMN uploaded_files.type IS '文件类型';
COMMENT ON COLUMN uploaded_files.uploaded_by IS '上传者ID';
COMMENT ON COLUMN uploaded_files.referenced IS '是否被引用';
COMMENT ON COLUMN uploaded_files.reference_count IS '引用次数';
COMMENT ON COLUMN uploaded_files.metadata IS '文件元数据（如图片尺寸等）';
COMMENT ON COLUMN uploaded_files.virus_scanned IS '是否已进行病毒扫描';
COMMENT ON COLUMN uploaded_files.virus_scan_result IS '病毒扫描结果';
COMMENT ON COLUMN uploaded_files.access_count IS '访问次数';
COMMENT ON COLUMN uploaded_files.last_accessed_at IS '最后访问时间';
COMMENT ON COLUMN uploaded_files.expires_at IS '过期时间';