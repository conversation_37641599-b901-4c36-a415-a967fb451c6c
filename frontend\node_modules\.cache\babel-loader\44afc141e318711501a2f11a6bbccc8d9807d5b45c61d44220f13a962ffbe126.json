{"ast": null, "code": "var _jsxFileName = \"D:\\\\claude\\u955C\\u50CF\\\\\\u6536\\u4E66\\u5356\\u4E66\\\\frontend\\\\src\\\\components\\\\ui\\\\LazyImage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect, useCallback } from 'react';\nimport { Skeleton } from 'antd';\nimport styled from 'styled-components';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ImageContainer = styled.div`\n  position: relative;\n  width: ${props => typeof props.width === 'number' ? `${props.width}px` : props.width || '100%'};\n  height: ${props => typeof props.height === 'number' ? `${props.height}px` : props.height || 'auto'};\n  overflow: hidden;\n  \n  .lazy-image {\n    width: 100%;\n    height: 100%;\n    object-fit: cover;\n    transition: opacity 0.3s ease;\n    \n    &.loading {\n      opacity: 0;\n    }\n    \n    &.loaded {\n      opacity: 1;\n    }\n    \n    &.error {\n      opacity: 0;\n    }\n  }\n  \n  .placeholder {\n    position: absolute;\n    top: 0;\n    left: 0;\n    width: 100%;\n    height: 100%;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    background-color: #f5f5f5;\n    color: #999;\n    font-size: 14px;\n    \n    &.hidden {\n      display: none;\n    }\n  }\n  \n  .skeleton {\n    position: absolute;\n    top: 0;\n    left: 0;\n    width: 100%;\n    height: 100%;\n    \n    &.hidden {\n      display: none;\n    }\n  }\n`;\n_c = ImageContainer;\nconst LazyImage = ({\n  src,\n  alt = '',\n  width,\n  height,\n  placeholder,\n  fallback = '/images/placeholder.png',\n  className,\n  style,\n  onClick,\n  onLoad,\n  onError,\n  threshold = 0.1,\n  rootMargin = '50px'\n}) => {\n  _s();\n  const [imageState, setImageState] = useState('loading');\n  const [imageSrc, setImageSrc] = useState('');\n  const [isInView, setIsInView] = useState(false);\n  const mountedRef = useRef(true);\n  const imgRef = useRef(null);\n  const containerRef = useRef(null);\n\n  // 组件清理\n  useEffect(() => {\n    return () => {\n      mountedRef.current = false;\n    };\n  }, []);\n\n  // Intersection Observer for lazy loading\n  useEffect(() => {\n    const observer = new IntersectionObserver(entries => {\n      const [entry] = entries;\n      if (entry.isIntersecting && mountedRef.current) {\n        setIsInView(true);\n        observer.disconnect();\n      }\n    }, {\n      threshold,\n      rootMargin\n    });\n    if (containerRef.current) {\n      observer.observe(containerRef.current);\n    }\n    return () => {\n      observer.disconnect();\n    };\n  }, [threshold, rootMargin]);\n\n  // Load image when in view\n  useEffect(() => {\n    if (isInView && src) {\n      setImageSrc(src);\n    }\n  }, [isInView, src]);\n\n  // Handle image load\n  const handleImageLoad = useCallback(() => {\n    if (mountedRef.current) {\n      setImageState('loaded');\n      onLoad === null || onLoad === void 0 ? void 0 : onLoad();\n    }\n  }, [onLoad]);\n\n  // Handle image error\n  const handleImageError = useCallback(() => {\n    if (mountedRef.current) {\n      setImageState('error');\n      if (fallback && fallback !== imageSrc) {\n        setImageSrc(fallback);\n        setImageState('loading');\n      } else {\n        onError === null || onError === void 0 ? void 0 : onError();\n      }\n    }\n  }, [fallback, imageSrc, onError]);\n\n  // Preload image\n  useEffect(() => {\n    if (imageSrc) {\n      const img = new Image();\n      img.onload = handleImageLoad;\n      img.onerror = handleImageError;\n      img.src = imageSrc;\n      return () => {\n        img.onload = null;\n        img.onerror = null;\n      };\n    }\n  }, [imageSrc, handleImageLoad, handleImageError]);\n  const showSkeleton = imageState === 'loading' && !placeholder;\n  const showPlaceholder = imageState === 'loading' && placeholder;\n  const showImage = imageState === 'loaded';\n  const showErrorPlaceholder = imageState === 'error' && !fallback;\n  return /*#__PURE__*/_jsxDEV(ImageContainer, {\n    ref: containerRef,\n    width: width,\n    height: height,\n    className: className,\n    style: style,\n    onClick: onClick,\n    children: [showSkeleton && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"skeleton\",\n      children: /*#__PURE__*/_jsxDEV(Skeleton.Image, {\n        style: {\n          width: '100%',\n          height: '100%'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 191,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 190,\n      columnNumber: 9\n    }, this), showPlaceholder && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"placeholder\",\n      children: placeholder\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 197,\n      columnNumber: 9\n    }, this), showErrorPlaceholder && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"placeholder\",\n      children: \"\\u56FE\\u7247\\u52A0\\u8F7D\\u5931\\u8D25\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 204,\n      columnNumber: 9\n    }, this), imageSrc && /*#__PURE__*/_jsxDEV(\"img\", {\n      ref: imgRef,\n      src: imageSrc,\n      alt: alt,\n      className: `lazy-image ${imageState}`,\n      style: {\n        display: showImage ? 'block' : 'none'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 211,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 180,\n    columnNumber: 5\n  }, this);\n};\n_s(LazyImage, \"8q8fpSd2/xwpH0zpXCiuWzRMtxQ=\");\n_c2 = LazyImage;\nexport default LazyImage;\nvar _c, _c2;\n$RefreshReg$(_c, \"ImageContainer\");\n$RefreshReg$(_c2, \"LazyImage\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "useCallback", "Skeleton", "styled", "jsxDEV", "_jsxDEV", "ImageContainer", "div", "props", "width", "height", "_c", "LazyImage", "src", "alt", "placeholder", "fallback", "className", "style", "onClick", "onLoad", "onError", "threshold", "rootMargin", "_s", "imageState", "setImageState", "imageSrc", "setImageSrc", "isInView", "setIsInView", "mountedRef", "imgRef", "containerRef", "current", "observer", "IntersectionObserver", "entries", "entry", "isIntersecting", "disconnect", "observe", "handleImageLoad", "handleImageError", "img", "Image", "onload", "onerror", "showSkeleton", "showPlaceholder", "showImage", "showErrorPlaceholder", "ref", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "display", "_c2", "$RefreshReg$"], "sources": ["D:/claude镜像/收书卖书/frontend/src/components/ui/LazyImage.tsx"], "sourcesContent": ["import React, { useState, useRef, useEffect, useCallback } from 'react';\nimport { Skeleton } from 'antd';\nimport styled from 'styled-components';\n\nconst ImageContainer = styled.div<{ width?: string | number; height?: string | number }>`\n  position: relative;\n  width: ${props => typeof props.width === 'number' ? `${props.width}px` : props.width || '100%'};\n  height: ${props => typeof props.height === 'number' ? `${props.height}px` : props.height || 'auto'};\n  overflow: hidden;\n  \n  .lazy-image {\n    width: 100%;\n    height: 100%;\n    object-fit: cover;\n    transition: opacity 0.3s ease;\n    \n    &.loading {\n      opacity: 0;\n    }\n    \n    &.loaded {\n      opacity: 1;\n    }\n    \n    &.error {\n      opacity: 0;\n    }\n  }\n  \n  .placeholder {\n    position: absolute;\n    top: 0;\n    left: 0;\n    width: 100%;\n    height: 100%;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    background-color: #f5f5f5;\n    color: #999;\n    font-size: 14px;\n    \n    &.hidden {\n      display: none;\n    }\n  }\n  \n  .skeleton {\n    position: absolute;\n    top: 0;\n    left: 0;\n    width: 100%;\n    height: 100%;\n    \n    &.hidden {\n      display: none;\n    }\n  }\n`;\n\ninterface LazyImageProps {\n  src: string;\n  alt?: string;\n  width?: string | number;\n  height?: string | number;\n  placeholder?: React.ReactNode;\n  fallback?: string;\n  className?: string;\n  style?: React.CSSProperties;\n  onClick?: () => void;\n  onLoad?: () => void;\n  onError?: () => void;\n  threshold?: number; // Intersection observer threshold\n  rootMargin?: string; // Intersection observer root margin\n}\n\nconst LazyImage: React.FC<LazyImageProps> = ({\n  src,\n  alt = '',\n  width,\n  height,\n  placeholder,\n  fallback = '/images/placeholder.png',\n  className,\n  style,\n  onClick,\n  onLoad,\n  onError,\n  threshold = 0.1,\n  rootMargin = '50px'\n}) => {\n  const [imageState, setImageState] = useState<'loading' | 'loaded' | 'error'>('loading');\n  const [imageSrc, setImageSrc] = useState<string>('');\n  const [isInView, setIsInView] = useState(false);\n  const mountedRef = useRef(true);\n  const imgRef = useRef<HTMLImageElement>(null);\n  const containerRef = useRef<HTMLDivElement>(null);\n\n  // 组件清理\n  useEffect(() => {\n    return () => {\n      mountedRef.current = false;\n    };\n  }, []);\n\n  // Intersection Observer for lazy loading\n  useEffect(() => {\n    const observer = new IntersectionObserver(\n      (entries) => {\n        const [entry] = entries;\n        if (entry.isIntersecting && mountedRef.current) {\n          setIsInView(true);\n          observer.disconnect();\n        }\n      },\n      {\n        threshold,\n        rootMargin\n      }\n    );\n\n    if (containerRef.current) {\n      observer.observe(containerRef.current);\n    }\n\n    return () => {\n      observer.disconnect();\n    };\n  }, [threshold, rootMargin]);\n\n  // Load image when in view\n  useEffect(() => {\n    if (isInView && src) {\n      setImageSrc(src);\n    }\n  }, [isInView, src]);\n\n  // Handle image load\n  const handleImageLoad = useCallback(() => {\n    if (mountedRef.current) {\n      setImageState('loaded');\n      onLoad?.();\n    }\n  }, [onLoad]);\n\n  // Handle image error\n  const handleImageError = useCallback(() => {\n    if (mountedRef.current) {\n      setImageState('error');\n      if (fallback && fallback !== imageSrc) {\n        setImageSrc(fallback);\n        setImageState('loading');\n      } else {\n        onError?.();\n      }\n    }\n  }, [fallback, imageSrc, onError]);\n\n  // Preload image\n  useEffect(() => {\n    if (imageSrc) {\n      const img = new Image();\n      img.onload = handleImageLoad;\n      img.onerror = handleImageError;\n      img.src = imageSrc;\n      \n      return () => {\n        img.onload = null;\n        img.onerror = null;\n      };\n    }\n  }, [imageSrc, handleImageLoad, handleImageError]);\n\n  const showSkeleton = imageState === 'loading' && !placeholder;\n  const showPlaceholder = imageState === 'loading' && placeholder;\n  const showImage = imageState === 'loaded';\n  const showErrorPlaceholder = imageState === 'error' && !fallback;\n\n  return (\n    <ImageContainer\n      ref={containerRef}\n      width={width}\n      height={height}\n      className={className}\n      style={style}\n      onClick={onClick}\n    >\n      {/* 骨架屏 */}\n      {showSkeleton && (\n        <div className=\"skeleton\">\n          <Skeleton.Image style={{ width: '100%', height: '100%' }} />\n        </div>\n      )}\n\n      {/* 自定义占位符 */}\n      {showPlaceholder && (\n        <div className=\"placeholder\">\n          {placeholder}\n        </div>\n      )}\n\n      {/* 错误占位符 */}\n      {showErrorPlaceholder && (\n        <div className=\"placeholder\">\n          图片加载失败\n        </div>\n      )}\n\n      {/* 实际图片 */}\n      {imageSrc && (\n        <img\n          ref={imgRef}\n          src={imageSrc}\n          alt={alt}\n          className={`lazy-image ${imageState}`}\n          style={{ display: showImage ? 'block' : 'none' }}\n        />\n      )}\n    </ImageContainer>\n  );\n};\n\nexport default LazyImage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AACvE,SAASC,QAAQ,QAAQ,MAAM;AAC/B,OAAOC,MAAM,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvC,MAAMC,cAAc,GAAGH,MAAM,CAACI,GAA0D;AACxF;AACA,WAAWC,KAAK,IAAI,OAAOA,KAAK,CAACC,KAAK,KAAK,QAAQ,GAAG,GAAGD,KAAK,CAACC,KAAK,IAAI,GAAGD,KAAK,CAACC,KAAK,IAAI,MAAM;AAChG,YAAYD,KAAK,IAAI,OAAOA,KAAK,CAACE,MAAM,KAAK,QAAQ,GAAG,GAAGF,KAAK,CAACE,MAAM,IAAI,GAAGF,KAAK,CAACE,MAAM,IAAI,MAAM;AACpG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GAtDIL,cAAc;AAwEpB,MAAMM,SAAmC,GAAGA,CAAC;EAC3CC,GAAG;EACHC,GAAG,GAAG,EAAE;EACRL,KAAK;EACLC,MAAM;EACNK,WAAW;EACXC,QAAQ,GAAG,yBAAyB;EACpCC,SAAS;EACTC,KAAK;EACLC,OAAO;EACPC,MAAM;EACNC,OAAO;EACPC,SAAS,GAAG,GAAG;EACfC,UAAU,GAAG;AACf,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAG5B,QAAQ,CAAiC,SAAS,CAAC;EACvF,MAAM,CAAC6B,QAAQ,EAAEC,WAAW,CAAC,GAAG9B,QAAQ,CAAS,EAAE,CAAC;EACpD,MAAM,CAAC+B,QAAQ,EAAEC,WAAW,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAMiC,UAAU,GAAGhC,MAAM,CAAC,IAAI,CAAC;EAC/B,MAAMiC,MAAM,GAAGjC,MAAM,CAAmB,IAAI,CAAC;EAC7C,MAAMkC,YAAY,GAAGlC,MAAM,CAAiB,IAAI,CAAC;;EAEjD;EACAC,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACX+B,UAAU,CAACG,OAAO,GAAG,KAAK;IAC5B,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAlC,SAAS,CAAC,MAAM;IACd,MAAMmC,QAAQ,GAAG,IAAIC,oBAAoB,CACtCC,OAAO,IAAK;MACX,MAAM,CAACC,KAAK,CAAC,GAAGD,OAAO;MACvB,IAAIC,KAAK,CAACC,cAAc,IAAIR,UAAU,CAACG,OAAO,EAAE;QAC9CJ,WAAW,CAAC,IAAI,CAAC;QACjBK,QAAQ,CAACK,UAAU,CAAC,CAAC;MACvB;IACF,CAAC,EACD;MACElB,SAAS;MACTC;IACF,CACF,CAAC;IAED,IAAIU,YAAY,CAACC,OAAO,EAAE;MACxBC,QAAQ,CAACM,OAAO,CAACR,YAAY,CAACC,OAAO,CAAC;IACxC;IAEA,OAAO,MAAM;MACXC,QAAQ,CAACK,UAAU,CAAC,CAAC;IACvB,CAAC;EACH,CAAC,EAAE,CAAClB,SAAS,EAAEC,UAAU,CAAC,CAAC;;EAE3B;EACAvB,SAAS,CAAC,MAAM;IACd,IAAI6B,QAAQ,IAAIhB,GAAG,EAAE;MACnBe,WAAW,CAACf,GAAG,CAAC;IAClB;EACF,CAAC,EAAE,CAACgB,QAAQ,EAAEhB,GAAG,CAAC,CAAC;;EAEnB;EACA,MAAM6B,eAAe,GAAGzC,WAAW,CAAC,MAAM;IACxC,IAAI8B,UAAU,CAACG,OAAO,EAAE;MACtBR,aAAa,CAAC,QAAQ,CAAC;MACvBN,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAG,CAAC;IACZ;EACF,CAAC,EAAE,CAACA,MAAM,CAAC,CAAC;;EAEZ;EACA,MAAMuB,gBAAgB,GAAG1C,WAAW,CAAC,MAAM;IACzC,IAAI8B,UAAU,CAACG,OAAO,EAAE;MACtBR,aAAa,CAAC,OAAO,CAAC;MACtB,IAAIV,QAAQ,IAAIA,QAAQ,KAAKW,QAAQ,EAAE;QACrCC,WAAW,CAACZ,QAAQ,CAAC;QACrBU,aAAa,CAAC,SAAS,CAAC;MAC1B,CAAC,MAAM;QACLL,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAG,CAAC;MACb;IACF;EACF,CAAC,EAAE,CAACL,QAAQ,EAAEW,QAAQ,EAAEN,OAAO,CAAC,CAAC;;EAEjC;EACArB,SAAS,CAAC,MAAM;IACd,IAAI2B,QAAQ,EAAE;MACZ,MAAMiB,GAAG,GAAG,IAAIC,KAAK,CAAC,CAAC;MACvBD,GAAG,CAACE,MAAM,GAAGJ,eAAe;MAC5BE,GAAG,CAACG,OAAO,GAAGJ,gBAAgB;MAC9BC,GAAG,CAAC/B,GAAG,GAAGc,QAAQ;MAElB,OAAO,MAAM;QACXiB,GAAG,CAACE,MAAM,GAAG,IAAI;QACjBF,GAAG,CAACG,OAAO,GAAG,IAAI;MACpB,CAAC;IACH;EACF,CAAC,EAAE,CAACpB,QAAQ,EAAEe,eAAe,EAAEC,gBAAgB,CAAC,CAAC;EAEjD,MAAMK,YAAY,GAAGvB,UAAU,KAAK,SAAS,IAAI,CAACV,WAAW;EAC7D,MAAMkC,eAAe,GAAGxB,UAAU,KAAK,SAAS,IAAIV,WAAW;EAC/D,MAAMmC,SAAS,GAAGzB,UAAU,KAAK,QAAQ;EACzC,MAAM0B,oBAAoB,GAAG1B,UAAU,KAAK,OAAO,IAAI,CAACT,QAAQ;EAEhE,oBACEX,OAAA,CAACC,cAAc;IACb8C,GAAG,EAAEnB,YAAa;IAClBxB,KAAK,EAAEA,KAAM;IACbC,MAAM,EAAEA,MAAO;IACfO,SAAS,EAAEA,SAAU;IACrBC,KAAK,EAAEA,KAAM;IACbC,OAAO,EAAEA,OAAQ;IAAAkC,QAAA,GAGhBL,YAAY,iBACX3C,OAAA;MAAKY,SAAS,EAAC,UAAU;MAAAoC,QAAA,eACvBhD,OAAA,CAACH,QAAQ,CAAC2C,KAAK;QAAC3B,KAAK,EAAE;UAAET,KAAK,EAAE,MAAM;UAAEC,MAAM,EAAE;QAAO;MAAE;QAAA4C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzD,CACN,EAGAR,eAAe,iBACd5C,OAAA;MAAKY,SAAS,EAAC,aAAa;MAAAoC,QAAA,EACzBtC;IAAW;MAAAuC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACT,CACN,EAGAN,oBAAoB,iBACnB9C,OAAA;MAAKY,SAAS,EAAC,aAAa;MAAAoC,QAAA,EAAC;IAE7B;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CACN,EAGA9B,QAAQ,iBACPtB,OAAA;MACE+C,GAAG,EAAEpB,MAAO;MACZnB,GAAG,EAAEc,QAAS;MACdb,GAAG,EAAEA,GAAI;MACTG,SAAS,EAAE,cAAcQ,UAAU,EAAG;MACtCP,KAAK,EAAE;QAAEwC,OAAO,EAAER,SAAS,GAAG,OAAO,GAAG;MAAO;IAAE;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClD,CACF;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACa,CAAC;AAErB,CAAC;AAACjC,EAAA,CAhJIZ,SAAmC;AAAA+C,GAAA,GAAnC/C,SAAmC;AAkJzC,eAAeA,SAAS;AAAC,IAAAD,EAAA,EAAAgD,GAAA;AAAAC,YAAA,CAAAjD,EAAA;AAAAiD,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}