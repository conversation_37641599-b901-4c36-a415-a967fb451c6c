const { Sequelize } = require('sequelize');
require('dotenv').config();

// 验证生产环境必需的环境变量
if (process.env.NODE_ENV === 'production') {
  const requiredEnvVars = ['DB_PASSWORD', 'DB_HOST', 'DB_NAME'];
  const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
  
  if (missingVars.length > 0) {
    throw new Error(`Missing required environment variables in production: ${missingVars.join(', ')}`);
  }
}

const sequelize = new Sequelize({
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 5432,
  database: process.env.DB_NAME || 'book_trading',
  username: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASSWORD || (process.env.NODE_ENV === 'development' ? 'postgres' : ''),
  dialect: 'postgres',
  logging: process.env.NODE_ENV === 'development' ? console.log : false,
  dialectOptions: {
    connectTimeout: 60000, // 60秒连接超时
    idleTimeoutMillis: 30000, // 30秒空闲超时
    statement_timeout: 30000, // 30秒查询超时
    ssl: process.env.DB_SSL === 'true' ? {
      require: true,
      rejectUnauthorized: false
    } : false
  },
  retry: {
    max: 3, // 最大重试次数
    timeout: 5000 // 重试超时
  },
  pool: {
    max: parseInt(process.env.DB_POOL_MAX) || 20, // 最大连接数
    min: parseInt(process.env.DB_POOL_MIN) || 2,  // 最小连接数
    acquire: parseInt(process.env.DB_POOL_ACQUIRE) || 60000, // 60秒超时
    idle: parseInt(process.env.DB_POOL_IDLE) || 10000, // 10秒空闲超时
    evict: parseInt(process.env.DB_POOL_EVICT) || 1000, // 每秒检查一次
    handleDisconnects: true, // 自动处理断开连接
    validate: (connection) => {
      // 连接健康检查
      return connection && !connection._invalid;
    }
  },
  define: {
    timestamps: true,
    underscored: true,
    freezeTableName: true
  }
});

// 连接池监控
setInterval(() => {
  if (process.env.NODE_ENV === 'development') {
    const pool = sequelize.connectionManager.pool;
    console.log('DB Pool Stats:', {
      used: pool.used.length,
      waiting: pool.pending.length,
      available: pool.available.length,
      max: pool.options.max
    });
  }
}, 30000); // 每30秒检查一次

// 优雅关闭连接
process.on('SIGINT', async () => {
  console.log('Closing database connection...');
  await sequelize.close();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log('Closing database connection...');
  await sequelize.close();
  process.exit(0);
});

module.exports = sequelize;
