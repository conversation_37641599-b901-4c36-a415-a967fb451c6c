import React, { useState, useRef, useEffect } from 'react';
import '../../styles/responsive.css';

interface MobileSearchProps {
  placeholder?: string;
  onSearch: (value: string) => void;
  suggestions?: string[];
  onSuggestionClick?: (suggestion: string) => void;
  className?: string;
  autoFocus?: boolean;
  value?: string;
  onChange?: (value: string) => void;
}

const MobileSearch: React.FC<MobileSearchProps> = ({
  placeholder = '搜索...',
  onSearch,
  suggestions = [],
  onSuggestionClick,
  className = '',
  autoFocus = false,
  value: controlledValue,
  onChange
}) => {
  const [internalValue, setInternalValue] = useState('');
  const [isFocused, setIsFocused] = useState(false);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);
  const suggestionsRef = useRef<HTMLDivElement>(null);

  const value = controlledValue !== undefined ? controlledValue : internalValue;

  useEffect(() => {
    if (autoFocus && inputRef.current) {
      inputRef.current.focus();
    }
  }, [autoFocus]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        suggestionsRef.current &&
        !suggestionsRef.current.contains(event.target as Node) &&
        !inputRef.current?.contains(event.target as Node)
      ) {
        setShowSuggestions(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    if (controlledValue === undefined) {
      setInternalValue(newValue);
    }
    onChange?.(newValue);
    setShowSuggestions(newValue.length > 0 && suggestions.length > 0);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSearch(value);
    setShowSuggestions(false);
    inputRef.current?.blur();
  };

  const handleClear = () => {
    if (controlledValue === undefined) {
      setInternalValue('');
    }
    onChange?.('');
    inputRef.current?.focus();
  };

  const handleSuggestionClick = (suggestion: string) => {
    if (controlledValue === undefined) {
      setInternalValue(suggestion);
    }
    onSuggestionClick?.(suggestion);
    onSearch(suggestion);
    setShowSuggestions(false);
  };

  return (
    <div className={`mobile-search-container ${className}`} style={{
      position: 'relative',
      width: '100%'
    }}>
      <form onSubmit={handleSubmit} style={{
        position: 'relative',
        width: '100%'
      }}>
        <div style={{
          position: 'relative',
          display: 'flex',
          alignItems: 'center',
          backgroundColor: isFocused ? '#fff' : '#f8f9fa',
          border: `2px solid ${isFocused ? '#007bff' : '#e0e0e0'}`,
          borderRadius: '24px',
          padding: '0 16px',
          transition: 'all 0.3s ease',
          boxShadow: isFocused ? '0 2px 8px rgba(0,123,255,0.2)' : 'none'
        }}>
          {/* 搜索图标 */}
          <span style={{
            color: isFocused ? '#007bff' : '#6c757d',
            marginRight: '12px',
            fontSize: '18px',
            transition: 'color 0.3s ease'
          }}>
            🔍
          </span>

          {/* 输入框 */}
          <input
            ref={inputRef}
            type="text"
            value={value}
            onChange={handleInputChange}
            onFocus={() => {
              setIsFocused(true);
              setShowSuggestions(value.length > 0 && suggestions.length > 0);
            }}
            onBlur={() => setIsFocused(false)}
            placeholder={placeholder}
            style={{
              flex: 1,
              border: 'none',
              outline: 'none',
              backgroundColor: 'transparent',
              padding: '12px 0',
              fontSize: '16px',
              color: '#333'
            }}
          />

          {/* 清除按钮 */}
          {value && (
            <button
              type="button"
              onClick={handleClear}
              className="touchable"
              style={{
                border: 'none',
                backgroundColor: 'transparent',
                padding: '8px',
                marginLeft: '8px',
                fontSize: '18px',
                color: '#6c757d',
                cursor: 'pointer'
              }}
            >
              ✕
            </button>
          )}

          {/* 搜索按钮 */}
          <button
            type="submit"
            className="btn-touch"
            style={{
              border: 'none',
              backgroundColor: '#007bff',
              color: '#fff',
              padding: '8px 16px',
              marginLeft: '8px',
              borderRadius: '20px',
              fontSize: '14px',
              fontWeight: '500',
              cursor: 'pointer'
            }}
          >
            搜索
          </button>
        </div>
      </form>

      {/* 搜索建议 */}
      {showSuggestions && suggestions.length > 0 && (
        <div 
          ref={suggestionsRef}
          className="search-suggestions"
          style={{
            position: 'absolute',
            top: '100%',
            left: 0,
            right: 0,
            marginTop: '8px',
            backgroundColor: '#fff',
            border: '1px solid #e0e0e0',
            borderRadius: '12px',
            boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
            maxHeight: '300px',
            overflowY: 'auto',
            zIndex: 100
          }}
        >
          {suggestions.map((suggestion, index) => (
            <div
              key={index}
              onClick={() => handleSuggestionClick(suggestion)}
              className="touchable"
              style={{
                padding: '12px 16px',
                cursor: 'pointer',
                borderBottom: index < suggestions.length - 1 ? '1px solid #f0f0f0' : 'none',
                transition: 'background-color 0.2s',
                display: 'flex',
                alignItems: 'center'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = '#f8f9fa';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = 'transparent';
              }}
            >
              <span style={{
                color: '#6c757d',
                marginRight: '12px',
                fontSize: '16px'
              }}>
                🔍
              </span>
              <span style={{
                flex: 1,
                color: '#333',
                fontSize: '15px'
              }}>
                {suggestion}
              </span>
              <span style={{
                color: '#6c757d',
                fontSize: '14px'
              }}>
                →
              </span>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default MobileSearch;