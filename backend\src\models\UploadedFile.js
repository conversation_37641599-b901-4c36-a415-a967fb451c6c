const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const UploadedFile = sequelize.define('UploadedFile', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  file_id: {
    type: DataTypes.STRING(64),
    unique: true,
    allowNull: false,
    comment: '文件唯一标识符'
  },
  filename: {
    type: DataTypes.STRING(255),
    allowNull: false,
    comment: '存储的文件名'
  },
  original_name: {
    type: DataTypes.STRING(255),
    allowNull: false,
    comment: '原始文件名'
  },
  mimetype: {
    type: DataTypes.STRING(100),
    allowNull: false,
    comment: 'MIME类型'
  },
  size: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '文件大小（字节）'
  },
  path: {
    type: DataTypes.STRING(500),
    allowNull: false,
    comment: '文件存储路径'
  },
  url: {
    type: DataTypes.STRING(255),
    allowNull: false,
    comment: '访问URL'
  },
  type: {
    type: DataTypes.ENUM('avatar', 'book', 'other'),
    defaultValue: 'other',
    comment: '文件类型'
  },
  uploaded_by: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    },
    comment: '上传者ID'
  },
  referenced: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: '是否被引用'
  },
  reference_count: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    comment: '引用次数'
  },
  metadata: {
    type: DataTypes.JSONB,
    defaultValue: {},
    comment: '文件元数据（如图片尺寸等）'
  },
  virus_scanned: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: '是否已进行病毒扫描'
  },
  virus_scan_result: {
    type: DataTypes.STRING(50),
    comment: '病毒扫描结果'
  },
  access_count: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    comment: '访问次数'
  },
  last_accessed_at: {
    type: DataTypes.DATE,
    comment: '最后访问时间'
  },
  expires_at: {
    type: DataTypes.DATE,
    comment: '过期时间'
  }
}, {
  tableName: 'uploaded_files',
  timestamps: true,
  underscored: true,
  indexes: [
    {
      fields: ['file_id']
    },
    {
      fields: ['uploaded_by']
    },
    {
      fields: ['type']
    },
    {
      fields: ['referenced']
    },
    {
      fields: ['created_at']
    },
    {
      fields: ['expires_at']
    }
  ],
  hooks: {
    beforeDestroy: async (file) => {
      // 在删除数据库记录前，尝试删除物理文件
      const fs = require('fs').promises;
      try {
        await fs.unlink(file.path);
      } catch (error) {
        console.error(`删除文件失败: ${file.path}`, error);
      }
    }
  }
});

// 定义关联
UploadedFile.associate = (models) => {
  UploadedFile.belongsTo(models.User, {
    foreignKey: 'uploaded_by',
    as: 'uploader'
  });
};

// 实例方法
UploadedFile.prototype.incrementAccessCount = async function() {
  this.access_count += 1;
  this.last_accessed_at = new Date();
  await this.save();
};

UploadedFile.prototype.markAsReferenced = async function() {
  this.referenced = true;
  this.reference_count += 1;
  await this.save();
};

UploadedFile.prototype.decrementReferenceCount = async function() {
  this.reference_count = Math.max(0, this.reference_count - 1);
  if (this.reference_count === 0) {
    this.referenced = false;
  }
  await this.save();
};

// 类方法
UploadedFile.cleanupExpiredFiles = async function() {
  const expiredFiles = await this.findAll({
    where: {
      expires_at: {
        [Op.lt]: new Date()
      }
    }
  });
  
  for (const file of expiredFiles) {
    await file.destroy();
  }
  
  return expiredFiles.length;
};

UploadedFile.cleanupUnreferencedFiles = async function(hoursOld = 24) {
  const cutoffDate = new Date();
  cutoffDate.setHours(cutoffDate.getHours() - hoursOld);
  
  const unreferencedFiles = await this.findAll({
    where: {
      referenced: false,
      created_at: {
        [Op.lt]: cutoffDate
      }
    }
  });
  
  for (const file of unreferencedFiles) {
    await file.destroy();
  }
  
  return unreferencedFiles.length;
};

module.exports = UploadedFile;