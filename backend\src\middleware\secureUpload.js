const multer = require('multer');
const path = require('path');
const fs = require('fs');
const crypto = require('crypto');
const sharp = require('sharp');
const { promisify } = require('util');
const unlinkAsync = promisify(fs.unlink);

// 文件类型的魔术数字验证
const MAGIC_NUMBERS = {
  jpg: 'ffd8ff',
  png: '89504e47',
  gif: '47494638',
  webp: '52494646'
};

// 允许的MIME类型
const ALLOWED_MIME_TYPES = [
  'image/jpeg',
  'image/jpg',
  'image/png',
  'image/gif',
  'image/webp'
];

// 允许的文件扩展名
const ALLOWED_EXTENSIONS = ['.jpg', '.jpeg', '.png', '.gif', '.webp'];

// 创建上传目录
const createUploadDir = (subDir) => {
  const uploadDir = process.env.UPLOAD_PATH || path.join(__dirname, '../../uploads');
  const fullPath = path.join(uploadDir, subDir);
  
  if (!fs.existsSync(fullPath)) {
    fs.mkdirSync(fullPath, { recursive: true, mode: 0o755 });
  }
  
  return fullPath;
};

// 验证文件的魔术数字
const verifyMagicNumber = async (filePath, mimeType) => {
  return new Promise((resolve, reject) => {
    const stream = fs.createReadStream(filePath, { start: 0, end: 8 });
    let fileSignature = '';
    
    stream.on('data', (chunk) => {
      fileSignature += chunk.toString('hex');
    });
    
    stream.on('end', () => {
      let isValid = false;
      
      if (mimeType.includes('jpeg') || mimeType.includes('jpg')) {
        isValid = fileSignature.startsWith(MAGIC_NUMBERS.jpg);
      } else if (mimeType.includes('png')) {
        isValid = fileSignature.startsWith(MAGIC_NUMBERS.png);
      } else if (mimeType.includes('gif')) {
        isValid = fileSignature.startsWith(MAGIC_NUMBERS.gif);
      } else if (mimeType.includes('webp')) {
        isValid = fileSignature.includes(MAGIC_NUMBERS.webp);
      }
      
      resolve(isValid);
    });
    
    stream.on('error', reject);
  });
};

// 处理和清理图片（移除EXIF数据，调整大小）
const processImage = async (filePath, outputPath, options = {}) => {
  try {
    const { maxWidth = 2048, maxHeight = 2048, quality = 90 } = options;
    
    // 使用sharp处理图片
    await sharp(filePath)
      .rotate() // 自动旋转基于EXIF方向
      .resize(maxWidth, maxHeight, {
        fit: 'inside',
        withoutEnlargement: true
      })
      .jpeg({ quality, progressive: true })
      .toFile(outputPath);
    
    // 删除原始文件
    await unlinkAsync(filePath);
    
    return outputPath;
  } catch (error) {
    // 如果处理失败，删除文件
    try {
      await unlinkAsync(filePath);
    } catch (e) {}
    throw error;
  }
};

// 生成安全的文件名
const generateSecureFilename = (originalname) => {
  const ext = path.extname(originalname).toLowerCase();
  const timestamp = Date.now();
  const randomBytes = crypto.randomBytes(16).toString('hex');
  const hash = crypto.createHash('sha256')
    .update(`${timestamp}-${randomBytes}-${originalname}`)
    .digest('hex')
    .substring(0, 32);
  
  return `${timestamp}-${hash}${ext}`;
};

// 配置存储
const storage = multer.diskStorage({
  destination: async (req, file, cb) => {
    try {
      let subDir = 'others';
      
      if (file.fieldname === 'avatar') {
        subDir = 'avatars';
      } else if (file.fieldname === 'book_images') {
        subDir = 'books';
      }
      
      // 防止目录遍历
      const safeSubDir = path.normalize(subDir).replace(/^(\.\.(\/|\\|$))+/, '');
      const uploadPath = createUploadDir(safeSubDir);
      
      // 创建临时目录用于文件验证
      const tempDir = createUploadDir('temp');
      
      cb(null, tempDir);
    } catch (error) {
      cb(error);
    }
  },
  filename: (req, file, cb) => {
    try {
      const secureFilename = generateSecureFilename(file.originalname);
      cb(null, secureFilename);
    } catch (error) {
      cb(error);
    }
  }
});

// 文件过滤器
const fileFilter = async (req, file, cb) => {
  try {
    // 检查文件名安全性
    const filename = file.originalname;
    if (filename.includes('..') || filename.includes('\0')) {
      return cb(new Error('非法文件名'), false);
    }
    
    // 检查MIME类型
    if (!ALLOWED_MIME_TYPES.includes(file.mimetype.toLowerCase())) {
      return cb(new Error('不支持的文件类型'), false);
    }
    
    // 检查扩展名
    const ext = path.extname(filename).toLowerCase();
    if (!ALLOWED_EXTENSIONS.includes(ext)) {
      return cb(new Error('不支持的文件扩展名'), false);
    }
    
    cb(null, true);
  } catch (error) {
    cb(error, false);
  }
};

// 创建multer实例
const createUploadMiddleware = (options = {}) => {
  const {
    fieldName = 'file',
    maxFiles = 1,
    maxFileSize = 5 * 1024 * 1024, // 5MB
    allowedTypes = ALLOWED_MIME_TYPES
  } = options;
  
  return multer({
    storage: storage,
    limits: {
      fileSize: maxFileSize,
      files: maxFiles,
      fields: 10,
      headerPairs: 100
    },
    fileFilter: fileFilter
  });
};

// 文件验证和处理中间件
const validateAndProcessFile = async (req, res, next) => {
  if (!req.file && !req.files) {
    return next();
  }
  
  const files = req.file ? [req.file] : req.files;
  const processedFiles = [];
  const uploadDir = process.env.UPLOAD_PATH || path.join(__dirname, '../../uploads');
  
  try {
    for (const file of files) {
      const tempPath = file.path;
      
      // 验证魔术数字
      const isValidMagicNumber = await verifyMagicNumber(tempPath, file.mimetype);
      if (!isValidMagicNumber) {
        await unlinkAsync(tempPath);
        return res.status(400).json({
          success: false,
          message: '文件内容与声明的类型不匹配'
        });
      }
      
      // 确定目标目录
      let subDir = 'others';
      if (file.fieldname === 'avatar') {
        subDir = 'avatars';
      } else if (file.fieldname === 'book_images') {
        subDir = 'books';
      }
      
      // 生成最终文件名和路径
      const finalFilename = generateSecureFilename(file.originalname);
      const finalDir = path.join(uploadDir, subDir);
      const finalPath = path.join(finalDir, finalFilename);
      
      // 处理图片（移除EXIF，调整大小）
      await processImage(tempPath, finalPath, {
        maxWidth: file.fieldname === 'avatar' ? 512 : 2048,
        maxHeight: file.fieldname === 'avatar' ? 512 : 2048,
        quality: 85
      });
      
      // 更新文件信息
      file.filename = finalFilename;
      file.path = finalPath;
      file.destination = finalDir;
      
      // 生成安全的URL（使用ID而不是直接文件名）
      const fileId = crypto.randomBytes(16).toString('hex');
      file.secureUrl = `/api/files/${fileId}`;
      file.fileId = fileId;
      
      // 存储文件映射（在实际应用中应存储到数据库）
      // 这里只是示例，实际应该使用数据库
      if (!req.uploadedFiles) {
        req.uploadedFiles = {};
      }
      req.uploadedFiles[fileId] = {
        path: finalPath,
        filename: finalFilename,
        mimetype: file.mimetype,
        size: file.size,
        uploadedBy: req.user ? req.user.id : null,
        uploadedAt: new Date()
      };
      
      processedFiles.push(file);
    }
    
    next();
  } catch (error) {
    // 清理所有文件
    for (const file of files) {
      try {
        await unlinkAsync(file.path);
      } catch (e) {}
    }
    
    res.status(500).json({
      success: false,
      message: '文件处理失败',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// 速率限制中间件
const createRateLimiter = (options = {}) => {
  const {
    windowMs = 15 * 60 * 1000, // 15分钟
    maxRequests = 10,
    message = '上传请求过于频繁，请稍后再试'
  } = options;
  
  const requests = new Map();
  
  return (req, res, next) => {
    const key = req.user ? req.user.id : req.ip;
    const now = Date.now();
    
    // 清理过期的记录
    for (const [k, timestamps] of requests.entries()) {
      const validTimestamps = timestamps.filter(t => now - t < windowMs);
      if (validTimestamps.length === 0) {
        requests.delete(k);
      } else {
        requests.set(k, validTimestamps);
      }
    }
    
    // 检查当前用户的请求
    const userRequests = requests.get(key) || [];
    const recentRequests = userRequests.filter(t => now - t < windowMs);
    
    if (recentRequests.length >= maxRequests) {
      return res.status(429).json({
        success: false,
        message: message
      });
    }
    
    // 记录新请求
    recentRequests.push(now);
    requests.set(key, recentRequests);
    
    next();
  };
};

// 文件访问控制中间件
const fileAccessControl = async (req, res, next) => {
  const { fileId } = req.params;
  
  // 从数据库获取文件信息（这里只是示例）
  const fileInfo = req.uploadedFiles && req.uploadedFiles[fileId];
  
  if (!fileInfo) {
    return res.status(404).json({
      success: false,
      message: '文件不存在'
    });
  }
  
  // 检查文件访问权限
  if (fileInfo.uploadedBy && req.user && fileInfo.uploadedBy !== req.user.id) {
    // 这里可以添加更复杂的权限逻辑
    return res.status(403).json({
      success: false,
      message: '无权访问此文件'
    });
  }
  
  req.fileInfo = fileInfo;
  next();
};

module.exports = {
  createUploadMiddleware,
  validateAndProcessFile,
  createRateLimiter,
  fileAccessControl,
  generateSecureFilename,
  processImage,
  verifyMagicNumber
};