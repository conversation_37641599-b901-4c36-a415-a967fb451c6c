const express = require('express');
const router = express.Router();
const { authenticate } = require('../middleware/auth');
const {
  createUploadMiddleware,
  validateAndProcessFile,
  createRateLimiter,
  fileAccessControl
} = require('../middleware/secureUpload');
const { User, Book, UploadedFile } = require('../models');
const path = require('path');
const fs = require('fs');
const { promisify } = require('util');
const unlinkAsync = promisify(fs.unlink);

// 创建不同用途的上传中间件
const avatarUpload = createUploadMiddleware({
  fieldName: 'avatar',
  maxFiles: 1,
  maxFileSize: 2 * 1024 * 1024 // 2MB for avatars
});

const bookImagesUpload = createUploadMiddleware({
  fieldName: 'book_images',
  maxFiles: 5,
  maxFileSize: 5 * 1024 * 1024 // 5MB for book images
});

// 速率限制
const uploadRateLimiter = createRateLimiter({
  windowMs: 15 * 60 * 1000, // 15分钟
  maxRequests: 10
});

// 上传头像路由
router.post('/avatar',
  authenticate,
  uploadRateLimiter,
  avatarUpload.single('avatar'),
  validateAndProcessFile,
  async (req, res) => {
    try {
      if (!req.file) {
        return res.status(400).json({
          success: false,
          message: '请选择要上传的头像文件'
        });
      }

      const file = req.file;
      
      // 保存文件信息到数据库
      const uploadedFile = await UploadedFile.create({
        file_id: file.fileId,
        filename: file.filename,
        original_name: file.originalname,
        mimetype: file.mimetype,
        size: file.size,
        path: file.path,
        url: file.secureUrl,
        type: 'avatar',
        uploaded_by: req.user.id,
        metadata: {
          width: file.width,
          height: file.height
        }
      });

      // 删除用户旧头像
      const user = await User.findByPk(req.user.id);
      if (user.avatar_file_id) {
        try {
          const oldFile = await UploadedFile.findOne({
            where: { file_id: user.avatar_file_id }
          });
          if (oldFile) {
            await unlinkAsync(oldFile.path);
            await oldFile.destroy();
          }
        } catch (error) {
          console.error('删除旧头像失败:', error);
        }
      }

      // 更新用户头像
      await user.update({
        avatar: file.secureUrl,
        avatar_file_id: file.fileId
      });

      res.json({
        success: true,
        message: '头像上传成功',
        data: {
          url: file.secureUrl,
          fileId: file.fileId,
          size: file.size
        }
      });
    } catch (error) {
      console.error('上传头像错误:', error);
      
      // 清理已上传的文件
      if (req.file) {
        try {
          await unlinkAsync(req.file.path);
        } catch (e) {}
      }
      
      res.status(500).json({
        success: false,
        message: '上传头像失败'
      });
    }
  }
);

// 上传图书图片路由
router.post('/book-images',
  authenticate,
  uploadRateLimiter,
  bookImagesUpload.array('book_images', 5),
  validateAndProcessFile,
  async (req, res) => {
    try {
      if (!req.files || req.files.length === 0) {
        return res.status(400).json({
          success: false,
          message: '请选择要上传的图片文件'
        });
      }

      const uploadedFiles = [];
      
      // 保存所有文件信息到数据库
      for (const file of req.files) {
        const uploadedFile = await UploadedFile.create({
          file_id: file.fileId,
          filename: file.filename,
          original_name: file.originalname,
          mimetype: file.mimetype,
          size: file.size,
          path: file.path,
          url: file.secureUrl,
          type: 'book',
          uploaded_by: req.user.id,
          metadata: {
            width: file.width,
            height: file.height
          }
        });
        
        uploadedFiles.push({
          url: file.secureUrl,
          fileId: file.fileId,
          size: file.size,
          originalname: file.originalname
        });
      }

      res.json({
        success: true,
        message: '图片上传成功',
        data: {
          files: uploadedFiles,
          count: uploadedFiles.length
        }
      });
    } catch (error) {
      console.error('上传图书图片错误:', error);
      
      // 清理已上传的文件
      if (req.files) {
        for (const file of req.files) {
          try {
            await unlinkAsync(file.path);
          } catch (e) {}
        }
      }
      
      res.status(500).json({
        success: false,
        message: '上传图片失败'
      });
    }
  }
);

// 安全的文件访问路由
router.get('/files/:fileId',
  authenticate,
  fileAccessControl,
  async (req, res) => {
    try {
      const { fileId } = req.params;
      
      // 从数据库获取文件信息
      const uploadedFile = await UploadedFile.findOne({
        where: { file_id: fileId }
      });
      
      if (!uploadedFile) {
        return res.status(404).json({
          success: false,
          message: '文件不存在'
        });
      }
      
      // 检查文件访问权限
      if (uploadedFile.uploaded_by !== req.user.id) {
        // 检查是否是公开的文件（如图书图片）
        if (uploadedFile.type === 'book') {
          // 检查用户是否有权查看该图书
          const book = await Book.findOne({
            where: {
              [Op.or]: [
                { cover_image: uploadedFile.url },
                { images: { [Op.contains]: [uploadedFile.url] } }
              ]
            }
          });
          
          if (!book || book.status !== '上架') {
            return res.status(403).json({
              success: false,
              message: '无权访问此文件'
            });
          }
        } else {
          return res.status(403).json({
            success: false,
            message: '无权访问此文件'
          });
        }
      }
      
      // 检查文件是否存在
      if (!fs.existsSync(uploadedFile.path)) {
        await uploadedFile.destroy();
        return res.status(404).json({
          success: false,
          message: '文件不存在'
        });
      }
      
      // 设置适当的响应头
      res.setHeader('Content-Type', uploadedFile.mimetype);
      res.setHeader('Content-Length', uploadedFile.size);
      res.setHeader('Cache-Control', 'public, max-age=31536000'); // 1年缓存
      
      // 发送文件
      res.sendFile(uploadedFile.path);
    } catch (error) {
      console.error('获取文件错误:', error);
      res.status(500).json({
        success: false,
        message: '获取文件失败'
      });
    }
  }
);

// 删除文件路由
router.delete('/files/:fileId',
  authenticate,
  async (req, res) => {
    try {
      const { fileId } = req.params;
      
      // 从数据库获取文件信息
      const uploadedFile = await UploadedFile.findOne({
        where: { file_id: fileId }
      });
      
      if (!uploadedFile) {
        return res.status(404).json({
          success: false,
          message: '文件不存在'
        });
      }
      
      // 检查文件所有权
      if (uploadedFile.uploaded_by !== req.user.id && req.user.role !== 'admin') {
        return res.status(403).json({
          success: false,
          message: '无权删除此文件'
        });
      }
      
      // 删除物理文件
      if (fs.existsSync(uploadedFile.path)) {
        await unlinkAsync(uploadedFile.path);
      }
      
      // 删除数据库记录
      await uploadedFile.destroy();
      
      res.json({
        success: true,
        message: '文件删除成功'
      });
    } catch (error) {
      console.error('删除文件错误:', error);
      res.status(500).json({
        success: false,
        message: '删除文件失败'
      });
    }
  }
);

// 获取用户上传的文件列表
router.get('/my-files',
  authenticate,
  async (req, res) => {
    try {
      const { type, page = 1, limit = 20 } = req.query;
      const offset = (page - 1) * limit;
      
      const where = { uploaded_by: req.user.id };
      if (type) {
        where.type = type;
      }
      
      const { count, rows } = await UploadedFile.findAndCountAll({
        where,
        limit: parseInt(limit),
        offset: parseInt(offset),
        order: [['created_at', 'DESC']],
        attributes: ['file_id', 'original_name', 'url', 'size', 'type', 'created_at']
      });
      
      res.json({
        success: true,
        data: {
          files: rows,
          pagination: {
            current_page: parseInt(page),
            total_pages: Math.ceil(count / limit),
            total_items: count,
            items_per_page: parseInt(limit)
          }
        }
      });
    } catch (error) {
      console.error('获取文件列表错误:', error);
      res.status(500).json({
        success: false,
        message: '获取文件列表失败'
      });
    }
  }
);

// 清理未使用的文件（管理员功能）
router.post('/cleanup',
  authenticate,
  async (req, res) => {
    try {
      if (req.user.role !== 'admin') {
        return res.status(403).json({
          success: false,
          message: '无权执行此操作'
        });
      }
      
      // 查找24小时前上传但未被引用的文件
      const cutoffDate = new Date();
      cutoffDate.setHours(cutoffDate.getHours() - 24);
      
      const unusedFiles = await UploadedFile.findAll({
        where: {
          created_at: { [Op.lt]: cutoffDate },
          referenced: false
        }
      });
      
      let deletedCount = 0;
      let failedCount = 0;
      
      for (const file of unusedFiles) {
        try {
          if (fs.existsSync(file.path)) {
            await unlinkAsync(file.path);
          }
          await file.destroy();
          deletedCount++;
        } catch (error) {
          console.error(`清理文件失败: ${file.file_id}`, error);
          failedCount++;
        }
      }
      
      res.json({
        success: true,
        message: `清理完成，删除了 ${deletedCount} 个文件，${failedCount} 个失败`,
        data: {
          deleted: deletedCount,
          failed: failedCount,
          total: unusedFiles.length
        }
      });
    } catch (error) {
      console.error('清理文件错误:', error);
      res.status(500).json({
        success: false,
        message: '清理文件失败'
      });
    }
  }
);

// 错误处理中间件
router.use((error, req, res, next) => {
  if (error.code === 'LIMIT_FILE_SIZE') {
    return res.status(400).json({
      success: false,
      message: '文件大小超出限制'
    });
  }
  
  if (error.code === 'LIMIT_FILE_COUNT') {
    return res.status(400).json({
      success: false,
      message: '文件数量超出限制'
    });
  }
  
  if (error.code === 'LIMIT_UNEXPECTED_FILE') {
    return res.status(400).json({
      success: false,
      message: '不允许的字段名称'
    });
  }
  
  res.status(400).json({
    success: false,
    message: error.message || '文件上传失败'
  });
});

module.exports = router;