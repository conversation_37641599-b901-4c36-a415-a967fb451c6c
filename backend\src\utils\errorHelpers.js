const { 
  AppError,
  ValidationError,
  AuthenticationError,
  AuthorizationError,
  NotFoundError,
  ConflictError,
  RateLimitError,
  ExternalServiceError,
  asyncHandler
} = require('../middleware/errorHandlerEnhanced');

// 统一的错误响应格式化
const formatErrorResponse = (error) => {
  return {
    success: false,
    error: {
      message: error.message,
      code: error.code || 'UNKNOWN_ERROR',
      details: error.details || {}
    }
  };
};

// 验证请求参数
const validateRequest = (schema) => {
  return asyncHandler(async (req, res, next) => {
    try {
      const validated = await schema.validateAsync(req.body, {
        abortEarly: false,
        stripUnknown: true
      });
      req.body = validated;
      next();
    } catch (error) {
      const details = error.details.reduce((acc, detail) => {
        acc[detail.path.join('.')] = detail.message;
        return acc;
      }, {});
      
      throw new ValidationError('请求参数验证失败', { fields: details });
    }
  });
};

// 验证查询参数
const validateQuery = (schema) => {
  return asyncHandler(async (req, res, next) => {
    try {
      const validated = await schema.validateAsync(req.query, {
        abortEarly: false,
        stripUnknown: true
      });
      req.query = validated;
      next();
    } catch (error) {
      const details = error.details.reduce((acc, detail) => {
        acc[detail.path.join('.')] = detail.message;
        return acc;
      }, {});
      
      throw new ValidationError('查询参数验证失败', { fields: details });
    }
  });
};

// 检查资源所有权
const checkOwnership = (getResourceFn, ownerField = 'user_id') => {
  return asyncHandler(async (req, res, next) => {
    const resource = await getResourceFn(req);
    
    if (!resource) {
      throw new NotFoundError('资源');
    }
    
    // 管理员有所有权限
    if (req.user.role === 'admin' || req.user.role === 'super_admin') {
      req.resource = resource;
      return next();
    }
    
    // 检查所有权
    if (resource[ownerField] !== req.user.id) {
      throw new AuthorizationError('您没有权限访问此资源');
    }
    
    req.resource = resource;
    next();
  });
};

// 事务包装器
const withTransaction = (fn) => {
  return asyncHandler(async (req, res, next) => {
    const sequelize = require('../config/database');
    const transaction = await sequelize.transaction();
    
    try {
      req.transaction = transaction;
      const result = await fn(req, res, next);
      await transaction.commit();
      return result;
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  });
};

// 缓存包装器
const withCache = (keyGenerator, ttl = 300) => {
  return (fn) => {
    return asyncHandler(async (req, res, next) => {
      const cacheService = require('../services/cacheService');
      const key = keyGenerator(req);
      
      // 尝试从缓存获取
      const cached = await cacheService.manager.get(key);
      if (cached) {
        return res.json(cached);
      }
      
      // 执行原函数
      const originalJson = res.json.bind(res);
      res.json = async function(data) {
        // 缓存成功的响应
        if (data && data.success) {
          await cacheService.manager.set(key, data, ttl);
        }
        return originalJson(data);
      };
      
      return fn(req, res, next);
    });
  };
};

// 分页助手
const paginate = (query) => {
  const page = parseInt(query.page) || 1;
  const limit = Math.min(parseInt(query.limit) || 20, 100);
  const offset = (page - 1) * limit;
  
  return {
    page,
    limit,
    offset,
    getPaginationData: (count) => ({
      current_page: page,
      total_pages: Math.ceil(count / limit),
      total_items: count,
      items_per_page: limit,
      has_previous: page > 1,
      has_next: page < Math.ceil(count / limit)
    })
  };
};

// 排序助手
const parseSort = (sortParam, allowedFields, defaultSort = { field: 'created_at', order: 'DESC' }) => {
  if (!sortParam) {
    return [[defaultSort.field, defaultSort.order]];
  }
  
  const sorts = sortParam.split(',').map(sort => {
    const [field, order = 'ASC'] = sort.split(':');
    
    if (!allowedFields.includes(field)) {
      throw new ValidationError(`不允许的排序字段: ${field}`);
    }
    
    const validOrder = order.toUpperCase();
    if (!['ASC', 'DESC'].includes(validOrder)) {
      throw new ValidationError(`无效的排序方向: ${order}`);
    }
    
    return [field, validOrder];
  });
  
  return sorts;
};

// 过滤助手
const buildWhereClause = (filters, allowedFilters) => {
  const where = {};
  const { Op } = require('sequelize');
  
  for (const [key, config] of Object.entries(allowedFilters)) {
    const value = filters[key];
    if (value === undefined || value === '') continue;
    
    switch (config.type) {
      case 'exact':
        where[config.field || key] = value;
        break;
        
      case 'like':
        where[config.field || key] = { [Op.iLike]: `%${value}%` };
        break;
        
      case 'range':
        const [min, max] = value.split(',');
        if (min) where[config.field || key] = { ...where[config.field || key], [Op.gte]: min };
        if (max) where[config.field || key] = { ...where[config.field || key], [Op.lte]: max };
        break;
        
      case 'in':
        where[config.field || key] = { [Op.in]: value.split(',') };
        break;
        
      case 'date':
        const date = new Date(value);
        where[config.field || key] = {
          [Op.gte]: date,
          [Op.lt]: new Date(date.getTime() + 24 * 60 * 60 * 1000)
        };
        break;
        
      case 'boolean':
        where[config.field || key] = value === 'true';
        break;
    }
  }
  
  return where;
};

// 批量操作助手
const batchOperation = async (items, operation, options = {}) => {
  const { chunkSize = 100, onProgress } = options;
  const results = [];
  const errors = [];
  
  for (let i = 0; i < items.length; i += chunkSize) {
    const chunk = items.slice(i, i + chunkSize);
    
    try {
      const chunkResults = await Promise.all(
        chunk.map(async (item, index) => {
          try {
            return await operation(item, i + index);
          } catch (error) {
            errors.push({ item, error: error.message });
            return null;
          }
        })
      );
      
      results.push(...chunkResults.filter(Boolean));
      
      if (onProgress) {
        onProgress(i + chunk.length, items.length);
      }
    } catch (error) {
      throw new AppError(`批量操作失败: ${error.message}`);
    }
  }
  
  return { results, errors };
};

// API响应助手
const sendSuccess = (res, data, message = '操作成功', statusCode = 200) => {
  res.status(statusCode).json({
    success: true,
    message,
    data
  });
};

const sendError = (res, error) => {
  const statusCode = error.statusCode || 500;
  res.status(statusCode).json(formatErrorResponse(error));
};

// 延迟重试
const retryOperation = async (operation, options = {}) => {
  const { maxRetries = 3, delay = 1000, backoff = 2 } = options;
  
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await operation();
    } catch (error) {
      if (i === maxRetries - 1) throw error;
      
      const waitTime = delay * Math.pow(backoff, i);
      await new Promise(resolve => setTimeout(resolve, waitTime));
    }
  }
};

module.exports = {
  // 错误类
  AppError,
  ValidationError,
  AuthenticationError,
  AuthorizationError,
  NotFoundError,
  ConflictError,
  RateLimitError,
  ExternalServiceError,
  
  // 中间件
  asyncHandler,
  validateRequest,
  validateQuery,
  checkOwnership,
  withTransaction,
  withCache,
  
  // 工具函数
  formatErrorResponse,
  paginate,
  parseSort,
  buildWhereClause,
  batchOperation,
  sendSuccess,
  sendError,
  retryOperation
};