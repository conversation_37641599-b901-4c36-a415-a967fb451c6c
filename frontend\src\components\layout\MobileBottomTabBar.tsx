import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { useCart } from '../../stores/cartStore';
import '../../styles/responsive.css';

interface TabItem {
  path: string;
  label: string;
  icon: string;
  badge?: number;
}

const MobileBottomTabBar: React.FC = () => {
  const location = useLocation();
  const { items } = useCart();
  const cartItemCount = items.reduce((total, item) => total + item.quantity, 0);

  const tabs: TabItem[] = [
    { path: '/', label: '首页', icon: '🏠' },
    { path: '/books', label: '分类', icon: '📚' },
    { path: '/cart', label: '购物车', icon: '🛒', badge: cartItemCount },
    { path: '/orders', label: '订单', icon: '📋' },
    { path: '/profile', label: '我的', icon: '👤' }
  ];

  const isActive = (path: string) => {
    if (path === '/') {
      return location.pathname === '/';
    }
    return location.pathname.startsWith(path);
  };

  // 不在某些页面显示底部导航
  const hiddenPaths = ['/auth/login', '/auth/register', '/admin'];
  if (hiddenPaths.some(path => location.pathname.startsWith(path))) {
    return null;
  }

  return (
    <div 
      className="mobile-bottom-tab-bar d-block d-lg-none"
      style={{
        position: 'fixed',
        bottom: 0,
        left: 0,
        right: 0,
        backgroundColor: '#fff',
        borderTop: '1px solid #e0e0e0',
        paddingBottom: 'var(--safe-area-inset-bottom)',
        zIndex: 99,
        boxShadow: '0 -2px 10px rgba(0,0,0,0.05)'
      }}
    >
      <div style={{
        display: 'flex',
        justifyContent: 'space-around',
        alignItems: 'center',
        height: '56px'
      }}>
        {tabs.map((tab) => (
          <Link
            key={tab.path}
            to={tab.path}
            className="touchable"
            style={{
              flex: 1,
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              gap: '4px',
              textDecoration: 'none',
              color: isActive(tab.path) ? '#007bff' : '#6c757d',
              padding: '8px',
              position: 'relative',
              transition: 'color 0.2s'
            }}
          >
            <div style={{
              position: 'relative',
              fontSize: '22px',
              lineHeight: 1
            }}>
              {tab.icon}
              {tab.badge && tab.badge > 0 && (
                <span style={{
                  position: 'absolute',
                  top: '-8px',
                  right: '-8px',
                  backgroundColor: '#dc3545',
                  color: '#fff',
                  borderRadius: '10px',
                  padding: '2px 6px',
                  fontSize: '10px',
                  fontWeight: 'bold',
                  minWidth: '16px',
                  height: '16px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}>
                  {tab.badge > 99 ? '99+' : tab.badge}
                </span>
              )}
            </div>
            <span style={{
              fontSize: '11px',
              fontWeight: isActive(tab.path) ? '600' : '400'
            }}>
              {tab.label}
            </span>
          </Link>
        ))}
      </div>
    </div>
  );
};

export default MobileBottomTabBar;