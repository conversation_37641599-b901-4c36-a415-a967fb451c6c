const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const rateLimit = require('express-rate-limit');
const { createServer } = require('http');
const { Server } = require('socket.io');
require('dotenv').config();

// Redis缓存初始化
const { redisClient } = require('./config/redis');
const { warmCache, maintainCache, cacheStats } = require('./middleware/cache');
const cacheService = require('./services/cacheService');

const { sequelize } = require('./models');
const authRoutes = require('./routes/auth');
const authEnhancedRoutes = require('./routes/authEnhanced');
const userRoutes = require('./routes/users');
const bookRoutes = require('./routes/books');
const bookRoutesOptimized = require('./routes/booksOptimized');
const orderRoutes = require('./routes/orders');
const orderRoutesOptimized = require('./routes/ordersOptimized');
const categoryRoutes = require('./routes/categories');
const messageRoutes = require('./routes/messages');
const favoritesRoutes = require('./routes/favorites');
const reviewsRoutes = require('./routes/reviews');
const recommendationsRoutes = require('./routes/recommendations');
const notificationsRoutes = require('./routes/notifications');
const analyticsRoutes = require('./routes/analytics');
const adminRoutes = require('./routes/admin');
const uploadRoutes = require('./routes/upload');
const secureUploadRoutes = require('./routes/secureUpload');
const cacheRoutes = require('./routes/cache');
const logsRoutes = require('./routes/logs');

const errorHandler = require('./middleware/errorHandler');
const { 
  errorHandler: enhancedErrorHandler, 
  requestIdMiddleware, 
  requestLogger,
  setupErrorMonitoring 
} = require('./middleware/errorHandlerEnhanced');
const { authenticateToken } = require('./middleware/authEnhanced');
const { startCleanupJobs } = require('./middleware/authEnhanced');
const logger = require('./utils/logger');

const app = express();
const server = createServer(app);
const io = new Server(server, {
  cors: {
    origin: process.env.FRONTEND_URL || "http://localhost:3000",
    methods: ["GET", "POST"]
  }
});

// 安全中间件配置
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'"],
      fontSrc: ["'self'"],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'"],
      frameSrc: ["'none'"]
    }
  },
  crossOriginEmbedderPolicy: false
}));

// CORS配置
const corsOptions = {
  origin: function (origin, callback) {
    const allowedOrigins = [
      process.env.FRONTEND_URL || "http://localhost:3000",
      "http://localhost:3000",
      "http://127.0.0.1:3000"
    ];
    
    if (!origin || allowedOrigins.includes(origin)) {
      callback(null, true);
    } else {
      callback(new Error('Not allowed by CORS'));
    }
  },
  credentials: true,
  optionsSuccessStatus: 200,
  methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
};

app.use(cors(corsOptions));

// 添加请求ID
app.use(requestIdMiddleware);

// 使用自定义日志器替代morgan
app.use(requestLogger);

// 请求体解析中间件
app.use(express.json({ 
  limit: '10mb',
  verify: (req, res, buf, encoding) => {
    // 验证JSON格式
    try {
      JSON.parse(buf);
    } catch (err) {
      const error = new Error('Invalid JSON');
      error.status = 400;
      throw error;
    }
  }
}));

app.use(express.urlencoded({ 
  extended: true, 
  limit: '10mb',
  parameterLimit: 1000 // 限制参数数量
}));

// 静态文件服务配置
const path = require('path');
app.use('/uploads', express.static(path.join(__dirname, '../uploads'), {
  maxAge: '1d', // 缓存1天
  etag: true,
  lastModified: true,
  setHeaders: (res, filePath) => {
    // 设置安全头
    res.set({
      'X-Content-Type-Options': 'nosniff',
      'X-Frame-Options': 'DENY',
      'Cache-Control': 'public, max-age=86400'
    });
    
    // 根据文件类型设置Content-Type
    const ext = path.extname(filePath).toLowerCase();
    const mimeTypes = {
      '.jpg': 'image/jpeg',
      '.jpeg': 'image/jpeg',
      '.png': 'image/png',
      '.gif': 'image/gif', 
      '.webp': 'image/webp'
    };
    
    if (mimeTypes[ext]) {
      res.type(mimeTypes[ext]);
    }
  }
}));

// 速率限制
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 100, // 限制每个IP 15分钟内最多100个请求
  message: {
    error: '请求过于频繁，请稍后再试'
  }
});
app.use('/api/', limiter);

// 缓存统计中间件
app.use('/api/', cacheStats);

// 更严格的认证相关限制
const authLimiter = rateLimit({
  windowMs: 15 * 60 * 1000,
  max: 5, // 认证相关请求更严格
  message: {
    error: '登录尝试过于频繁，请稍后再试'
  }
});
app.use('/api/auth/login', authLimiter);
app.use('/api/auth/register', authLimiter);

// Socket.IO 连接处理
io.on('connection', (socket) => {
  console.log('用户连接:', socket.id);
  
  // 加入房间（用于私聊）
  socket.on('join_room', (roomId) => {
    socket.join(roomId);
    console.log(`用户 ${socket.id} 加入房间 ${roomId}`);
  });
  
  // 发送消息
  socket.on('send_message', (data) => {
    socket.to(data.roomId).emit('receive_message', data);
  });
  
  // 断开连接
  socket.on('disconnect', () => {
    console.log('用户断开连接:', socket.id);
  });
});

// 将 io 实例添加到 app 中，供路由使用
app.set('io', io);

// 健康检查路由
const healthRoutes = require('./routes/health');
app.use('/health', healthRoutes);
app.use('/api/health', healthRoutes);

// API 路由
app.use('/api/auth', authEnhancedRoutes); // 使用增强的认证路由
// app.use('/api/auth-legacy', authRoutes); // 保留旧路由用于向后兼容
app.use('/api/users', authenticateToken, userRoutes);
// 使用优化的路由
app.use('/api/books', bookRoutesOptimized);
app.use('/api/orders', authenticateToken, orderRoutesOptimized);
// 保留旧路由用于向后兼容
// app.use('/api/books-legacy', bookRoutes);
// app.use('/api/orders-legacy', authenticateToken, orderRoutes);
app.use('/api/categories', categoryRoutes);
app.use('/api/messages', authenticateToken, messageRoutes);
app.use('/api/favorites', authenticateToken, favoritesRoutes);
app.use('/api/reviews', reviewsRoutes);
app.use('/api/recommendations', recommendationsRoutes);
app.use('/api/notifications', authenticateToken, notificationsRoutes);
app.use('/api/analytics', authenticateToken, analyticsRoutes);
app.use('/api/admin', authenticateToken, adminRoutes);
// 使用新的安全上传路由
app.use('/api/upload', authenticateToken, secureUploadRoutes);
// 保留旧路由用于向后兼容（可选）
// app.use('/api/upload-legacy', authenticateToken, uploadRoutes);
app.use('/api/cache', cacheRoutes);
app.use('/api/logs', logsRoutes);

// 404 处理
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: '请求的资源不存在'
  });
});

// 错误处理中间件（使用增强版）
app.use(enhancedErrorHandler);

// 设置错误监控（如果配置了）
const sentry = setupErrorMonitoring();
if (sentry) {
  app.use(sentry.Handlers.requestHandler());
  app.use(sentry.Handlers.errorHandler());
}

const PORT = process.env.PORT || 3001;

// 数据库连接和服务器启动
async function startServer() {
  try {
    // 测试数据库连接
    await sequelize.authenticate();
    console.log('数据库连接成功');
    
    // 同步数据库模型（开发环境）
    if (process.env.NODE_ENV === 'development') {
      await sequelize.sync({ alter: true });
      console.log('数据库模型同步完成');
    }
    
    // 启动清理任务
    startCleanupJobs();
    console.log('后台清理任务已启动');
    
    // 初始化Redis缓存
    await cacheService.warmupCache();
    maintainCache();
    console.log('Redis缓存已初始化');
    
    // 启动服务器
    server.listen(PORT, () => {
      console.log(`服务器运行在端口 ${PORT}`);
      console.log(`环境: ${process.env.NODE_ENV}`);
      console.log(`Redis已连接: ${redisClient.connected ? '是' : '否'}`);
    });
  } catch (error) {
    console.error('服务器启动失败:', error);
    process.exit(1);
  }
}

// 优雅关闭
process.on('SIGTERM', async () => {
  console.log('收到 SIGTERM 信号，正在关闭服务器...');
  await sequelize.close();
  process.exit(0);
});

process.on('SIGINT', async () => {
  console.log('收到 SIGINT 信号，正在关闭服务器...');
  await sequelize.close();
  process.exit(0);
});

startServer();

module.exports = app;
