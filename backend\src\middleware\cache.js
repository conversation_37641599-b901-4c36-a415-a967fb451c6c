const { cacheManager, CACHE_TTL, CACHE_PREFIX } = require('../config/redis');
const crypto = require('crypto');

// 生成请求缓存键
const generateCacheKey = (req) => {
  const { originalUrl, method, user } = req;
  const userId = user ? user.id : 'anonymous';
  const hash = crypto.createHash('md5').update(originalUrl).digest('hex');
  return `${CACHE_PREFIX.SEARCH}${method}:${userId}:${hash}`;
};

// 缓存中间件工厂
const cacheMiddleware = (options = {}) => {
  const {
    ttl = CACHE_TTL.MEDIUM,
    keyGenerator = generateCacheKey,
    condition = () => true,
    invalidatePattern = null
  } = options;

  return async (req, res, next) => {
    // 检查是否应该缓存
    if (!condition(req)) {
      return next();
    }

    // 只缓存GET请求
    if (req.method !== 'GET') {
      return next();
    }

    const cacheKey = keyGenerator(req);

    try {
      // 尝试从缓存获取
      const cachedData = await cacheManager.get(cacheKey);
      
      if (cachedData) {
        // 设置缓存命中头
        res.setHeader('X-Cache', 'HIT');
        res.setHeader('X-Cache-Key', cacheKey);
        
        // 记录热点数据
        await cacheManager.recordAccess('api', req.path);
        
        return res.json(cachedData);
      }

      // 缓存未命中，继续处理请求
      res.setHeader('X-Cache', 'MISS');

      // 重写res.json以捕获响应数据
      const originalJson = res.json.bind(res);
      res.json = async function(data) {
        // 只缓存成功的响应
        if (res.statusCode === 200 && data && data.success) {
          await cacheManager.set(cacheKey, data, ttl);
          
          // 如果有失效模式，设置相关失效
          if (invalidatePattern) {
            // 这将在数据更新时使用
          }
        }
        
        return originalJson(data);
      };

      next();
    } catch (error) {
      console.error('缓存中间件错误:', error);
      // 缓存错误不应影响正常请求
      next();
    }
  };
};

// 缓存失效中间件
const invalidateCache = (patterns) => {
  return async (req, res, next) => {
    const originalJson = res.json.bind(res);
    
    res.json = async function(data) {
      // 只在成功响应时失效缓存
      if (res.statusCode < 300 && data && data.success) {
        try {
          for (const pattern of patterns) {
            await cacheManager.delPattern(pattern);
          }
        } catch (error) {
          console.error('缓存失效错误:', error);
        }
      }
      
      return originalJson(data);
    };
    
    next();
  };
};

// 预定义的缓存策略
const cacheStrategies = {
  // 短期缓存（1分钟）
  short: cacheMiddleware({ ttl: CACHE_TTL.SHORT }),
  
  // 中期缓存（5分钟）
  medium: cacheMiddleware({ ttl: CACHE_TTL.MEDIUM }),
  
  // 长期缓存（1小时）
  long: cacheMiddleware({ ttl: CACHE_TTL.LONG }),
  
  // 图书列表缓存
  bookList: cacheMiddleware({
    ttl: CACHE_TTL.MEDIUM,
    keyGenerator: (req) => {
      const { page = 1, limit = 20, search, category_id, sort_by, sort_order } = req.query;
      const key = `${CACHE_PREFIX.BOOK}list:${page}:${limit}:${search || ''}:${category_id || ''}:${sort_by || ''}:${sort_order || ''}`;
      return key;
    }
  }),
  
  // 图书详情缓存
  bookDetail: cacheMiddleware({
    ttl: CACHE_TTL.LONG,
    keyGenerator: (req) => `${CACHE_PREFIX.BOOK}detail:${req.params.id}`
  }),
  
  // 分类缓存
  category: cacheMiddleware({
    ttl: CACHE_TTL.DAY,
    keyGenerator: (req) => `${CACHE_PREFIX.CATEGORY}all`
  }),
  
  // 用户数据缓存（需要用户认证）
  userData: cacheMiddleware({
    ttl: CACHE_TTL.SHORT,
    condition: (req) => req.user && req.user.id,
    keyGenerator: (req) => `${CACHE_PREFIX.USER}${req.user.id}:${req.path}`
  }),
  
  // 热门数据缓存
  trending: cacheMiddleware({
    ttl: CACHE_TTL.LONG,
    keyGenerator: (req) => `${CACHE_PREFIX.TRENDING}${req.path}`
  })
};

// 缓存预热
const warmCache = async () => {
  try {
    console.log('开始缓存预热...');
    
    // 预热分类数据
    const { Category } = require('../models');
    const categories = await Category.findAll({
      where: { is_active: true },
      order: [['sort_order', 'ASC']]
    });
    
    await cacheManager.set(
      `${CACHE_PREFIX.CATEGORY}all`,
      { success: true, data: categories },
      CACHE_TTL.DAY
    );
    
    console.log('缓存预热完成');
  } catch (error) {
    console.error('缓存预热错误:', error);
  }
};

// 定期缓存维护
const maintainCache = () => {
  // 每小时清理过期数据
  setInterval(async () => {
    try {
      const keys = await cacheManager.redis.keys(`${CACHE_PREFIX.SEARCH}*`);
      for (const key of keys) {
        const ttl = await cacheManager.ttl(key);
        if (ttl === -1) {
          // 没有过期时间的键，设置默认过期
          await cacheManager.expire(key, CACHE_TTL.DAY);
        }
      }
    } catch (error) {
      console.error('缓存维护错误:', error);
    }
  }, 60 * 60 * 1000); // 1小时
};

// 缓存统计中间件
const cacheStats = async (req, res, next) => {
  const start = Date.now();
  
  res.on('finish', async () => {
    const duration = Date.now() - start;
    const cacheStatus = res.getHeader('X-Cache') || 'NONE';
    
    // 记录缓存统计
    const statsKey = `${CACHE_PREFIX.STATS}cache:${new Date().toISOString().split('T')[0]}`;
    await cacheManager.redis.hincrby(statsKey, `${cacheStatus}:count`, 1);
    await cacheManager.redis.hincrby(statsKey, `${cacheStatus}:time`, duration);
    await cacheManager.expire(statsKey, CACHE_TTL.WEEK);
  });
  
  next();
};

// 获取缓存统计信息
const getCacheStats = async (date) => {
  const statsKey = `${CACHE_PREFIX.STATS}cache:${date || new Date().toISOString().split('T')[0]}`;
  const stats = await cacheManager.redis.hgetall(statsKey);
  
  if (!stats) {
    return null;
  }
  
  const hitCount = parseInt(stats['HIT:count'] || 0);
  const missCount = parseInt(stats['MISS:count'] || 0);
  const totalCount = hitCount + missCount;
  const hitRate = totalCount > 0 ? (hitCount / totalCount * 100).toFixed(2) : 0;
  
  const hitTime = parseInt(stats['HIT:time'] || 0);
  const missTime = parseInt(stats['MISS:time'] || 0);
  
  return {
    date,
    hit: {
      count: hitCount,
      avgTime: hitCount > 0 ? (hitTime / hitCount).toFixed(2) : 0
    },
    miss: {
      count: missCount,
      avgTime: missCount > 0 ? (missTime / missCount).toFixed(2) : 0
    },
    total: totalCount,
    hitRate: `${hitRate}%`
  };
};

module.exports = {
  cacheMiddleware,
  invalidateCache,
  cacheStrategies,
  warmCache,
  maintainCache,
  cacheStats,
  getCacheStats
};