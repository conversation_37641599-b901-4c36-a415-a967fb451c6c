#!/bin/bash

# 收书卖书平台 - 增强版部署脚本
# 作者: DevOps Team
# 版本: 2.0
# 支持蓝绿部署、滚动更新、健康检查、自动回滚

set -euo pipefail

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_NAME="booktrading"
COMPOSE_FILE="docker-compose.secure.yml"
BACKUP_COMPOSE_FILE="docker-compose.backup.yml"
MONITORING_COMPOSE_FILE="docker-compose.monitoring.yml"

# 颜色定义
readonly RED='\033[0;31m'
readonly GREEN='\033[0;32m'
readonly YELLOW='\033[1;33m'
readonly BLUE='\033[0;34m'
readonly PURPLE='\033[0;35m'
readonly CYAN='\033[0;36m'
readonly NC='\033[0m' # No Color

# 日志级别
LOG_LEVEL="${LOG_LEVEL:-INFO}"

# 日志函数
log() {
    local level="$1"
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    case "$level" in
        "DEBUG")
            [[ "$LOG_LEVEL" == "DEBUG" ]] && echo -e "${CYAN}[DEBUG]${NC} [$timestamp] $message" >&2
            ;;
        "INFO")
            echo -e "${BLUE}[INFO]${NC} [$timestamp] $message"
            ;;
        "SUCCESS")
            echo -e "${GREEN}[SUCCESS]${NC} [$timestamp] $message"
            ;;
        "WARNING")
            echo -e "${YELLOW}[WARNING]${NC} [$timestamp] $message"
            ;;
        "ERROR")
            echo -e "${RED}[ERROR]${NC} [$timestamp] $message" >&2
            ;;
        "CRITICAL")
            echo -e "${PURPLE}[CRITICAL]${NC} [$timestamp] $message" >&2
            ;;
    esac
    
    # 写入日志文件
    echo "[$level] [$timestamp] $message" >> "${SCRIPT_DIR}/deploy.log"
}

# 错误处理
error_exit() {
    log "CRITICAL" "$1"
    cleanup_on_error
    exit 1
}

# 错误时清理
cleanup_on_error() {
    log "WARNING" "部署失败，开始清理..."
    
    # 如果有备份，尝试回滚
    if [[ -f ".deployment-backup" ]]; then
        log "INFO" "尝试自动回滚..."
        rollback_deployment
    fi
}

# 检查系统要求
check_system_requirements() {
    log "INFO" "检查系统要求..."
    
    # 检查操作系统
    if [[ ! "$OSTYPE" =~ ^(linux|darwin) ]]; then
        error_exit "不支持的操作系统: $OSTYPE"
    fi
    
    # 检查内存
    local available_memory
    if command -v free >/dev/null; then
        available_memory=$(free -m | awk 'NR==2{printf "%.0f", $7}')
    elif command -v vm_stat >/dev/null; then
        available_memory=$(vm_stat | awk '/Pages free/ {print $3}' | sed 's/\.//' | awk '{print $1 * 4096 / 1024 / 1024}')
    else
        log "WARNING" "无法检测可用内存"
        return
    fi
    
    if [[ -n "$available_memory" ]] && [[ "$available_memory" -lt 2048 ]]; then
        log "WARNING" "可用内存不足 (${available_memory}MB < 2048MB)，可能影响性能"
    fi
    
    # 检查磁盘空间
    local available_space=$(df "$SCRIPT_DIR" | awk 'NR==2 {print $4}')
    if [[ "$available_space" -lt 5242880 ]]; then  # 5GB in KB
        log "WARNING" "磁盘空间不足 ($(($available_space/1024/1024))GB < 5GB)"
    fi
    
    log "SUCCESS" "系统要求检查完成"
}

# 检查依赖
check_dependencies() {
    log "INFO" "检查依赖工具..."
    
    local required_tools=("docker" "docker-compose" "curl" "jq")
    local missing_tools=()
    
    for tool in "${required_tools[@]}"; do
        if ! command -v "$tool" &> /dev/null; then
            missing_tools+=("$tool")
        fi
    done
    
    if [[ ${#missing_tools[@]} -gt 0 ]]; then
        error_exit "缺少必要工具: ${missing_tools[*]}"
    fi
    
    # 检查Docker版本
    local docker_version=$(docker --version | grep -oE "[0-9]+\.[0-9]+")
    if [[ $(echo "$docker_version < 20.10" | bc -l 2>/dev/null || echo "1") -eq 1 ]]; then
        log "WARNING" "Docker版本较旧 ($docker_version)，建议升级到20.10+"
    fi
    
    # 检查Docker Compose版本
    local compose_version=$(docker-compose --version | grep -oE "[0-9]+\.[0-9]+")
    if [[ $(echo "$compose_version < 2.0" | bc -l 2>/dev/null || echo "1") -eq 1 ]]; then
        log "WARNING" "Docker Compose版本较旧 ($compose_version)，建议升级到2.0+"
    fi
    
    log "SUCCESS" "依赖检查完成"
}

# 环境准备
prepare_environment() {
    log "INFO" "准备部署环境..."
    
    # 创建必要目录
    local dirs=(
        "data/postgres"
        "data/redis" 
        "data/uploads"
        "logs"
        "backups"
        "nginx/ssl"
        "scripts"
        "monitoring"
    )
    
    for dir in "${dirs[@]}"; do
        mkdir -p "$dir"
        log "DEBUG" "创建目录: $dir"
    done
    
    # 设置权限
    chmod +x scripts/*.sh 2>/dev/null || true
    
    # 检查环境变量文件
    if [[ ! -f ".env" ]]; then
        if [[ -f ".env.example" ]]; then
            log "INFO" "从示例文件创建.env..."
            cp ".env.example" ".env"
            log "WARNING" "请编辑.env文件设置正确的环境变量"
        else
            error_exit "缺少.env文件，请创建环境变量配置"
        fi
    fi
    
    # 验证关键环境变量
    source .env
    local required_vars=("DB_PASSWORD" "REDIS_PASSWORD" "JWT_SECRET")
    local missing_vars=()
    
    for var in "${required_vars[@]}"; do
        if [[ -z "${!var:-}" ]]; then
            missing_vars+=("$var")
        fi
    done
    
    if [[ ${#missing_vars[@]} -gt 0 ]]; then
        error_exit "缺少必要的环境变量: ${missing_vars[*]}"
    fi
    
    log "SUCCESS" "环境准备完成"
}

# 安全检查
security_check() {
    log "INFO" "执行安全检查..."
    
    source .env
    
    # 检查默认密码
    local default_passwords=(
        "password:123456"
        "admin:admin"
        "booktrading:booktrading123"
        "your_secure_password:${DB_PASSWORD}"
        "your_redis_password:${REDIS_PASSWORD}"
    )
    
    for check in "${default_passwords[@]}"; do
        IFS=':' read -r pattern value <<< "$check"
        if [[ "$value" == *"$pattern"* ]]; then
            log "WARNING" "检测到可能的默认密码: $pattern"
        fi
    done
    
    # 检查文件权限
    if [[ -f ".env" ]]; then
        local env_perms=$(stat -c "%a" .env 2>/dev/null || stat -f "%A" .env 2>/dev/null || echo "unknown")
        if [[ "$env_perms" != "600" ]] && [[ "$env_perms" != "unknown" ]]; then
            log "WARNING" ".env文件权限不安全 ($env_perms)，建议设置为600"
            chmod 600 .env
        fi
    fi
    
    log "SUCCESS" "安全检查完成"
}

# 构建镜像
build_images() {
    log "INFO" "构建Docker镜像..."
    
    # 设置构建参数
    local build_args=(
        "--no-cache"
        "--pull"
        "--parallel"
    )
    
    # 使用BuildKit增强构建性能
    export DOCKER_BUILDKIT=1
    export COMPOSE_DOCKER_CLI_BUILD=1
    
    # 构建镜像
    if docker-compose -f "$COMPOSE_FILE" build "${build_args[@]}"; then
        log "SUCCESS" "镜像构建完成"
    else
        error_exit "镜像构建失败"
    fi
    
    # 清理构建缓存
    docker builder prune -f --filter until=24h || log "WARNING" "清理构建缓存失败"
}

# 预部署检查
pre_deployment_check() {
    log "INFO" "执行预部署检查..."
    
    # 检查端口占用
    local ports=(80 443)
    for port in "${ports[@]}"; do
        if lsof -i ":$port" >/dev/null 2>&1; then
            log "WARNING" "端口 $port 已被占用，可能影响部署"
        fi
    done
    
    # 检查镜像是否存在
    local images=$(docker-compose -f "$COMPOSE_FILE" config --services)
    for service in $images; do
        local image=$(docker-compose -f "$COMPOSE_FILE" config | yq e ".services.$service.image" -)
        if [[ "$image" != "null" ]] && ! docker image inspect "$image" >/dev/null 2>&1; then
            log "WARNING" "镜像不存在: $image"
        fi
    done
    
    log "SUCCESS" "预部署检查完成"
}

# 创建部署备份
create_deployment_backup() {
    log "INFO" "创建部署备份..."
    
    local backup_timestamp=$(date +"%Y%m%d_%H%M%S")
    local backup_info="backup_${backup_timestamp}"
    
    # 备份当前运行的容器信息
    {
        echo "TIMESTAMP=$backup_timestamp"
        echo "COMPOSE_FILE=$COMPOSE_FILE"
        docker-compose -f "$COMPOSE_FILE" ps --format json || echo "[]"
    } > ".deployment-backup"
    
    # 备份数据库（如果运行中）
    if docker-compose -f "$COMPOSE_FILE" ps postgres | grep -q "Up"; then
        log "INFO" "备份数据库..."
        docker-compose -f "$COMPOSE_FILE" exec -T postgres pg_dump -U "$DB_USER" -d "$DB_NAME" > "backups/pre_deploy_${backup_timestamp}.sql" || {
            log "WARNING" "数据库备份失败"
        }
    fi
    
    log "SUCCESS" "部署备份创建完成"
}

# 健康检查
health_check() {
    local service="$1"
    local max_attempts="${2:-30}"
    local interval="${3:-10}"
    
    log "INFO" "检查服务健康状态: $service"
    
    for ((i=1; i<=max_attempts; i++)); do
        local health_status=$(docker-compose -f "$COMPOSE_FILE" ps "$service" --format json | jq -r '.[0].Health // "unknown"')
        
        case "$health_status" in
            "healthy")
                log "SUCCESS" "服务 $service 健康检查通过 (${i}/${max_attempts})"
                return 0
                ;;
            "unhealthy")
                log "ERROR" "服务 $service 健康检查失败 (${i}/${max_attempts})"
                ;;
            "starting")
                log "INFO" "服务 $service 正在启动... (${i}/${max_attempts})"
                ;;
            *)
                log "DEBUG" "服务 $service 健康状态未知: $health_status (${i}/${max_attempts})"
                ;;
        esac
        
        if [[ $i -lt $max_attempts ]]; then
            sleep "$interval"
        fi
    done
    
    log "ERROR" "服务 $service 健康检查超时"
    return 1
}

# 滚动更新部署
rolling_update() {
    log "INFO" "开始滚动更新部署..."
    
    local services=("postgres" "redis" "backend" "frontend" "nginx")
    
    for service in "${services[@]}"; do
        log "INFO" "更新服务: $service"
        
        # 更新服务
        if docker-compose -f "$COMPOSE_FILE" up -d --no-deps "$service"; then
            log "SUCCESS" "服务 $service 更新完成"
            
            # 等待服务健康
            if ! health_check "$service" 20 5; then
                error_exit "服务 $service 健康检查失败，停止部署"
            fi
        else
            error_exit "服务 $service 更新失败"
        fi
        
        # 短暂等待
        sleep 5
    done
    
    log "SUCCESS" "滚动更新完成"
}

# 蓝绿部署
blue_green_deploy() {
    log "INFO" "开始蓝绿部署..."
    
    local blue_env="blue"
    local green_env="green"
    local current_env=$(docker-compose -f "$COMPOSE_FILE" ps --format json | jq -r '.[0].Labels["com.docker.compose.project"] // "blue"')
    
    # 确定目标环境
    local target_env
    if [[ "$current_env" == "$blue_env" ]]; then
        target_env="$green_env"
    else
        target_env="$blue_env"
    fi
    
    log "INFO" "当前环境: $current_env, 目标环境: $target_env"
    
    # 部署到目标环境
    COMPOSE_PROJECT_NAME="$target_env" docker-compose -f "$COMPOSE_FILE" up -d
    
    # 健康检查
    local services=("backend" "frontend")
    for service in "${services[@]}"; do
        if ! health_check "$service" 30 10; then
            error_exit "目标环境健康检查失败"
        fi
    done
    
    # 切换流量（更新负载均衡器配置）
    log "INFO" "切换流量到目标环境..."
    # 这里应该更新负载均衡器配置
    
    # 停止旧环境
    log "INFO" "停止旧环境..."
    COMPOSE_PROJECT_NAME="$current_env" docker-compose -f "$COMPOSE_FILE" down
    
    log "SUCCESS" "蓝绿部署完成"
}

# 部署后验证
post_deployment_verification() {
    log "INFO" "执行部署后验证..."
    
    # API健康检查
    local api_url="http://localhost:3001/api/health"
    local max_attempts=10
    
    for ((i=1; i<=max_attempts; i++)); do
        if curl -f -s "$api_url" >/dev/null; then
            log "SUCCESS" "API健康检查通过"
            break
        elif [[ $i -eq $max_attempts ]]; then
            log "ERROR" "API健康检查失败"
            return 1
        else
            log "INFO" "等待API启动... (${i}/${max_attempts})"
            sleep 10
        fi
    done
    
    # 前端访问检查
    local frontend_url="http://localhost:3000"
    if curl -f -s "$frontend_url" >/dev/null; then
        log "SUCCESS" "前端访问检查通过"
    else
        log "ERROR" "前端访问检查失败"
        return 1
    fi
    
    # 数据库连接检查
    if docker-compose -f "$COMPOSE_FILE" exec -T postgres pg_isready -U "$DB_USER" -d "$DB_NAME" >/dev/null; then
        log "SUCCESS" "数据库连接检查通过"
    else
        log "ERROR" "数据库连接检查失败"
        return 1
    fi
    
    # 性能基准测试
    log "INFO" "执行基本性能测试..."
    local response_time=$(curl -o /dev/null -s -w "%{time_total}" "$api_url")
    if (( $(echo "$response_time < 2.0" | bc -l) )); then
        log "SUCCESS" "API响应时间: ${response_time}s"
    else
        log "WARNING" "API响应时间较慢: ${response_time}s"
    fi
    
    log "SUCCESS" "部署后验证完成"
}

# 回滚部署
rollback_deployment() {
    log "WARNING" "开始回滚部署..."
    
    if [[ ! -f ".deployment-backup" ]]; then
        error_exit "未找到部署备份信息，无法回滚"
    fi
    
    source .deployment-backup
    
    # 停止当前服务
    docker-compose -f "$COMPOSE_FILE" down
    
    # 恢复数据库备份
    if [[ -f "backups/pre_deploy_${TIMESTAMP}.sql" ]]; then
        log "INFO" "恢复数据库备份..."
        docker-compose -f "$COMPOSE_FILE" up -d postgres
        sleep 10  # 等待数据库启动
        docker-compose -f "$COMPOSE_FILE" exec -T postgres psql -U "$DB_USER" -d "$DB_NAME" < "backups/pre_deploy_${TIMESTAMP}.sql"
    fi
    
    # 启动备份时的服务配置
    # 这里需要根据备份信息恢复服务状态
    
    log "SUCCESS" "回滚完成"
}

# 监控和告警
setup_monitoring() {
    log "INFO" "设置监控和告警..."
    
    if [[ -f "$MONITORING_COMPOSE_FILE" ]]; then
        docker-compose -f "$MONITORING_COMPOSE_FILE" up -d
        log "SUCCESS" "监控服务已启动"
    else
        log "WARNING" "监控配置文件不存在，跳过监控设置"
    fi
}

# 发送部署通知
send_deployment_notification() {
    local status="$1"
    local details="$2"
    
    if [[ -n "${WEBHOOK_URL:-}" ]]; then
        local message="部署状态: $status\\n详情: $details\\n时间: $(date)\\n项目: $PROJECT_NAME"
        
        curl -X POST "$WEBHOOK_URL" \
            -H "Content-Type: application/json" \
            -d "{\"text\":\"$message\"}" \
            --max-time 10 --silent || log "WARNING" "发送通知失败"
    fi
}

# 显示部署信息
show_deployment_info() {
    log "SUCCESS" "部署完成！"
    echo
    echo "=================================="
    echo "访问信息:"
    echo "  前端应用: http://localhost:3000"
    echo "  后端API:  http://localhost:3001"
    echo "  健康检查: http://localhost:3001/api/health"
    echo
    echo "管理命令:"
    echo "  查看状态: docker-compose -f $COMPOSE_FILE ps"
    echo "  查看日志: docker-compose -f $COMPOSE_FILE logs -f [service]"
    echo "  停止服务: docker-compose -f $COMPOSE_FILE down"
    echo "  重启服务: docker-compose -f $COMPOSE_FILE restart [service]"
    echo
    echo "监控信息:"
    echo "  Grafana: http://localhost:3003 (admin/admin)"
    echo "  Prometheus: http://localhost:9090"
    echo
    echo "备份管理:"
    echo "  创建备份: ./scripts/backup.sh"
    echo "  恢复数据: ./scripts/restore.sh -l"
    echo "=================================="
}

# 主部署函数
deploy() {
    local deployment_type="${1:-rolling}"
    
    log "INFO" "开始部署 - 类型: $deployment_type"
    
    # 系统检查
    check_system_requirements
    check_dependencies
    
    # 环境准备
    prepare_environment
    security_check
    
    # 构建和检查
    build_images
    pre_deployment_check
    
    # 创建备份
    create_deployment_backup
    
    # 执行部署
    case "$deployment_type" in
        "rolling")
            rolling_update
            ;;
        "blue-green")
            blue_green_deploy
            ;;
        "recreate")
            docker-compose -f "$COMPOSE_FILE" up -d --force-recreate
            ;;
        *)
            error_exit "不支持的部署类型: $deployment_type"
            ;;
    esac
    
    # 部署后操作
    post_deployment_verification
    setup_monitoring
    
    # 清理备份文件
    rm -f .deployment-backup
    
    log "SUCCESS" "部署成功完成"
    send_deployment_notification "SUCCESS" "部署类型: $deployment_type"
    show_deployment_info
}

# 显示帮助信息
show_help() {
    cat << EOF
收书卖书平台 - 增强版部署脚本

用法: $0 [命令] [选项]

命令:
  deploy [type]              部署应用 (type: rolling|blue-green|recreate)
  rollback                   回滚到上一个版本
  status                     查看服务状态
  logs [service]             查看日志
  backup                     创建备份
  restore [backup_id]        恢复备份
  cleanup                    清理资源
  health                     健康检查
  help                       显示帮助信息

部署类型:
  rolling                    滚动更新（默认）
  blue-green                 蓝绿部署
  recreate                   重新创建所有服务

选项:
  --env-file FILE           指定环境变量文件 (默认: .env)
  --compose-file FILE       指定docker-compose文件
  --skip-backup            跳过部署前备份
  --skip-health-check      跳过健康检查
  --verbose                设置详细日志模式
  --dry-run                试运行模式

示例:
  $0 deploy rolling         # 滚动更新部署
  $0 deploy blue-green      # 蓝绿部署
  $0 rollback               # 回滚部署
  $0 backup                 # 创建备份
  $0 status                 # 查看状态

环境变量:
  LOG_LEVEL=DEBUG           设置日志级别
  WEBHOOK_URL=<url>         设置通知webhook地址

EOF
}

# 主函数
main() {
    # 参数解析
    local command="${1:-help}"
    local deployment_type="${2:-rolling}"
    
    # 设置错误处理
    trap 'error_exit "脚本执行被中断"' INT TERM
    trap 'cleanup_on_error' ERR
    
    # 创建日志文件
    touch "${SCRIPT_DIR}/deploy.log"
    
    case "$command" in
        "deploy")
            deploy "$deployment_type"
            ;;
        "rollback")
            rollback_deployment
            ;;
        "status")
            docker-compose -f "$COMPOSE_FILE" ps
            ;;
        "logs")
            docker-compose -f "$COMPOSE_FILE" logs -f "${2:-}"
            ;;
        "backup")
            ./scripts/backup.sh
            ;;
        "restore")
            ./scripts/restore.sh "${2:-}"
            ;;
        "cleanup")
            docker system prune -f
            docker volume prune -f
            ;;
        "health")
            post_deployment_verification
            ;;
        "help"|"--help"|"-h")
            show_help
            ;;
        *)
            log "ERROR" "未知命令: $command"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"