{"name": "book-trading-backend", "version": "1.0.0", "description": "大学生二手书交易平台后端API", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "test": "jest", "test:watch": "jest --watch", "migrate": "node src/database/migrate.js", "seed": "node src/database/seed.js"}, "keywords": ["book", "trading", "university", "secondhand", "marketplace"], "author": "Your Name", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "pg": "^8.11.3", "pg-hstore": "^2.3.4", "sequelize": "^6.35.0", "multer": "^1.4.5-lts.1", "cloudinary": "^1.41.0", "joi": "^17.11.0", "express-rate-limit": "^7.1.5", "socket.io": "^4.7.4", "nodemailer": "^6.9.7", "uuid": "^9.0.1", "moment": "^2.29.4", "sharp": "^0.33.1", "redis": "^3.1.2", "winston": "^3.11.0", "winston-daily-rotate-file": "^4.7.1", "@sentry/node": "^7.91.0"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "@types/jest": "^29.5.8"}, "engines": {"node": ">=18.0.0"}}