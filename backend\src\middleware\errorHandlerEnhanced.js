const logger = require('../utils/logger');

// 自定义错误类
class AppError extends Error {
  constructor(message, statusCode = 500, code = 'INTERNAL_ERROR', details = {}) {
    super(message);
    this.statusCode = statusCode;
    this.code = code;
    this.details = details;
    this.isOperational = true;
    
    Error.captureStackTrace(this, this.constructor);
  }
}

// 业务错误类
class ValidationError extends AppError {
  constructor(message, details = {}) {
    super(message, 400, 'VALIDATION_ERROR', details);
  }
}

class AuthenticationError extends AppError {
  constructor(message = '认证失败') {
    super(message, 401, 'AUTHENTICATION_ERROR');
  }
}

class AuthorizationError extends AppError {
  constructor(message = '权限不足') {
    super(message, 403, 'AUTHORIZATION_ERROR');
  }
}

class NotFoundError extends AppError {
  constructor(resource = '资源') {
    super(`${resource}不存在`, 404, 'NOT_FOUND');
  }
}

class ConflictError extends AppError {
  constructor(message = '资源冲突') {
    super(message, 409, 'CONFLICT');
  }
}

class RateLimitError extends AppError {
  constructor(message = '请求过于频繁') {
    super(message, 429, 'RATE_LIMIT_EXCEEDED');
  }
}

class ExternalServiceError extends AppError {
  constructor(service, message) {
    super(`外部服务错误: ${service} - ${message}`, 502, 'EXTERNAL_SERVICE_ERROR', { service });
  }
}

// 错误处理工具函数
const isDevelopment = process.env.NODE_ENV === 'development';
const isProduction = process.env.NODE_ENV === 'production';

// 处理Sequelize验证错误
const handleSequelizeValidationError = (err) => {
  const errors = err.errors.map(e => ({
    field: e.path,
    message: e.message,
    value: e.value
  }));
  
  return new ValidationError('数据验证失败', { errors });
};

// 处理Sequelize唯一约束错误
const handleSequelizeUniqueConstraintError = (err) => {
  const field = err.errors[0]?.path || 'unknown';
  const value = err.errors[0]?.value;
  
  return new ConflictError(`${field} 已存在: ${value}`);
};

// 处理JWT错误
const handleJWTError = (err) => {
  if (err.name === 'TokenExpiredError') {
    return new AuthenticationError('令牌已过期');
  }
  if (err.name === 'JsonWebTokenError') {
    return new AuthenticationError('无效的令牌');
  }
  return new AuthenticationError('认证失败');
};

// 处理MongoDB错误
const handleMongoError = (err) => {
  if (err.code === 11000) {
    const field = Object.keys(err.keyValue)[0];
    return new ConflictError(`${field} 已存在`);
  }
  return new AppError('数据库错误', 500);
};

// 异步错误捕获器
const asyncHandler = (fn) => (req, res, next) => {
  Promise.resolve(fn(req, res, next)).catch(next);
};

// 主错误处理中间件
const errorHandler = (err, req, res, next) => {
  let error = err;
  
  // 处理特定类型的错误
  if (err.name === 'SequelizeValidationError') {
    error = handleSequelizeValidationError(err);
  } else if (err.name === 'SequelizeUniqueConstraintError') {
    error = handleSequelizeUniqueConstraintError(err);
  } else if (err.name === 'JsonWebTokenError' || err.name === 'TokenExpiredError') {
    error = handleJWTError(err);
  } else if (err.name === 'MongoError') {
    error = handleMongoError(err);
  } else if (err.name === 'MulterError') {
    if (err.code === 'LIMIT_FILE_SIZE') {
      error = new ValidationError('文件大小超出限制');
    } else {
      error = new ValidationError('文件上传错误');
    }
  } else if (!(error instanceof AppError)) {
    // 非操作性错误，可能是编程错误
    error = new AppError(
      isDevelopment ? err.message : '服务器内部错误',
      500,
      'INTERNAL_ERROR'
    );
  }
  
  // 记录错误日志
  if (error.statusCode >= 500) {
    logger.logApiError(req, error, {
      body: req.body,
      params: req.params,
      query: req.query
    });
  } else if (error.statusCode >= 400) {
    logger.warn('Client Error', {
      message: error.message,
      code: error.code,
      statusCode: error.statusCode,
      url: req.originalUrl,
      method: req.method,
      ip: req.ip
    });
  }
  
  // 构建错误响应
  const response = {
    success: false,
    error: {
      message: error.message,
      code: error.code,
      statusCode: error.statusCode
    }
  };
  
  // 开发环境下提供更多错误信息
  if (isDevelopment) {
    response.error.stack = error.stack;
    response.error.details = error.details;
  }
  
  // 添加请求ID（如果有）
  if (req.id) {
    response.requestId = req.id;
  }
  
  // 发送错误响应
  res.status(error.statusCode).json(response);
};

// 404处理中间件
const notFoundHandler = (req, res, next) => {
  const error = new NotFoundError('API端点');
  next(error);
};

// 未捕获的Promise拒绝处理
process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled Rejection', {
    reason: reason,
    promise: promise
  });
  
  // 在生产环境中，记录错误后优雅关闭
  if (isProduction) {
    process.exit(1);
  }
});

// 未捕获的异常处理
process.on('uncaughtException', (error) => {
  logger.error('Uncaught Exception', {
    error: error.message,
    stack: error.stack
  });
  
  // 优雅关闭
  process.exit(1);
});

// 请求ID生成中间件
const requestIdMiddleware = (req, res, next) => {
  req.id = req.headers['x-request-id'] || require('crypto').randomBytes(16).toString('hex');
  res.setHeader('X-Request-ID', req.id);
  next();
};

// 请求日志中间件
const requestLogger = (req, res, next) => {
  const start = Date.now();
  
  // 记录请求
  logger.http('Incoming Request', {
    method: req.method,
    url: req.originalUrl,
    ip: req.ip,
    requestId: req.id
  });
  
  // 监听响应完成
  res.on('finish', () => {
    const duration = Date.now() - start;
    logger.logAccess(req, res, duration);
  });
  
  next();
};

// 错误监控集成（如Sentry）
const setupErrorMonitoring = () => {
  if (process.env.SENTRY_DSN) {
    const Sentry = require('@sentry/node');
    
    Sentry.init({
      dsn: process.env.SENTRY_DSN,
      environment: process.env.NODE_ENV,
      integrations: [
        new Sentry.Integrations.Http({ tracing: true }),
      ],
      tracesSampleRate: process.env.NODE_ENV === 'production' ? 0.1 : 1.0,
    });
    
    return Sentry;
  }
  
  return null;
};

module.exports = {
  // 错误类
  AppError,
  ValidationError,
  AuthenticationError,
  AuthorizationError,
  NotFoundError,
  ConflictError,
  RateLimitError,
  ExternalServiceError,
  
  // 中间件
  errorHandler,
  notFoundHandler,
  asyncHandler,
  requestIdMiddleware,
  requestLogger,
  
  // 工具函数
  setupErrorMonitoring
};