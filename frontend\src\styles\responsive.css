/* 响应式布局断点 */
:root {
  --breakpoint-xs: 480px;
  --breakpoint-sm: 576px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 992px;
  --breakpoint-xl: 1200px;
  --breakpoint-xxl: 1400px;
  
  /* 移动端安全区域 */
  --safe-area-inset-top: env(safe-area-inset-top);
  --safe-area-inset-right: env(safe-area-inset-right);
  --safe-area-inset-bottom: env(safe-area-inset-bottom);
  --safe-area-inset-left: env(safe-area-inset-left);
}

/* 移动端优化基础样式 */
* {
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  user-select: none;
}

/* 可选择的文本 */
.selectable,
input,
textarea {
  -webkit-user-select: text;
  user-select: text;
}

/* 防止iOS缩放 */
input[type="text"],
input[type="email"],
input[type="tel"],
input[type="password"],
textarea {
  font-size: 16px;
}

/* 移动端滚动优化 */
.scrollable {
  -webkit-overflow-scrolling: touch;
  overflow-y: auto;
}

/* 隐藏滚动条但保持滚动功能 */
.hide-scrollbar {
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.hide-scrollbar::-webkit-scrollbar {
  display: none;
}

/* 响应式容器 */
.container-responsive {
  width: 100%;
  padding-right: 15px;
  padding-left: 15px;
  margin-right: auto;
  margin-left: auto;
}

@media (min-width: 576px) {
  .container-responsive {
    max-width: 540px;
  }
}

@media (min-width: 768px) {
  .container-responsive {
    max-width: 720px;
  }
}

@media (min-width: 992px) {
  .container-responsive {
    max-width: 960px;
  }
}

@media (min-width: 1200px) {
  .container-responsive {
    max-width: 1140px;
  }
}

/* 响应式网格系统 */
.row {
  display: flex;
  flex-wrap: wrap;
  margin-right: -15px;
  margin-left: -15px;
}

.col {
  flex: 1 0 0%;
  padding-right: 15px;
  padding-left: 15px;
}

/* 移动优先的列宽度 */
.col-12 { flex: 0 0 100%; max-width: 100%; }
.col-6 { flex: 0 0 50%; max-width: 50%; }
.col-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
.col-3 { flex: 0 0 25%; max-width: 25%; }

/* 平板断点 */
@media (min-width: 768px) {
  .col-md-12 { flex: 0 0 100%; max-width: 100%; }
  .col-md-6 { flex: 0 0 50%; max-width: 50%; }
  .col-md-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
  .col-md-3 { flex: 0 0 25%; max-width: 25%; }
}

/* 桌面断点 */
@media (min-width: 992px) {
  .col-lg-12 { flex: 0 0 100%; max-width: 100%; }
  .col-lg-6 { flex: 0 0 50%; max-width: 50%; }
  .col-lg-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
  .col-lg-3 { flex: 0 0 25%; max-width: 25%; }
}

/* 响应式间距 */
.spacing-responsive {
  padding: 1rem;
}

@media (min-width: 768px) {
  .spacing-responsive {
    padding: 1.5rem;
  }
}

@media (min-width: 992px) {
  .spacing-responsive {
    padding: 2rem;
  }
}

/* 响应式字体 */
.text-responsive {
  font-size: 14px;
  line-height: 1.5;
}

@media (min-width: 768px) {
  .text-responsive {
    font-size: 16px;
    line-height: 1.6;
  }
}

.heading-responsive {
  font-size: 1.5rem;
  line-height: 1.2;
}

@media (min-width: 768px) {
  .heading-responsive {
    font-size: 2rem;
    line-height: 1.3;
  }
}

/* 移动端导航栏适配 */
.mobile-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  padding-bottom: var(--safe-area-inset-bottom);
}

/* 触摸友好的按钮 */
.btn-touch {
  min-height: 44px;
  min-width: 44px;
  padding: 12px 24px;
  font-size: 16px;
  touch-action: manipulation;
}

/* 移动端卡片 */
.card-mobile {
  margin: 0 -15px;
  border-radius: 0;
}

@media (min-width: 768px) {
  .card-mobile {
    margin: 0;
    border-radius: 8px;
  }
}

/* 响应式图片 */
.img-responsive {
  max-width: 100%;
  height: auto;
  display: block;
}

/* 响应式表格 */
.table-responsive {
  display: block;
  width: 100%;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

/* 移动端表格优化 */
@media (max-width: 767px) {
  .table-mobile {
    border: 0;
  }
  
  .table-mobile thead {
    display: none;
  }
  
  .table-mobile tr {
    display: block;
    margin-bottom: 10px;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 10px;
  }
  
  .table-mobile td {
    display: block;
    text-align: right;
    padding-left: 50%;
    position: relative;
  }
  
  .table-mobile td:before {
    content: attr(data-label);
    position: absolute;
    left: 10px;
    font-weight: bold;
  }
}

/* 移动端模态框 */
.modal-mobile {
  margin: 0;
  width: 100%;
  height: 100%;
  max-width: 100%;
  max-height: 100%;
}

@media (min-width: 768px) {
  .modal-mobile {
    margin: 1.75rem auto;
    width: auto;
    max-width: 500px;
    height: auto;
    max-height: calc(100vh - 3.5rem);
  }
}

/* 响应式隐藏/显示 */
.d-none { display: none !important; }
.d-block { display: block !important; }
.d-flex { display: flex !important; }

@media (min-width: 768px) {
  .d-md-none { display: none !important; }
  .d-md-block { display: block !important; }
  .d-md-flex { display: flex !important; }
}

@media (min-width: 992px) {
  .d-lg-none { display: none !important; }
  .d-lg-block { display: block !important; }
  .d-lg-flex { display: flex !important; }
}

/* 移动端表单优化 */
.form-mobile {
  padding: 16px;
}

.form-mobile .form-group {
  margin-bottom: 20px;
}

.form-mobile label {
  font-size: 14px;
  margin-bottom: 8px;
  display: block;
}

.form-mobile input,
.form-mobile select,
.form-mobile textarea {
  width: 100%;
  padding: 12px;
  font-size: 16px;
  border: 1px solid #ddd;
  border-radius: 8px;
  background-color: #fff;
  -webkit-appearance: none;
}

/* 移动端列表优化 */
.list-mobile {
  padding: 0;
  margin: 0;
  list-style: none;
}

.list-mobile-item {
  padding: 16px;
  border-bottom: 1px solid #eee;
  display: flex;
  align-items: center;
  transition: background-color 0.2s;
}

.list-mobile-item:active {
  background-color: #f0f0f0;
}

/* 触摸滑动组件 */
.swiper-container {
  overflow: hidden;
  position: relative;
}

.swiper-wrapper {
  display: flex;
  transition-property: transform;
}

.swiper-slide {
  flex-shrink: 0;
  width: 100%;
}

/* 固定底部操作栏 */
.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 12px 16px;
  padding-bottom: calc(12px + var(--safe-area-inset-bottom));
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
  z-index: 999;
}

/* 加载更多 */
.load-more {
  text-align: center;
  padding: 20px;
  color: #666;
}

.load-more.loading {
  pointer-events: none;
  opacity: 0.6;
}

/* 下拉刷新 */
.pull-to-refresh {
  position: relative;
  overflow: hidden;
}

.pull-to-refresh-indicator {
  position: absolute;
  top: -50px;
  left: 50%;
  transform: translateX(-50%);
  transition: top 0.3s;
}

/* 骨架屏 */
.skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #999;
}

.empty-state-icon {
  font-size: 64px;
  margin-bottom: 16px;
  opacity: 0.3;
}

.empty-state-text {
  font-size: 16px;
  margin-bottom: 20px;
}

/* 移动端优化的工具类 */
.text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 触摸反馈 */
.touchable {
  transition: transform 0.1s, opacity 0.1s;
}

.touchable:active {
  transform: scale(0.98);
  opacity: 0.8;
}

/* 安全区域适配 */
.safe-area-top {
  padding-top: var(--safe-area-inset-top);
}

.safe-area-bottom {
  padding-bottom: var(--safe-area-inset-bottom);
}

/* 横屏适配 */
@media (orientation: landscape) and (max-height: 500px) {
  .mobile-nav {
    display: none;
  }
  
  .container-responsive {
    max-height: 100vh;
    overflow-y: auto;
  }
}