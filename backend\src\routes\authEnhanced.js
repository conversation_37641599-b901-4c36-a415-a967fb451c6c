const express = require('express');
const crypto = require('crypto');
const { User } = require('../models');
const { validate, registerSchema, loginSchema } = require('../utils/validation');
const {
  generateTokenPair,
  authenticateToken,
  refreshAccessToken,
  logout,
  getActiveSessions,
  revokeSession
} = require('../middleware/authEnhanced');

const router = express.Router();

// 设备信息解析
const parseDeviceInfo = (req) => {
  const userAgent = req.headers['user-agent'] || '';
  const ipAddress = req.ip || req.connection.remoteAddress;
  
  // 简单的设备名称解析（实际应用中可以使用更复杂的库）
  let deviceName = 'Unknown Device';
  if (userAgent.includes('iPhone')) deviceName = 'iPhone';
  else if (userAgent.includes('Android')) deviceName = 'Android Device';
  else if (userAgent.includes('Windows')) deviceName = 'Windows PC';
  else if (userAgent.includes('Mac')) deviceName = 'Mac';
  else if (userAgent.includes('Linux')) deviceName = 'Linux PC';
  
  // 生成设备ID（基于UA和IP的哈希）
  const deviceId = crypto
    .createHash('sha256')
    .update(userAgent + ipAddress)
    .digest('hex')
    .substring(0, 16);
  
  return {
    deviceId,
    deviceName,
    ipAddress,
    userAgent
  };
};

// 用户注册
router.post('/register', validate(registerSchema), async (req, res) => {
  try {
    const { phone, email, password, username } = req.body;

    // 检查手机号是否已存在
    const existingUser = await User.findOne({ where: { phone } });
    if (existingUser) {
      return res.status(400).json({
        success: false,
        message: '手机号已被注册',
        code: 'PHONE_EXISTS'
      });
    }

    // 检查邮箱是否已存在（如果提供了邮箱）
    if (email) {
      const existingEmail = await User.findOne({ where: { email } });
      if (existingEmail) {
        return res.status(400).json({
          success: false,
          message: '邮箱已被注册',
          code: 'EMAIL_EXISTS'
        });
      }
    }

    // 检查用户名是否已存在（如果提供了用户名）
    if (username) {
      const existingUsername = await User.findOne({ where: { username } });
      if (existingUsername) {
        return res.status(400).json({
          success: false,
          message: '用户名已被使用',
          code: 'USERNAME_EXISTS'
        });
      }
    }

    // 创建用户
    const user = await User.create({
      phone,
      email,
      username,
      password_hash: password, // 会在模型的hook中自动加密
      register_type: 'phone',
      phone_verified: false,
      email_verified: false
    });

    // 生成令牌对
    const deviceInfo = parseDeviceInfo(req);
    const tokens = await generateTokenPair(user.id, deviceInfo);

    // 发送验证码（生产环境中实现）
    // await sendVerificationCode(phone);

    res.status(201).json({
      success: true,
      message: '注册成功',
      data: {
        user: {
          id: user.id,
          phone: user.phone,
          email: user.email,
          username: user.username,
          role: user.role,
          phone_verified: user.phone_verified,
          email_verified: user.email_verified
        },
        access_token: tokens.accessToken,
        refresh_token: tokens.refreshToken,
        expires_in: tokens.expiresIn
      }
    });
  } catch (error) {
    console.error('注册错误:', error);
    res.status(500).json({
      success: false,
      message: '注册失败',
      code: 'REGISTRATION_FAILED'
    });
  }
});

// 用户登录
router.post('/login', validate(loginSchema), async (req, res) => {
  try {
    const { phone, password, device_name } = req.body;

    // 查找用户
    const user = await User.findOne({ 
      where: { phone },
      attributes: ['id', 'phone', 'email', 'username', 'password_hash', 'role', 'status', 'phone_verified', 'email_verified']
    });
    
    if (!user) {
      return res.status(401).json({
        success: false,
        message: '手机号或密码错误',
        code: 'INVALID_CREDENTIALS'
      });
    }

    // 验证密码
    const isValidPassword = await user.validatePassword(password);
    if (!isValidPassword) {
      return res.status(401).json({
        success: false,
        message: '手机号或密码错误',
        code: 'INVALID_CREDENTIALS'
      });
    }

    // 检查账户状态
    if (user.status !== 'active') {
      return res.status(401).json({
        success: false,
        message: '账户已被禁用',
        code: 'ACCOUNT_DISABLED'
      });
    }

    // 更新最后登录时间
    await user.update({ last_login_at: new Date() });

    // 生成令牌对
    const deviceInfo = parseDeviceInfo(req);
    if (device_name) {
      deviceInfo.deviceName = device_name;
    }
    const tokens = await generateTokenPair(user.id, deviceInfo);

    res.json({
      success: true,
      message: '登录成功',
      data: {
        user: {
          id: user.id,
          phone: user.phone,
          email: user.email,
          username: user.username,
          role: user.role,
          phone_verified: user.phone_verified,
          email_verified: user.email_verified
        },
        access_token: tokens.accessToken,
        refresh_token: tokens.refreshToken,
        expires_in: tokens.expiresIn
      }
    });
  } catch (error) {
    console.error('登录错误:', error);
    res.status(500).json({
      success: false,
      message: '登录失败',
      code: 'LOGIN_FAILED'
    });
  }
});

// 刷新令牌
router.post('/refresh', refreshAccessToken);

// 登出（需要认证）
router.post('/logout', authenticateToken, logout);

// 获取活跃会话（需要认证）
router.get('/sessions', authenticateToken, getActiveSessions);

// 撤销特定会话（需要认证）
router.delete('/sessions/:sessionId', authenticateToken, revokeSession);

// 手机号验证
router.post('/verify-phone', authenticateToken, async (req, res) => {
  try {
    const { code } = req.body;
    
    // 验证验证码（生产环境中实现）
    // const isValid = await verifyPhoneCode(req.user.phone, code);
    const isValid = code === '123456'; // 测试用
    
    if (!isValid) {
      return res.status(400).json({
        success: false,
        message: '验证码错误',
        code: 'INVALID_CODE'
      });
    }
    
    // 更新用户验证状态
    await User.update(
      { phone_verified: true },
      { where: { id: req.user.id } }
    );
    
    res.json({
      success: true,
      message: '手机号验证成功'
    });
  } catch (error) {
    console.error('手机验证错误:', error);
    res.status(500).json({
      success: false,
      message: '验证失败',
      code: 'VERIFICATION_FAILED'
    });
  }
});

// 邮箱验证
router.post('/verify-email', authenticateToken, async (req, res) => {
  try {
    const { token } = req.body;
    
    // 验证邮箱令牌（生产环境中实现）
    // const isValid = await verifyEmailToken(req.user.email, token);
    const isValid = token === 'test-token'; // 测试用
    
    if (!isValid) {
      return res.status(400).json({
        success: false,
        message: '验证令牌无效',
        code: 'INVALID_TOKEN'
      });
    }
    
    // 更新用户验证状态
    await User.update(
      { email_verified: true },
      { where: { id: req.user.id } }
    );
    
    res.json({
      success: true,
      message: '邮箱验证成功'
    });
  } catch (error) {
    console.error('邮箱验证错误:', error);
    res.status(500).json({
      success: false,
      message: '验证失败',
      code: 'VERIFICATION_FAILED'
    });
  }
});

// 重置密码请求
router.post('/reset-password-request', async (req, res) => {
  try {
    const { phone } = req.body;
    
    const user = await User.findOne({ where: { phone } });
    if (!user) {
      // 为了安全，即使用户不存在也返回成功
      return res.json({
        success: true,
        message: '如果手机号存在，验证码已发送'
      });
    }
    
    // 发送重置密码验证码（生产环境中实现）
    // await sendResetPasswordCode(phone);
    
    res.json({
      success: true,
      message: '如果手机号存在，验证码已发送'
    });
  } catch (error) {
    console.error('重置密码请求错误:', error);
    res.status(500).json({
      success: false,
      message: '请求失败',
      code: 'REQUEST_FAILED'
    });
  }
});

// 重置密码
router.post('/reset-password', async (req, res) => {
  try {
    const { phone, code, new_password } = req.body;
    
    // 验证重置密码验证码（生产环境中实现）
    // const isValid = await verifyResetPasswordCode(phone, code);
    const isValid = code === '123456'; // 测试用
    
    if (!isValid) {
      return res.status(400).json({
        success: false,
        message: '验证码错误或已过期',
        code: 'INVALID_CODE'
      });
    }
    
    const user = await User.findOne({ where: { phone } });
    if (!user) {
      return res.status(404).json({
        success: false,
        message: '用户不存在',
        code: 'USER_NOT_FOUND'
      });
    }
    
    // 更新密码
    await user.update({ password_hash: new_password });
    
    // 撤销该用户的所有令牌
    const { RefreshToken } = require('../models');
    await RefreshToken.revokeAllUserTokens(user.id);
    
    res.json({
      success: true,
      message: '密码重置成功，请重新登录'
    });
  } catch (error) {
    console.error('重置密码错误:', error);
    res.status(500).json({
      success: false,
      message: '重置密码失败',
      code: 'RESET_FAILED'
    });
  }
});

// 修改密码（需要认证）
router.post('/change-password', authenticateToken, async (req, res) => {
  try {
    const { old_password, new_password } = req.body;
    
    // 验证旧密码
    const user = await User.findByPk(req.user.id);
    const isValidPassword = await user.validatePassword(old_password);
    
    if (!isValidPassword) {
      return res.status(400).json({
        success: false,
        message: '原密码错误',
        code: 'INVALID_OLD_PASSWORD'
      });
    }
    
    // 更新密码
    await user.update({ password_hash: new_password });
    
    // 撤销该用户的所有其他令牌（保留当前会话）
    // 实际实现需要更复杂的逻辑
    
    res.json({
      success: true,
      message: '密码修改成功'
    });
  } catch (error) {
    console.error('修改密码错误:', error);
    res.status(500).json({
      success: false,
      message: '修改密码失败',
      code: 'CHANGE_PASSWORD_FAILED'
    });
  }
});

module.exports = router;