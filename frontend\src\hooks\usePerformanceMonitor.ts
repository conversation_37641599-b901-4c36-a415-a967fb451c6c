import { useEffect, useRef, useCallback } from 'react';

interface PerformanceMetrics {
  renderTime: number;
  componentName: string;
  timestamp: number;
}

interface PerformanceConfig {
  enabled?: boolean;
  threshold?: number; // 渲染时间阈值（毫秒）
  logToConsole?: boolean;
  reportToServer?: boolean;
}

/**
 * 性能监控Hook
 * 监控组件渲染时间和性能指标
 */
export function usePerformanceMonitor(
  componentName: string,
  config: PerformanceConfig = {}
) {
  const {
    enabled = process.env.NODE_ENV === 'development',
    threshold = 16, // 60fps = 16.67ms per frame
    logToConsole = true,
    reportToServer = false
  } = config;

  const renderStartTime = useRef<number>(0);
  const metricsRef = useRef<PerformanceMetrics[]>([]);

  // 开始测量渲染时间
  const startMeasurement = useCallback(() => {
    if (enabled) {
      renderStartTime.current = performance.now();
    }
  }, [enabled]);

  // 结束测量并记录指标
  const endMeasurement = useCallback(() => {
    if (enabled && renderStartTime.current > 0) {
      const renderTime = performance.now() - renderStartTime.current;
      const metrics: PerformanceMetrics = {
        renderTime,
        componentName,
        timestamp: Date.now()
      };

      metricsRef.current.push(metrics);

      // 如果渲染时间超过阈值，发出警告
      if (renderTime > threshold) {
        if (logToConsole) {
          console.warn(
            `🐌 Slow render detected in ${componentName}: ${renderTime.toFixed(2)}ms`
          );
        }

        if (reportToServer) {
          reportPerformanceIssue(metrics);
        }
      }

      renderStartTime.current = 0;
    }
  }, [enabled, componentName, threshold, logToConsole, reportToServer]);

  // 组件挂载时开始测量
  useEffect(() => {
    startMeasurement();
    return endMeasurement;
  });

  // 获取性能统计
  const getMetrics = useCallback(() => {
    const metrics = metricsRef.current;
    if (metrics.length === 0) return null;

    const totalTime = metrics.reduce((sum, m) => sum + m.renderTime, 0);
    const avgTime = totalTime / metrics.length;
    const maxTime = Math.max(...metrics.map(m => m.renderTime));
    const minTime = Math.min(...metrics.map(m => m.renderTime));

    return {
      componentName,
      totalRenders: metrics.length,
      averageRenderTime: avgTime,
      maxRenderTime: maxTime,
      minRenderTime: minTime,
      totalTime
    };
  }, [componentName]);

  // 清除指标
  const clearMetrics = useCallback(() => {
    metricsRef.current = [];
  }, []);

  return {
    startMeasurement,
    endMeasurement,
    getMetrics,
    clearMetrics
  };
}

/**
 * 上报性能问题到服务器
 */
async function reportPerformanceIssue(metrics: PerformanceMetrics) {
  try {
    await fetch('/api/performance/report', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        type: 'slow_render',
        metrics,
        userAgent: navigator.userAgent,
        url: window.location.href,
        timestamp: Date.now()
      })
    });
  } catch (error) {
    console.error('Failed to report performance issue:', error);
  }
}

/**
 * 监控Web Vitals
 */
export function useWebVitals() {
  useEffect(() => {
    // Largest Contentful Paint (LCP)
    const observer = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        if (entry.entryType === 'largest-contentful-paint') {
          console.log('LCP:', entry.startTime);
        }
      }
    });

    observer.observe({ entryTypes: ['largest-contentful-paint'] });

    // First Input Delay (FID)
    const fidObserver = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        const fid = entry.processingStart - entry.startTime;
        console.log('FID:', fid);
      }
    });

    try {
      fidObserver.observe({ entryTypes: ['first-input'] });
    } catch (e) {
      // Browser doesn't support first-input entries
    }

    return () => {
      observer.disconnect();
      fidObserver.disconnect();
    };
  }, []);
}

/**
 * 内存使用监控Hook
 */
export function useMemoryMonitor() {
  const checkMemoryUsage = useCallback(() => {
    if ('memory' in performance) {
      const memInfo = (performance as any).memory;
      const memoryUsage = {
        used: Math.round(memInfo.usedJSHeapSize / 1048576), // MB
        total: Math.round(memInfo.totalJSHeapSize / 1048576), // MB
        limit: Math.round(memInfo.jsHeapSizeLimit / 1048576) // MB
      };

      console.log('Memory Usage:', memoryUsage);
      
      // 警告内存使用过高
      if (memoryUsage.used > memoryUsage.limit * 0.8) {
        console.warn('⚠️ High memory usage detected:', memoryUsage);
      }

      return memoryUsage;
    }
    return null;
  }, []);

  useEffect(() => {
    const interval = setInterval(checkMemoryUsage, 30000); // 每30秒检查一次
    return () => clearInterval(interval);
  }, [checkMemoryUsage]);

  return { checkMemoryUsage };
}