const express = require('express');
const { Op } = require('sequelize');
const { Book, Category, User, Favorite, Review } = require('../models');
const { authenticateToken, requireAdmin } = require('../middleware/authEnhanced');
const { validate, createBookSchema } = require('../utils/validation');
const { QueryBuilder, createLoaders, preloadAssociations, QueryMonitor } = require('../utils/queryOptimizer');
const { cacheStrategies, invalidateCache } = require('../middleware/cache');
const cacheService = require('../services/cacheService');

const router = express.Router();
const queryMonitor = new QueryMonitor();

// 创建数据加载器实例
const loaders = createLoaders({ User, Book, Category });

// 获取图书列表（优化版，带缓存）
router.get('/', cacheStrategies.bookList, async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      search,
      category_id,
      min_price,
      max_price,
      condition,
      sort_by = 'created_at',
      sort_order = 'DESC'
    } = req.query;

    // 使用查询构建器
    const queryBuilder = new QueryBuilder(Book, {
      defaultAttributes: [
        'id', 'isbn', 'title', 'author', 'publisher',
        'category_id', 'price', 'original_price', 'condition',
        'cover_image', 'status', 'views', 'sales_count',
        'created_at', 'created_by', 'stock'
      ]
    });

    // 构建查询条件
    const where = { status: '上架' };

    // 搜索条件 - 使用全文搜索索引
    if (search) {
      where[Op.or] = [
        { title: { [Op.iLike]: `%${search}%` } },
        { author: { [Op.iLike]: `%${search}%` } },
        { isbn: { [Op.iLike]: `%${search}%` } }
      ];
    }

    // 分类筛选
    if (category_id) {
      where.category_id = category_id;
    }

    // 价格筛选
    if (min_price || max_price) {
      where.price = {};
      if (min_price) where.price[Op.gte] = parseFloat(min_price);
      if (max_price) where.price[Op.lte] = parseFloat(max_price);
    }

    // 状况筛选
    if (condition) {
      where.condition = condition;
    }

    // 执行查询
    const result = await queryMonitor.monitor(async () => {
      const { count, rows: books } = await queryBuilder
        .where(where)
        .order([[sort_by, sort_order.toUpperCase()]])
        .paginate(page, limit)
        .execute();

      // 批量加载关联数据
      if (books.length > 0) {
        // 收集所有需要的ID
        const categoryIds = [...new Set(books.map(b => b.category_id).filter(Boolean))];
        const creatorIds = [...new Set(books.map(b => b.created_by).filter(Boolean))];

        // 并行加载所有关联数据
        const [categories, creators] = await Promise.all([
          categoryIds.length > 0 ? loaders.categoryLoader.loadMany(categoryIds) : [],
          creatorIds.length > 0 ? loaders.userLoader.loadMany(creatorIds) : []
        ]);

        // 创建映射
        const categoryMap = new Map(categories.filter(Boolean).map(c => [c.id, c]));
        const creatorMap = new Map(creators.filter(Boolean).map(u => [u.id, u]));

        // 附加关联数据到书籍
        books.forEach(book => {
          book.dataValues.category = categoryMap.get(book.category_id) || null;
          book.dataValues.creator = creatorMap.get(book.created_by) || null;
        });
      }

      return { count, books };
    }, 'getBooksList');

    res.json({
      success: true,
      data: {
        books: result.books,
        pagination: {
          current_page: parseInt(page),
          total_pages: Math.ceil(result.count / limit),
          total_items: result.count,
          items_per_page: parseInt(limit)
        }
      }
    });
  } catch (error) {
    console.error('获取图书列表错误:', error);
    res.status(500).json({
      success: false,
      message: '获取图书列表失败'
    });
  }
});

// 获取图书详情（优化版，带缓存）
router.get('/:id', cacheStrategies.bookDetail, async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user?.id;

    const book = await queryMonitor.monitor(async () => {
      // 基础查询
      const book = await Book.findByPk(id, {
        attributes: [
          'id', 'isbn', 'title', 'author', 'publisher', 'publication_date',
          'category_id', 'description', 'condition', 'price', 'original_price',
          'stock', 'cover_image', 'images', 'status', 'views', 'sales_count',
          'created_by', 'created_at', 'updated_at'
        ]
      });

      if (!book) return null;

      // 并行加载所有需要的数据
      const [category, creator, reviewStats, isFavorited] = await Promise.all([
        book.category_id ? loaders.categoryLoader.load(book.category_id) : null,
        book.created_by ? loaders.userLoader.load(book.created_by) : null,
        // 评论统计
        Review.findOne({
          where: { book_id: id },
          attributes: [
            [sequelize.fn('COUNT', sequelize.col('id')), 'total_reviews'],
            [sequelize.fn('AVG', sequelize.col('rating')), 'avg_rating']
          ]
        }),
        // 检查是否收藏（如果用户已登录）
        userId ? Favorite.findOne({
          where: { user_id: userId, book_id: id }
        }) : null
      ]);

      // 组装数据
      book.dataValues.category = category;
      book.dataValues.creator = creator;
      book.dataValues.review_stats = reviewStats?.dataValues || { total_reviews: 0, avg_rating: 0 };
      book.dataValues.is_favorited = !!isFavorited;

      return book;
    }, 'getBookDetail');

    if (!book) {
      return res.status(404).json({
        success: false,
        message: '图书不存在'
      });
    }

    // 异步增加浏览次数（不阻塞响应）
    setImmediate(async () => {
      try {
        await book.increment('views');
      } catch (error) {
        console.error('更新浏览次数失败:', error);
      }
    });

    res.json({
      success: true,
      data: book
    });
  } catch (error) {
    console.error('获取图书详情错误:', error);
    res.status(500).json({
      success: false,
      message: '获取图书详情失败'
    });
  }
});

// 搜索建议（使用缓存）
router.get('/search/suggestions', cacheStrategies.medium, async (req, res) => {
  try {
    const { q } = req.query;
    
    if (!q || q.length < 2) {
      return res.json({
        success: true,
        data: { suggestions: [] }
      });
    }

    const suggestions = await queryMonitor.monitor(async () => {
      // 使用数据库的全文搜索功能
      const books = await Book.findAll({
        where: {
          status: '上架',
          [Op.or]: [
            { title: { [Op.iLike]: `${q}%` } },
            { author: { [Op.iLike]: `${q}%` } }
          ]
        },
        attributes: ['id', 'title', 'author'],
        limit: 10,
        order: [['views', 'DESC']] // 按热度排序
      });

      return books.map(book => ({
        id: book.id,
        text: book.title,
        subtitle: book.author
      }));
    }, 'searchSuggestions');

    res.json({
      success: true,
      data: { suggestions }
    });
  } catch (error) {
    console.error('搜索建议错误:', error);
    res.status(500).json({
      success: false,
      message: '获取搜索建议失败'
    });
  }
});

// 热门图书（使用缓存）
router.get('/popular/trending', cacheStrategies.trending, async (req, res) => {
  try {
    const { limit = 10, timeframe = '7d' } = req.query;

    // 计算时间范围
    const dateLimit = new Date();
    const days = parseInt(timeframe) || 7;
    dateLimit.setDate(dateLimit.getDate() - days);

    const books = await queryMonitor.monitor(async () => {
      const result = await Book.findAll({
        where: {
          status: '上架',
          updated_at: { [Op.gte]: dateLimit }
        },
        attributes: [
          'id', 'title', 'author', 'cover_image', 'price',
          'views', 'sales_count', 'category_id'
        ],
        order: [
          ['sales_count', 'DESC'],
          ['views', 'DESC']
        ],
        limit: parseInt(limit)
      });

      // 批量加载分类
      if (result.length > 0) {
        const categoryIds = [...new Set(result.map(b => b.category_id).filter(Boolean))];
        const categories = await loaders.categoryLoader.loadMany(categoryIds);
        const categoryMap = new Map(categories.filter(Boolean).map(c => [c.id, c]));
        
        result.forEach(book => {
          book.dataValues.category = categoryMap.get(book.category_id) || null;
        });
      }

      return result;
    }, 'getTrendingBooks');

    res.json({
      success: true,
      data: { books }
    });
  } catch (error) {
    console.error('获取热门图书错误:', error);
    res.status(500).json({
      success: false,
      message: '获取热门图书失败'
    });
  }
});

// 分类下的图书（优化版）
router.get('/category/:categoryId', async (req, res) => {
  try {
    const { categoryId } = req.params;
    const { page = 1, limit = 20 } = req.query;

    const result = await queryMonitor.monitor(async () => {
      const queryBuilder = new QueryBuilder(Book);
      
      const { count, rows: books } = await queryBuilder
        .where({ category_id: categoryId, status: '上架' })
        .select(['id', 'title', 'author', 'cover_image', 'price', 'condition', 'created_at'])
        .paginate(page, limit)
        .execute();

      return { count, books };
    }, 'getBooksByCategory');

    res.json({
      success: true,
      data: {
        books: result.books,
        pagination: {
          current_page: parseInt(page),
          total_pages: Math.ceil(result.count / limit),
          total_items: result.count,
          items_per_page: parseInt(limit)
        }
      }
    });
  } catch (error) {
    console.error('获取分类图书错误:', error);
    res.status(500).json({
      success: false,
      message: '获取分类图书失败'
    });
  }
});

// 获取查询性能报告（仅管理员）
router.get('/admin/performance-report', authenticateToken, requireAdmin, (req, res) => {
  const report = queryMonitor.getReport();
  res.json({
    success: true,
    data: report
  });
});

// 重置性能监控（仅管理员）
router.post('/admin/reset-monitor', authenticateToken, requireAdmin, (req, res) => {
  queryMonitor.reset();
  res.json({
    success: true,
    message: '性能监控已重置'
  });
});

module.exports = router;