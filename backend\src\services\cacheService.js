const { cacheManager, CACHE_PREFIX, CACHE_TTL } = require('../config/redis');

class CacheService {
  constructor() {
    this.manager = cacheManager;
  }

  // ==================== 图书缓存 ====================
  
  async getBook(bookId) {
    const key = `${CACHE_PREFIX.BOOK}${bookId}`;
    return await this.manager.get(key);
  }

  async setBook(bookId, bookData, ttl = CACHE_TTL.LONG) {
    const key = `${CACHE_PREFIX.BOOK}${bookId}`;
    return await this.manager.set(key, bookData, ttl);
  }

  async deleteBook(bookId) {
    const key = `${CACHE_PREFIX.BOOK}${bookId}`;
    await this.manager.del(key);
    
    // 删除相关的列表缓存
    await this.manager.delPattern(`${CACHE_PREFIX.BOOK}list:*`);
    await this.manager.delPattern(`${CACHE_PREFIX.TRENDING}*`);
  }

  async getBookList(params) {
    const { page = 1, limit = 20, search = '', category_id = '', sort_by = '', sort_order = '' } = params;
    const key = `${CACHE_PREFIX.BOOK}list:${page}:${limit}:${search}:${category_id}:${sort_by}:${sort_order}`;
    return await this.manager.get(key);
  }

  async setBookList(params, data, ttl = CACHE_TTL.MEDIUM) {
    const { page = 1, limit = 20, search = '', category_id = '', sort_by = '', sort_order = '' } = params;
    const key = `${CACHE_PREFIX.BOOK}list:${page}:${limit}:${search}:${category_id}:${sort_by}:${sort_order}`;
    return await this.manager.set(key, data, ttl);
  }

  // ==================== 用户缓存 ====================
  
  async getUser(userId) {
    const key = `${CACHE_PREFIX.USER}${userId}`;
    return await this.manager.get(key);
  }

  async setUser(userId, userData, ttl = CACHE_TTL.MEDIUM) {
    const key = `${CACHE_PREFIX.USER}${userId}`;
    return await this.manager.set(key, userData, ttl);
  }

  async deleteUser(userId) {
    const key = `${CACHE_PREFIX.USER}${userId}`;
    await this.manager.del(key);
    
    // 删除用户相关的所有缓存
    await this.manager.delPattern(`${CACHE_PREFIX.USER}${userId}:*`);
  }

  async getUserStats(userId) {
    const key = `${CACHE_PREFIX.USER}${userId}:stats`;
    return await this.manager.get(key);
  }

  async setUserStats(userId, stats, ttl = CACHE_TTL.SHORT) {
    const key = `${CACHE_PREFIX.USER}${userId}:stats`;
    return await this.manager.set(key, stats, ttl);
  }

  // ==================== 订单缓存 ====================
  
  async getOrder(orderId) {
    const key = `${CACHE_PREFIX.ORDER}${orderId}`;
    return await this.manager.get(key);
  }

  async setOrder(orderId, orderData, ttl = CACHE_TTL.SHORT) {
    const key = `${CACHE_PREFIX.ORDER}${orderId}`;
    return await this.manager.set(key, orderData, ttl);
  }

  async deleteOrder(orderId) {
    const key = `${CACHE_PREFIX.ORDER}${orderId}`;
    await this.manager.del(key);
  }

  async getUserOrders(userId, page = 1) {
    const key = `${CACHE_PREFIX.USER}${userId}:orders:${page}`;
    return await this.manager.get(key);
  }

  async setUserOrders(userId, page, orders, ttl = CACHE_TTL.SHORT) {
    const key = `${CACHE_PREFIX.USER}${userId}:orders:${page}`;
    return await this.manager.set(key, orders, ttl);
  }

  async invalidateUserOrders(userId) {
    await this.manager.delPattern(`${CACHE_PREFIX.USER}${userId}:orders:*`);
  }

  // ==================== 分类缓存 ====================
  
  async getAllCategories() {
    const key = `${CACHE_PREFIX.CATEGORY}all`;
    return await this.manager.get(key);
  }

  async setAllCategories(categories, ttl = CACHE_TTL.DAY) {
    const key = `${CACHE_PREFIX.CATEGORY}all`;
    return await this.manager.set(key, categories, ttl);
  }

  async getCategory(categoryId) {
    const key = `${CACHE_PREFIX.CATEGORY}${categoryId}`;
    return await this.manager.get(key);
  }

  async setCategory(categoryId, categoryData, ttl = CACHE_TTL.DAY) {
    const key = `${CACHE_PREFIX.CATEGORY}${categoryId}`;
    return await this.manager.set(key, categoryData, ttl);
  }

  async invalidateCategories() {
    await this.manager.delPattern(`${CACHE_PREFIX.CATEGORY}*`);
  }

  // ==================== 会话管理 ====================
  
  async getSession(sessionId) {
    const key = `${CACHE_PREFIX.SESSION}${sessionId}`;
    return await this.manager.get(key);
  }

  async setSession(sessionId, sessionData, ttl = CACHE_TTL.DAY) {
    const key = `${CACHE_PREFIX.SESSION}${sessionId}`;
    return await this.manager.set(key, sessionData, ttl);
  }

  async deleteSession(sessionId) {
    const key = `${CACHE_PREFIX.SESSION}${sessionId}`;
    await this.manager.del(key);
  }

  async extendSession(sessionId, ttl = CACHE_TTL.DAY) {
    const key = `${CACHE_PREFIX.SESSION}${sessionId}`;
    return await this.manager.expire(key, ttl);
  }

  // ==================== 搜索缓存 ====================
  
  async getSearchResults(query, page = 1) {
    const key = `${CACHE_PREFIX.SEARCH}${query}:${page}`;
    return await this.manager.get(key);
  }

  async setSearchResults(query, page, results, ttl = CACHE_TTL.MEDIUM) {
    const key = `${CACHE_PREFIX.SEARCH}${query}:${page}`;
    return await this.manager.set(key, results, ttl);
  }

  async getSearchSuggestions(query) {
    const key = `${CACHE_PREFIX.SEARCH}suggest:${query}`;
    return await this.manager.get(key);
  }

  async setSearchSuggestions(query, suggestions, ttl = CACHE_TTL.LONG) {
    const key = `${CACHE_PREFIX.SEARCH}suggest:${query}`;
    return await this.manager.set(key, suggestions, ttl);
  }

  // ==================== 热门数据缓存 ====================
  
  async getTrendingBooks(timeframe = 'day') {
    const key = `${CACHE_PREFIX.TRENDING}books:${timeframe}`;
    return await this.manager.get(key);
  }

  async setTrendingBooks(timeframe, books, ttl = CACHE_TTL.LONG) {
    const key = `${CACHE_PREFIX.TRENDING}books:${timeframe}`;
    return await this.manager.set(key, books, ttl);
  }

  async getHotCategories() {
    const key = `${CACHE_PREFIX.TRENDING}categories`;
    return await this.manager.get(key);
  }

  async setHotCategories(categories, ttl = CACHE_TTL.LONG) {
    const key = `${CACHE_PREFIX.TRENDING}categories`;
    return await this.manager.set(key, categories, ttl);
  }

  // ==================== 统计缓存 ====================
  
  async getDashboardStats() {
    const key = `${CACHE_PREFIX.STATS}dashboard`;
    return await this.manager.get(key);
  }

  async setDashboardStats(stats, ttl = CACHE_TTL.MEDIUM) {
    const key = `${CACHE_PREFIX.STATS}dashboard`;
    return await this.manager.set(key, stats, ttl);
  }

  async getUserActivityStats(userId, date) {
    const key = `${CACHE_PREFIX.STATS}user:${userId}:${date}`;
    return await this.manager.get(key);
  }

  async setUserActivityStats(userId, date, stats, ttl = CACHE_TTL.DAY) {
    const key = `${CACHE_PREFIX.STATS}user:${userId}:${date}`;
    return await this.manager.set(key, stats, ttl);
  }

  // ==================== 速率限制 ====================
  
  async checkRateLimit(identifier, limit = 100, window = 3600) {
    const key = `${CACHE_PREFIX.RATE_LIMIT}${identifier}`;
    const current = await this.manager.incr(key);
    
    if (current === 1) {
      await this.manager.expire(key, window);
    }
    
    return {
      allowed: current <= limit,
      current,
      limit,
      remaining: Math.max(0, limit - current),
      resetAt: Date.now() + (await this.manager.ttl(key)) * 1000
    };
  }

  // ==================== 分布式锁 ====================
  
  async acquireLock(resource, ttl = 30) {
    return await this.manager.acquireLock(resource, ttl);
  }

  async releaseLock(resource, identifier) {
    return await this.manager.releaseLock(resource, identifier);
  }

  // ==================== 缓存预热 ====================
  
  async warmupCache() {
    try {
      console.log('开始缓存预热...');
      
      const { Category, Book } = require('../models');
      
      // 预热分类数据
      const categories = await Category.findAll({
        where: { is_active: true },
        order: [['sort_order', 'ASC']]
      });
      await this.setAllCategories(categories);
      
      // 预热热门图书
      const trendingBooks = await Book.findAll({
        where: { status: '上架' },
        order: [['sales_count', 'DESC'], ['views', 'DESC']],
        limit: 20
      });
      await this.setTrendingBooks('day', trendingBooks);
      
      console.log('缓存预热完成');
    } catch (error) {
      console.error('缓存预热失败:', error);
    }
  }

  // ==================== 缓存监控 ====================
  
  async getCacheInfo() {
    const info = {};
    
    // 获取各类缓存的数量
    const patterns = [
      { name: 'books', pattern: `${CACHE_PREFIX.BOOK}*` },
      { name: 'users', pattern: `${CACHE_PREFIX.USER}*` },
      { name: 'orders', pattern: `${CACHE_PREFIX.ORDER}*` },
      { name: 'sessions', pattern: `${CACHE_PREFIX.SESSION}*` },
      { name: 'search', pattern: `${CACHE_PREFIX.SEARCH}*` },
      { name: 'trending', pattern: `${CACHE_PREFIX.TRENDING}*` }
    ];
    
    for (const { name, pattern } of patterns) {
      const keys = await this.manager.redis.keys(pattern);
      info[name] = keys.length;
    }
    
    // 获取内存使用情况
    const client = this.manager.client;
    const memoryInfo = await new Promise((resolve) => {
      client.info('memory', (err, data) => {
        if (err) {
          resolve({});
        } else {
          const lines = data.split('\r\n');
          const memory = {};
          lines.forEach(line => {
            if (line.includes(':')) {
              const [key, value] = line.split(':');
              memory[key] = value;
            }
          });
          resolve(memory);
        }
      });
    });
    
    info.memory = {
      used: memoryInfo.used_memory_human,
      peak: memoryInfo.used_memory_peak_human,
      rss: memoryInfo.used_memory_rss_human
    };
    
    return info;
  }
}

// 导出单例
module.exports = new CacheService();