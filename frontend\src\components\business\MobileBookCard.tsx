import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useCart } from '../../stores/cartStore';
import TouchableCard from '../ui/TouchableCard';
import '../../styles/responsive.css';

interface Book {
  id: number;
  title: string;
  author: string;
  price: number;
  original_price?: number;
  cover_image?: string;
  description?: string;
  condition?: string;
  isbn?: string;
  stock?: number;
  category?: {
    name: string;
  };
  user?: {
    username: string;
  };
}

interface MobileBookCardProps {
  book: Book;
  onFavoriteToggle?: (bookId: number) => void;
  isFavorited?: boolean;
  showActions?: boolean;
}

const MobileBookCard: React.FC<MobileBookCardProps> = ({
  book,
  onFavoriteToggle,
  isFavorited = false,
  showActions = true
}) => {
  const navigate = useNavigate();
  const { addItem } = useCart();
  const [isAdding, setIsAdding] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);

  const handleCardClick = () => {
    navigate(`/books/${book.id}`);
  };

  const handleAddToCart = async (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsAdding(true);
    
    try {
      await addItem({
        id: book.id,
        name: book.title,
        price: book.price,
        quantity: 1,
        image: book.cover_image
      });
      
      setShowSuccess(true);
      setTimeout(() => setShowSuccess(false), 2000);
    } catch (error) {
      console.error('添加到购物车失败:', error);
    } finally {
      setIsAdding(false);
    }
  };

  const handleFavoriteClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    onFavoriteToggle?.(book.id);
  };

  const discount = book.original_price 
    ? Math.round((1 - book.price / book.original_price) * 100)
    : 0;

  const conditionBadge = book.condition ? {
    text: book.condition === 'new' ? '全新' : 
          book.condition === 'like_new' ? '9成新' :
          book.condition === 'good' ? '良好' : '一般',
    color: book.condition === 'new' ? '#28a745' :
           book.condition === 'like_new' ? '#17a2b8' :
           book.condition === 'good' ? '#ffc107' : '#6c757d'
  } : undefined;

  const actions = showActions ? (
    <>
      {/* 收藏按钮 */}
      <button
        onClick={handleFavoriteClick}
        className="btn-touch"
        style={{
          backgroundColor: isFavorited ? '#fff0f6' : '#f8f9fa',
          border: `1px solid ${isFavorited ? '#ff4d6d' : '#dee2e6'}`,
          color: isFavorited ? '#ff4d6d' : '#6c757d',
          padding: '8px 16px',
          borderRadius: '8px',
          fontSize: '14px',
          display: 'flex',
          alignItems: 'center',
          gap: '6px',
          transition: 'all 0.2s'
        }}
      >
        <span>{isFavorited ? '❤️' : '🤍'}</span>
        <span>{isFavorited ? '已收藏' : '收藏'}</span>
      </button>

      {/* 加入购物车按钮 */}
      <button
        onClick={handleAddToCart}
        disabled={isAdding || book.stock === 0}
        className="btn-touch"
        style={{
          flex: 1,
          backgroundColor: book.stock === 0 ? '#6c757d' : 
                         showSuccess ? '#28a745' : '#007bff',
          color: '#fff',
          border: 'none',
          padding: '10px 20px',
          borderRadius: '8px',
          fontSize: '16px',
          fontWeight: '500',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          gap: '8px',
          transition: 'all 0.3s',
          opacity: isAdding ? 0.7 : 1
        }}
      >
        {book.stock === 0 ? (
          <>
            <span>已售罄</span>
          </>
        ) : showSuccess ? (
          <>
            <span>✓</span>
            <span>已添加</span>
          </>
        ) : isAdding ? (
          <>
            <span className="skeleton" style={{
              width: '16px',
              height: '16px',
              borderRadius: '50%',
              animation: 'loading 1s infinite'
            }} />
            <span>添加中...</span>
          </>
        ) : (
          <>
            <span>🛒</span>
            <span>加入购物车</span>
          </>
        )}
      </button>
    </>
  ) : null;

  return (
    <TouchableCard
      title={book.title}
      subtitle={book.author}
      image={book.cover_image}
      description={book.description}
      onClick={handleCardClick}
      actions={actions}
      badge={discount > 0 ? { text: `-${discount}%`, color: '#dc3545' } : badgeBadge}
      className="book-card-mobile"
    >
      {/* 价格信息 */}
      <div style={{
        display: 'flex',
        alignItems: 'baseline',
        gap: '8px',
        marginBottom: '12px'
      }}>
        <span style={{
          fontSize: '20px',
          fontWeight: 'bold',
          color: '#dc3545'
        }}>
          ¥{book.price.toFixed(2)}
        </span>
        {book.original_price && book.original_price > book.price && (
          <span style={{
            fontSize: '14px',
            color: '#6c757d',
            textDecoration: 'line-through'
          }}>
            ¥{book.original_price.toFixed(2)}
          </span>
        )}
      </div>

      {/* 额外信息 */}
      <div style={{
        display: 'flex',
        flexWrap: 'wrap',
        gap: '8px',
        fontSize: '12px',
        color: '#6c757d'
      }}>
        {book.category && (
          <span style={{
            backgroundColor: '#f8f9fa',
            padding: '2px 8px',
            borderRadius: '4px'
          }}>
            {book.category.name}
          </span>
        )}
        {book.user && (
          <span style={{
            backgroundColor: '#f8f9fa',
            padding: '2px 8px',
            borderRadius: '4px'
          }}>
            卖家: {book.user.username}
          </span>
        )}
        {book.isbn && (
          <span style={{
            backgroundColor: '#f8f9fa',
            padding: '2px 8px',
            borderRadius: '4px'
          }}>
            ISBN: {book.isbn}
          </span>
        )}
      </div>
    </TouchableCard>
  );
};

export default MobileBookCard;