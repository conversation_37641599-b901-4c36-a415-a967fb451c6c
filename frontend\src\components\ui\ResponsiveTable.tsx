import React from 'react';
import '../../styles/responsive.css';

interface Column<T> {
  key: keyof T | string;
  label: string;
  render?: (value: any, item: T) => React.ReactNode;
  mobileLabel?: string;
  hideOnMobile?: boolean;
  priority?: number;
}

interface ResponsiveTableProps<T> {
  data: T[];
  columns: Column<T>[];
  onRowClick?: (item: T) => void;
  loading?: boolean;
  emptyMessage?: string;
  className?: string;
}

function ResponsiveTable<T extends Record<string, any>>({
  data,
  columns,
  onRowClick,
  loading = false,
  emptyMessage = '暂无数据',
  className = ''
}: ResponsiveTableProps<T>) {
  // 根据优先级排序列，移动端只显示重要列
  const sortedColumns = [...columns].sort((a, b) => 
    (b.priority || 0) - (a.priority || 0)
  );

  const visibleColumns = sortedColumns.filter(col => !col.hideOnMobile);

  if (loading) {
    return (
      <div className="table-loading" style={{
        padding: '40px',
        textAlign: 'center'
      }}>
        <div className="skeleton" style={{
          height: '20px',
          marginBottom: '10px',
          borderRadius: '4px'
        }} />
        <div className="skeleton" style={{
          height: '20px',
          marginBottom: '10px',
          borderRadius: '4px'
        }} />
        <div className="skeleton" style={{
          height: '20px',
          borderRadius: '4px'
        }} />
      </div>
    );
  }

  if (data.length === 0) {
    return (
      <div className="empty-state">
        <div className="empty-state-icon">📋</div>
        <div className="empty-state-text">{emptyMessage}</div>
      </div>
    );
  }

  return (
    <>
      {/* 桌面端表格 */}
      <div className={`table-responsive d-none d-md-block ${className}`}>
        <table className="table" style={{
          width: '100%',
          borderCollapse: 'collapse'
        }}>
          <thead>
            <tr style={{
              backgroundColor: '#f8f9fa',
              borderBottom: '2px solid #dee2e6'
            }}>
              {columns.map((column) => (
                <th 
                  key={column.key as string}
                  style={{
                    padding: '12px',
                    textAlign: 'left',
                    fontWeight: '600',
                    color: '#495057'
                  }}
                >
                  {column.label}
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {data.map((item, index) => (
              <tr 
                key={index}
                onClick={() => onRowClick?.(item)}
                style={{
                  borderBottom: '1px solid #dee2e6',
                  cursor: onRowClick ? 'pointer' : 'default',
                  transition: 'background-color 0.2s'
                }}
                onMouseEnter={(e) => {
                  if (onRowClick) {
                    e.currentTarget.style.backgroundColor = '#f8f9fa';
                  }
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = 'transparent';
                }}
              >
                {columns.map((column) => (
                  <td 
                    key={column.key as string}
                    style={{
                      padding: '12px',
                      color: '#212529'
                    }}
                  >
                    {column.render 
                      ? column.render(item[column.key as keyof T], item)
                      : item[column.key as keyof T]
                    }
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* 移动端卡片列表 */}
      <div className={`d-block d-md-none ${className}`}>
        {data.map((item, index) => (
          <div 
            key={index}
            className="card-mobile touchable"
            onClick={() => onRowClick?.(item)}
            style={{
              backgroundColor: '#fff',
              border: '1px solid #dee2e6',
              borderRadius: '8px',
              padding: '16px',
              marginBottom: '12px',
              cursor: onRowClick ? 'pointer' : 'default',
              boxShadow: '0 1px 3px rgba(0,0,0,0.1)'
            }}
          >
            {visibleColumns.map((column) => {
              const value = column.render 
                ? column.render(item[column.key as keyof T], item)
                : item[column.key as keyof T];

              // 跳过空值
              if (!value && value !== 0) return null;

              return (
                <div 
                  key={column.key as string}
                  style={{
                    marginBottom: '8px',
                    display: 'flex',
                    flexWrap: 'wrap',
                    gap: '8px'
                  }}
                >
                  <span style={{
                    fontWeight: '600',
                    color: '#6c757d',
                    fontSize: '14px',
                    minWidth: '80px'
                  }}>
                    {column.mobileLabel || column.label}:
                  </span>
                  <span style={{
                    color: '#212529',
                    fontSize: '14px',
                    flex: 1
                  }}>
                    {value}
                  </span>
                </div>
              );
            })}
          </div>
        ))}
      </div>
    </>
  );
}

export default ResponsiveTable;